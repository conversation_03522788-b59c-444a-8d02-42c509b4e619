// https://github.com/wechat-miniprogram/mobx-miniprogram-bindings
import { observable, action } from "mobx-miniprogram";

// 创建 store 时可以采用任何 mobx 的接口风格
// 这里以传统的 observable 风格为例

import { authStore } from "./authStore";
import { productStore } from "./productStore";
import { orderStore } from "./orderStore";
import { locationStore } from "./locationStore";
import { userStore } from "./userStore";
import { cardStore } from "./cardStore";
import { noteStore } from "./noteStore";

// 默认导出
export default {
	authStore,
	userStore,
	cardStore,
	productStore,
	orderStore,
	locationStore,
	noteStore,
};

// 命名导出
export {
	authStore,
	userStore,
	cardStore,
	productStore,
	orderStore,
	locationStore,
	noteStore,
};

// // Named imports (as in your example)
// import {
//   productStore,
//   cardStore,
//   authStore,
//   orderStore,
//   userStore
// } from "../../stores/index";

// // Or using default import (existing way)
// import stores from "../../stores";
// // Then access as: stores.productStore, stores.authStore, etc.
