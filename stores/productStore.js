// https://github.com/wechat-miniprogram/mobx-miniprogram-bindings
import { observable, action, runInAction } from "mobx-miniprogram";
import { init } from "@cloudbase/wx-cloud-client-sdk";
import { initHTTPOverCallFunction } from "@cloudbase/wx-cloud-client-sdk";

// 指定云开发环境 ID
wx.cloud.init({
	env: "cloud1-0gpy573m8caa7db3", // 指定云开发环境 ID
	traceUser: true, // 是否记录用户访问
});
const client = initHTTPOverCallFunction(wx.cloud);
const models = client.models;

export const productStore = observable({
	shopsLoadStatus: 0, // 0: 初始， 1: loading, 2: no more，3:failed 具体看load-more组件
	// shops: [],
	// shops3: [],


	shops_home: [],
	shops_ykj: [],


	page_size: 10,
	page_number: 1,
	total: 0,

	categories: [],
	selectedCategory: "0", // 首页选中的分类

	shop: {},
	product: {},
	promotions: {},

	promotion: {},

	searchHistory: [],

	saveSearchHistory: function (query) {
		const history = wx.getStorageSync("dgx-search-history") || [];
		if (history.includes(query)) {
			return;
		}
		history.unshift(query);
		wx.setStorageSync("dgx-search-history", history);
	},
	loadSearchHistory: function () {
		const history = wx.getStorageSync("dgx-search-history") || [];
		this.searchHistory = history;
	},
	clearSearchHistory: function () {
		wx.setStorageSync("dgx-search-history", []);
		this.searchHistory = [];
	},
	search: async function (query) {
		if (!query) {
			return;
		}
		this.saveSearchHistory(query);
		console.debug("query: ", query);

		const location = wx.getStorageSync("dgx-location");
		const res = await wx.cloud.callFunction({
			name: "search",
			data: {
				q: `%${query}%`,
				user_longitude: location?.longitude,
				user_latitude: location?.latitude,
			},
		});
		console.debug("search res", res);
		runInAction(() => {
			if (res.errMsg === "cloud.callFunction:ok") {
				const records = res.result.data;
				this.shops = records;
			}
		});

		// 处理店铺logo
		const shopsToProcess = this.shops;
		for (let index = 0; index < shopsToProcess.length; index++) {
			const x = shopsToProcess[index];
			if (x.main_image) {
				const arr = JSON.parse(x.main_image);
				x.logo = arr[0];
			}
		}

		runInAction(() => {
			console.debug("RUN IN ACTION");
			console.debug(shopsToProcess);
			// this.shops = shopsToProcess
			// 创建新的数组，确保每个对象都是新的引用
			this.shops = shopsToProcess.map((shop) => ({
				...shop, // 展开运算符创建对象的新副本
			}));
		});

		for (let index = 0; index < shopsToProcess.length; index++) {
			const x = shopsToProcess[index];
			productStore.getPromotionsForShop(x._id);
		}
	},

	getAllCategories: async function () {
		const cates = wx.getStorageSync("dgx-categories");
		if (cates) {
			runInAction(() => {
				this.categories = cates;
			});
		}
		const res = await models.shop_categories.list({
			// 只查询必要的字段
			select: {
				_id: true,
				name: true,
				sort: true,
			},
			filter: {
				// 可以根据条件过滤
				where: {
					enabled: {
						$eq: true,
					},
				},
			},
			orderBy: [
				{
					sort: "desc",
				},
			],
			// getCount: true, // 开启用来获取总数
		});

		runInAction(async () => {
			const records = await res.data.records;
			// console.debug("categories", records)
			records.splice(0, 0, { name: "全部", _id: 0, sort: 10 });
			this.categories = records;
			wx.setStorageSync("categories", this.categories);
		});
	},

	// onTabChange切换分类的时候要重置
	setPageNumber: function (n) {
		this.page_number = n;
	},
	// 切换分类的时候要重置total
	setTotal: function (n) {
		this.total = 0;
	},
	// 切换分类的时候要重置selectedCategory
	setSelectedCategory: function (category_id) {
		this.selectedCategory = category_id;
	},
	// 切换分类的时候要重置shops
	resetShops: function () {
		this.shops_home = [];
		this.shops_ykj = [];
		this.shopsLoadStatus = 0; // 重置加载状态
	},
	getNearestShops: async function (category_id, business_type_tags) {
		// return new Promise((resolve, reject) => {});
		this.shopsLoadStatus = 1;
		console.debug("category_id:", category_id);
		const location = wx.getStorageSync("dgx-location");
		console.debug("location:", location);
		if (!location.longitude || !location.latitude) {
			console.debug("location 为空，没有选择位置, 无法获取最近店铺列表");
			this.shopsLoadStatus = 3; // 设置为失败状态
			return;
		}

		try {
		// const location = locationStore.location || wx.getStorageSync("dgx-location");
		let data = {
			// '{"longitude": 114.261007,"latitude": 30.72176,"page_size": 10,"page_number": 1}'
			longitude: location.longitude,
			latitude: location.latitude,
			page_size: this.page_size,
			page_number: this.page_number,
			category_id: "0",
		};
		if (category_id != "0" && category_id) {
			data.category_id = category_id;
		}

		let targetShopsKey = "shops";
		let	cloudfunctionName = "findNearestShops";
		if(business_type_tags === "tandian1") {
			cloudfunctionName = "findNearestShops"
			targetShopsKey = "shops_home";
		}
		if(business_type_tags === "tandian2") {
			cloudfunctionName = "findNearestShops2"
			targetShopsKey = "shops_home";
		}
		if(business_type_tags === "ykj") {
			cloudfunctionName = "findNearestShops3"
			targetShopsKey = "shops_ykj";
		}

		// 根据业务类型检查对应的店铺数量
		const currentShops = this[targetShopsKey] || [];
		if (this.total != 0 && currentShops.length >= this.total) {
			this.shopsLoadStatus = 2;
			return;
		}

		const response = await wx.cloud.callFunction({
			name: cloudfunctionName,
			data: data,
		});

		runInAction(() => {
			console.debug(`${cloudfunctionName} response:`, response);
			if (response.errMsg === "cloud.callFunction:ok") {
				if (response.result.code === 200) {
					console.debug("total:", response.result.total);
					// 初始化时就设置 logo 字段
					const shops = response.result.data.map((shop) => ({
						...shop,
						logo: "", // 初始化 logo 字段
						products: [], // 初始化 products
						promotions: [], // 初始化 promotions
					}));

					if (this.selectedCategory === category_id) {
						// 同一个分类，追加 - 使用push性能更好
						this[targetShopsKey].push(...shops);
					} else {
						// 切换分类了，从头来
						this[targetShopsKey] = shops;
					}

					this.total = response.result.total;

					// 处理店铺logo
					const shopsToProcess = this[targetShopsKey];
					for (let index = 0; index < shopsToProcess.length; index++) {
						const x = shopsToProcess[index];
						if (x.main_image) {
							const arr = JSON.parse(x.main_image);
							x.logo = arr[0];
						}
					}

					// 快速更新加载状态
					setTimeout(() => {
						if (this.total === 0) {
							// 如果总数为0，说明没有数据
							this.shopsLoadStatus = 2; // 没有更多数据
						} else if (this[targetShopsKey].length >= this.total) {
							// 如果已加载数据 >= 总数，说明全部加载完成
							this.shopsLoadStatus = 2; // 没有更多数据
						} else {
							// 还有更多数据可以加载
							this.shopsLoadStatus = 0; // 可以继续加载
							// 为下次请求递增页码
							this.page_number++;
						}
					}, 50); // 减少到50ms，加快响应速度
				} else {
					// 请求成功但业务逻辑失败
					console.error("业务逻辑错误:", response.result);
					this.shopsLoadStatus = 3; // 加载失败
				}
			} else {
				// 云函数调用失败
				console.error("云函数调用失败:", response.errMsg);
				this.shopsLoadStatus = 3; // 加载失败
			}
		});

		// 异步加载促销信息，不阻塞主要的数据显示
		setTimeout(() => {
			const shopsToProcess = this[targetShopsKey];
			for (let index = 0; index < shopsToProcess.length; index++) {
				const x = shopsToProcess[index];
				productStore.getPromotionsForShop(x._id, business_type_tags, false);
			}
		}, 100); // 延迟100ms加载促销信息，让主要数据先显示

		// runInAction( () => {
		//   this.shops = this.shops.filter(shop => shop.promotions.length > 0)
		// })
		} catch (error) {
			console.error("getNearestShops 异常:", error);
			runInAction(() => {
				this.shopsLoadStatus = 3; // 设置为失败状态
			});
		}
	},

	shopDetail: async function (store_id) {
		const response = await models.shops.get({
			select: {
				name: true,
				is_open: true,
				latitude: true,
				longitude: true,
				province: true,
				city: true,
				district: true,
				address: true,
				phone: true,
				business_hours: true,
				tag: true,
				star_rating: true,
				description: true,
				// products: true,
				// promotions: {
				//   _id: true,
				//   original_price: true,
				//   discount_price: true,
				//   current_price: true,
				//   name: true,
				//   platform: true,
				//   level: true,
				//   followers_count: true,
				//   requires: true,
				//   shop_id: true,
				// },
				main_image: true,
			},
			filter: {
				// 可以根据条件过滤
				where: {
					_id: {
						$eq: store_id,
					},
				},
			},
		});

		console.debug("models.shops.get response:", response);

		// runInAction(() => {
		// })

		// // 使用Object.assign更新状态
		// // Object.assign(this, { shop: response.data });
		// this.shop = response.data;

		// 在异步操作中使用runInAction更新状态
		// https://github.com/wechat-miniprogram/mobx-miniprogram-bindings/issues/16
		// https://github.com/MatrixCross/Weapp-Starter/blob/307c76cd9ac935309795375b222d819c63b5f3f4/src/store/global.ts#L23C14-L23C15
		runInAction(() => {
			this.shop = response.data;

			// 处理店铺logo图
			if (this.shop.main_image) {
				this.shop.logo = this.shop.main_image[0];
			}

			// 处理距离
			const location = wx.getStorageSync("dgx-location");
			if (location) {
				console.debug(location);
				console.debug(this.shop.longitude, this.shop.latitude);
				const distance = this.haversineDistance(
					location.latitude,
					location.longitude,
					this.shop.latitude,
					this.shop.longitude,
				);
				console.debug(distance);
				if (distance > 1) {
					this.shop.distance = `${distance.toFixed(2)}km`;
				} else {
					this.shop.distance = `${(distance * 100).toFixed(0)}m`;
				}
			}

			productStore.getPromotionsForShop(this.shop._id, "tandian1", true);
		});
	},

	productDetail: action(async function (product_id) {
		const response = await models.products.get({
			select: {
				_id: true,
				name: true,
				images: true,
				original_price: true,
				quantity: true,
				shop_id: true,
				available: true,
				createdAt: true,
				updatedAt: true,
				detail: true,
			},
			filter: {
				where: {
					_id: {
						$eq: product_id,
					},
				},
			},
		});

		runInAction(() => {
			this.product = response.data;
		});
	}),
 
	promotionDetail: async function (promotion_id) {
		const response = await models.promotions.get({
			select: {
				_id: true,
				name: true,
				original_price: true,
				sale_price: true,
				discount_price: true,
				current_price: true,
				shop_id: true,
				product_id: {
					_id: true,
					name: true,
					images: true,
					original_price: true,
					quantity: true,
					shop_id: true,
					available: true,
					detail: true,
				},
				shop_id: true,
				platform: true,
				promotion_type: true,
				followers_count: true,
				level: true,
				requires: true,
				available: true,
				quantity: true,
				realtime_quantity: true,
				topics: true,
				text_count: true,
				images_count: true,
				commission: true,
				note_text: true,
				verify_start_date: true,
				verify_end_date: true,
				updatedAt: true,
				createdAt: true,
			},
			filter: {
				where: {
					_id: {
						$eq: promotion_id,
					},
				},
			},
		});

		return new Promise((resolve, reject) => {
			runInAction(() => {
				this.promotion = response.data;
				resolve(response.data);
			});
		});
	},

	getPromotionsForShop: async function (store_id, business_type_tags = "tandian1", in_shop_detail_page = false) {
		const { data } = await models.promotions.list({
			// 只查询必要的字段
			select: {
				_id: true,
				name: true,
				original_price: true,
				discount_price: true,
				current_price: true,
				sale_price: true,
				// quantity: true,
				shop_id: true,
				product_id: {
					_id: true,
					name: true,
					images: true,
					original_price: true,
					quantity: true,
					shop_id: true,
					available: true,
				},
				platform: true,
				followers_count: true,
				level: true,
				requires: true,
				available: true,
				quantity: true,
				realtime_quantity: true,
				updatedAt: true,
				createdAt: true,
			},
			filter: {
				where: {
					shop_id: {
						$eq: store_id,
					},
					available: {
						$eq: true,
					},
					promotion_type: {
						$eq: business_type_tags || "tandian1"
					}
				},
			},
			// getCount: true, // 开启用来获取总数
		});

		runInAction(() => {
			console.debug("fetched promotions for shop:", store_id, "business_type_tags:", business_type_tags, "in_shop_detail_page:", in_shop_detail_page);
			const promotions = data.records;
			console.debug("promotions count:", promotions.length, "promotions:", promotions);

			// 更新当前商店的促销信息
			if (!in_shop_detail_page) {
				// this.shops = this.shops.map((shop) =>
				// 	shop._id === store_id ? { ...shop, promotions: promotions } : shop,
				// );
				let targetShopsKey = "shops";
				if(business_type_tags === "tandian1") {
					targetShopsKey = "shops_home";
				}
				if(business_type_tags === "tandian2") {
					targetShopsKey = "shops_home";
				}
				if(business_type_tags === "ykj") {
					targetShopsKey = "shops_ykj";
				}
				this[targetShopsKey] = this[targetShopsKey].map((shop) =>
					shop._id === store_id ? { ...shop, promotions: promotions } : shop,
				);
			}

			if (in_shop_detail_page) {
				// 无论是否有促销活动，都要设置 promotions 字段
				this.shop = { ...this.shop, promotions: promotions };
			}

			// // 只保留有促销活动的商店
			// this.shops = this.shops.filter(shop => shop.promotions && shop.promotions.length > 0);
			// console.debug("过滤后的商店列表", this.shops);
		});
	},

	haversineDistance: function (lat1, lon1, lat2, lon2) {
		// 将经纬度从度数转换为弧度
		const toRadians = (degrees) => degrees * (Math.PI / 180);

		const R = 6371; // 地球半径，单位：公里
		const dLat = toRadians(lat2 - lat1);
		const dLon = toRadians(lon2 - lon1);

		const a =
			Math.sin(dLat / 2) * Math.sin(dLat / 2) +
			Math.cos(toRadians(lat1)) *
				Math.cos(toRadians(lat2)) *
				Math.sin(dLon / 2) *
				Math.sin(dLon / 2);

		const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

		const distance = R * c; // 最终距离，单位：公里
		return distance;
	},
});
