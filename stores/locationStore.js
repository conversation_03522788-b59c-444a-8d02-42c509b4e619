// https://github.com/wechat-miniprogram/mobx-miniprogram-bindings
import { observable, action, runInAction } from "mobx-miniprogram";
import { initHTTPOverCallFunction } from "@cloudbase/wx-cloud-client-sdk";

wx.cloud.init({
	env: "cloud1-0gpy573m8caa7db3",
});

const client = initHTTPOverCallFunction(wx.cloud);
const models = client.models;

export const locationStore = observable({
	location: null,
	hotAreas: null,
	nearbyAreas: null,

	// actions
	locate: action(function (ignoreLocalStorage) {
		if (ignoreLocalStorage) {
			return new Promise((resolve, reject) => {
				wx.getLocation({
					type: "gcj02",
					async success(res) {
						wx.setStorageSync("dgx-location", {
							longitude: res.longitude,
							latitude: res.latitude,
						});
						console.debug("locationStore.locate, res", res);
						await locationStore.geocoder({
							location: `${res.latitude},${res.longitude}`,
						});
						resolve();
					},
					fail(err) {
						console.error("获取地理位置失败:", err);
						reject(err);
					},
				});
			});
		}
		const loc = wx.getStorageSync("dgx-location");
		if (loc) {
			console.debug("this.location", loc);
			runInAction(async () => {
				this.location = loc;
				return;
			});
		} else {
			return new Promise((resolve, reject) => {
				// const that = this;
				wx.getLocation({
					type: "gcj02",
					async success(res) {
						wx.setStorageSync("dgx-location", {
							longitude: res.longitude,
							latitude: res.latitude,
						});
						console.debug("locationStore.locate, res", res);
						await locationStore.geocoder({
							location: `${res.latitude},${res.longitude}`,
						});
						resolve();
					},
					fail(err) {
						console.error("获取模糊地理位置失败:", err);
						reject(err);
					},
				});
			});
		}
	}),

	chooseLocation: action(function () {
		const location = wx.getStorageSync("dgx-location");
		if (location) {
			return;
		}

		// 检查用户是否已授权地址权限
		wx.getSetting({
			success(res) {
				if (!res.authSetting["scope.userLocation"]) {
					// 未授权，请求用户授权
					wx.authorize({
						scope: "scope.userLocation",
						success() {
							console.log("用户已授权地址权限");
							// 授权成功后调用 wx.chooseAddress
							wx.chooseLocation({
								success(res) {
									wx.setStorageSync("dgx-location", {
										longitude: res.longitude,
										latitude: res.latitude,
										name: res.name,
									});
								},
								fail(err) {
									console.error("选择地址失败:", err);
								},
							});
						},
						fail(err) {
							console.error("用户拒绝授权:", err);
							// 提示用户手动打开授权设置
							wx.showModal({
								title: "提示",
								content: "请手动打开地址权限",
								success(res) {
									if (res.confirm) {
										wx.openSetting(); // 打开设置页面
									}
								},
							});
						},
					});
				} else {
					// 已授权，直接调用 wx.chooseLocation
					wx.chooseLocation({
						success(res) {
							console.log("选择地址:", res);
							// 保存地址到本地
							wx.setStorageSync("dgx-location", {
								longitude: res.longitude,
								latitude: res.latitude,
								name: res.name,
							});
						},
						fail(err) {
							console.error("选择地址失败:", err);
						},
					});
				}
			},
			fail(err) {
				console.error("获取设置失败:", err);
			},
		});
	}),

	// 热门商圈
	getHotAreas: async function () {
		const that = this;
		wx.getLocation({
			type: "gcj02",
			async success(res) {
				console.log("模糊地理位置:", res);
				// wx.setStorageSync("dgx-location", {
				// 	longitude: res.longitude,
				// 	latitude: res.latitude,
				// });

				// const result = await models.areas.runSQLTemplate({
				// 	templateName: 'nearby_areas',
				// 	envType: "prod",
				// 	params: {
				// 		"longitude": res.longitude || 114.290771,
				// 		"latitude": res.latitude || 30.579331
				// 	}
				// })
				// console.log("runSQLTemplate nearby_areas result:", result.data)

				const result2 = await models.areas.runSQLTemplate({
					templateName: "hot_areas",
					envType: "prod",
					params: {
						longitude: res.longitude || 114.290771,
						latitude: res.latitude || 30.579331,
					},
				});
				console.log("runSQLTemplate hot_areas result:", result2.data);
				runInAction(async () => {
					that.hotAreas = result2.data.records;
				});
			},
			fail(err) {
				console.error("获取模糊地理位置失败:", err);
			},
		});
	},

	// 附近商圈
	getNearbyAreas: async function () {
		const that = this;
		wx.getLocation({
			type: "gcj02",
			async success(res) {
				console.log("模糊地理位置:", res);
				// wx.setStorageSync("dgx-location", {
				// 	longitude: res.longitude,
				// 	latitude: res.latitude,
				// });

				const result = await models.areas.runSQLTemplate({
					templateName: "nearby_areas",
					envType: "prod",
					params: {
						longitude: res.longitude || 114.290771,
						latitude: res.latitude || 30.579331,
					},
				});
				console.log("runSQLTemplate nearby_areas result:", result.data.records);
				runInAction(async () => {
					that.nearbyAreas = result.data.records;
				});
				// const result2 = await models.areas.runSQLTemplate({
				// 	templateName: 'hot_areas',
				// 	envType: "prod",
				// 	params: {
				// 		"longitude": res.longitude || 114.290771,
				// 		"latitude": res.latitude || 30.579331
				// 	}
				// })
				// console.log("runSQLTemplate hot_areas result:", result2.data)
			},
			fail(err) {
				console.error("获取模糊地理位置失败:", err);
			},
		});
	},

	getClientIP: action(async function () {
		wx.cloud.callFunction({
			name: "ip",
			data: {},
			success: (res) => {
				console.debug("getClientIP:", res);
			},
		});
	}),

	// {"code":200,"data":{"address":"湖北省武汉市黄陂区盘龙大道46号","longitude":"30.721677","latitude":"114.260876","address_component":{"nation":"中国","province":"湖北省","city":"武汉市","district":"黄陂区","street":"盘龙大道","street_number":"盘龙大道46号"}}}
	geocoder: function (params) {
		return new Promise((resolve, reject) => {
			let latlngPair = null;
			if (!params.location) {
				const loc = wx.getStorageSync("dgx-location");
				latlngPair = `${loc.latitude},${loc.longitude}`;
			} else {
				latlngPair = params.location;
			}

			if (!latlngPair) {
				reject(new Error("No location provided"));
				return;
			}

			const that = this;
			wx.cloud.callFunction({
				name: "geocoder",
				data: {
					location: latlngPair,
				},
				success: (res) => {
					console.debug("geocoder:", res);
					if (res.errMsg === "cloud.callFunction:ok") {
						if (res.result.code === 200) {
							const loc = {
								longitude: Number(res.result.data.longitude),
								latitude: Number(res.result.data.latitude),
								address: res.result.data.address,
								address_component: res.result.data.address_component,
							};

							runInAction(async () => {
								const loc1 = wx.getStorageSync("dgx-location");
								const merged = { ...loc1, ...loc };
								that.location = merged;
								console.debug("merged");
								console.debug(merged);
								wx.setStorageSync("dgx-location", that.location);
								resolve(merged);
							});
						} else {
							reject(new Error(res.result.message || "Geocoding failed"));
						}
					} else {
						reject(new Error(res.errMsg));
					}
				},
				fail: (err) => {
					reject(err);
				},
			});
		});
	},

	setLocation: async function (location) {
		this.location = location;
	},

	loadLocationFromStorage: async function () {
		const loc = wx.getStorageSync("dgx-location");
		if (loc) {
			return new Promise((resolve) => {
				runInAction(() => {
					this.location = loc;
					resolve(loc);
				});
			});
		}
	},
});
