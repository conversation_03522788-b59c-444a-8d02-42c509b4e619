import { observable, action, runInAction } from "mobx-miniprogram";
import { init } from "@cloudbase/wx-cloud-client-sdk";
import dayjs from "dayjs";

const client = init(wx.cloud);
// const client = init(wx.cloud, {
// 	env: "cloud1-0gpy573m8caa7db3", // 动态传入环境 ID
// });
const models = client.models;

export const noteStore = observable({
	note: {},

	submitNote: async function (params) {
		console.debug(params);
		let note_id = null;
		const auth = wx.getStorageSync("dgx-auth");
		console.debug("auth", auth);
		if (!auth.user_id) {
			return;
		}

		// 如果订单状态为待审核，则不能提交笔记
		// 逻辑:
		// 首次提交笔记，需要把订单状态从待审核更改为待审核, 笔记状态为待审核
		// 重新提及笔记，需要把订单状态从驳回状态更改为待审核, 笔记状态为待审核
		// 如果笔记id存在，则更新笔记
		if (params._id) {
			const { _id, ...newParams } = params;
			const { data } = await models.notes.update({
				data: {
					...newParams,
					status: 0,
					user_id: {
						_id: auth.user_id,
					},
				},
				filter: {
					where: {
						_id: {
							$eq: _id,
						},
					},
				},
			});
			console.debug(data);
			note_id = _id;
		} else {
			const { data } = await models.notes.create({
				data: {
					...params,
					status: 0,
					user_id: {
						_id: auth.user_id,
					},
				},
			});
			console.debug(data);
			note_id = data.Id;
		}

		// 把订单状态更改为待审核
		const { data: orderUpdateRes } = await models.orders.update({
			data: {
				status: "30",
			},
			filter: {
				where: {
					_id: {
						$eq: params.order_id._id,
					},
				},
			},
		});
		console.debug(orderUpdateRes, "orderUpdateRes");

		return {
			note_id: note_id,
			order_id: params.order_id._id,
		};
	},

	fetchNote: async function (orderId) {
		const { data } = await models.notes.get({
			filter: {
				where: {
					order_id: {
						$eq: orderId,
					},
				},
			},
		});
		console.debug(data);
		return data;
	},
});
