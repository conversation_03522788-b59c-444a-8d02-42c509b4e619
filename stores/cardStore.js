// https://github.com/wechat-miniprogram/mobx-miniprogram-bindings
import { observable, action, runInAction } from "mobx-miniprogram";
// 创建 store 时可以采用任何 mobx 的接口风格
// 这里以传统的 observable 风格为例

// const app = getApp();
// const models = app.dataModel;

const { init } = require("@cloudbase/wx-cloud-client-sdk");
// // 指定云开发环境 ID
// wx.cloud.init({
// 	env: "cloud1-0gpy573m8caa7db3", // 指定云开发环境 ID
// 	traceUser: true, // 是否记录用户访问
// });
const client = init(wx.cloud, {
	env: "cloud1-0gpy573m8caa7db3", // 动态传入环境 ID
});
const models = client.models;

export const cardStore = observable({
	// 数据字段
	numA: 1,
	numB: 2,

	card: {
		name: "",
		platform: "",
		level: 1,
		followers_count: 0,
		url: "",
		fileID: "",
		updatedAt: "",
	},

	cards: [],

	dpCard: null,
	xhsCard: null,

	// 计算属性
	get sum() {
		return this.numA + this.numB;
	},

	// actions
	add: action(async (card) => {
		// console.debug(this.card);
		console.debug(card);
		try {
			return models.cards.create({
				data: {
					...card,
					// level: 0,  // 设置下默认值÷
					verified: 0, // 设置下默认值
				},
			});
		} catch (error) {
			console.error("添加名片失败：", error);
		}
	}),

	delete: async function (id) {
		const { data } = await models.cards.get({
			filter: {
				where: {
					_id: {
						$eq: id, // 推荐传入_id数据标识进行操作
					},
				},
			},
		});
		return new Promise((resolve, reject) => {
			runInAction(async function () {
				console.debug("data:", data);
				if (data.fileID) {
					console.debug("delete fileID:", data.fileID);
					const files = [];
					files.push(data.fileID);
					wx.cloud.deleteFile({
						fileList: files,
						success: (res) => {
							// handle success
							console.log(res.fileList);
						},
						fail: console.error,
					});
				}

				if (data._id) {
					console.debug("delete card:", data._id);
					const { data: deleteResp } = await models.cards.delete({
						filter: {
							where: {
								_id: {
									$eq: data._id, // 推荐传入_id数据标识进行操作
								},
							},
						},
					});
					resolve(deleteResp);

					// this.list();
					// // 删除成功后更新列表状态
					// cardStore.list();

					// // 显示删除成功提示
					// wx.showToast({
					//   title: '解绑成功',
					//   icon: 'success',
					//   duration: 2000
					// });
				}
			});
		});
	},

	update: action(async (card) => {
		try {
			const { _id, ...rest } = card;
			return models.cards.update({
				data: {
					...rest,
					verified: 0, // 设置下默认值
				},
				filter: {
					where: {
						_id: {
							$eq: card._id, // 推荐传入_id数据标识进行操作
						},
					},
				},
			});
		} catch (error) {
			console.error("添加名片失败：", error);
		}
	}),

	fetchCards: action(async function (userid) {
		const auth = wx.getStorageSync("dgx-auth");
		console.debug("auth", auth);
		if (!auth.user_id) {
			return;
		}

		const response = await models.cards.list({
			// 只查询必要的字段
			select: {
				_id: true,
				name: true,
				url: true,
				image: true,
				fileID: true,
				user_id: true,
				platform: true,
				followers_count: true,
				level: true,
				verified: true,
				updatedAt: true,
			},
			filter: {
				// 可以根据条件过滤
				where: {
					user_id: {
						$eq: auth.user_id,
					},
				},
			},
			getCount: true, // 开启用来获取总数
		});

		return new Promise((resolve, reject) => {
			runInAction(() => {
				console.debug("response", response);
				this.cards = response.data.records;
				const dpCard = response.data.records.filter(
					(x) => x.platform === "dianping",
				)[0];
				const xhsCard = response.data.records.filter(
					(x) => x.platform === "xiaohongshu",
				)[0];
				console.debug(dpCard);
				console.debug(xhsCard);
				this.dpCard = dpCard;
				this.xhsCard = xhsCard;
				resolve(response);
			});
		});
	}),
});
