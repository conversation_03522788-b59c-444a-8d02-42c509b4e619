// https://github.com/wechat-miniprogram/mobx-miniprogram-bindings
import { observable, action, runInAction, transaction } from "mobx-miniprogram";
import { init, initHTTPOverCallFunction } from "@cloudbase/wx-cloud-client-sdk";
import { authStore } from "./authStore";
import dayjs from "dayjs";

wx.cloud.init({
	env: "cloud1-0gpy573m8caa7db3",
});
// const client = init(wx.cloud)
// const client = init(wx.cloud, {
//   env: "cloud1-0gpy573m8caa7db3", // 动态传入环境 ID
// });

const client = initHTTPOverCallFunction(wx.cloud);
const models = client.models;

export const userStore = observable({
	balance: 0,

	withdrawHistory: [],

	withdrawRecordCreated: false,

	teamMembers: [],
	status: 0,

	effective_members_count: 0, // 有效团员数
	ineffective_members_count: 0, // 无效团员数

	totalWithdrawedAmount: 0, // 用户累计提现金额
	totalHistoryRewards: [], // 用户收益明细
	totalReferralRewards: 0, // 团长收益

	transactionDetail: null, // transaction明细

	points: [], // 积分

	favorites: {}, // 我的收藏
	isShopFavorited: false,

	fetchUserBalance: async function () {},

	fetchUserCoupons: async function () {
		// await authStore.refreshUserInfo()
	},

	// 获取用户收益明细（包括奖励和提现）
	fetchUserTotalHistoryRewards: async function () {
		const auth = wx.getStorageSync("dgx-auth");
		console.debug("auth", auth);
		if (!auth.user_id) {
			return;
		}

		const response = await models.transactions.list({
			// 只查询必要的字段
			select: {
				_id: true,
				user_id: true,
				amount: true,
				type: true,
				createdAt: true,
				status: true,
			},
			filter: {
				// 可以根据条件过滤
				where: {
					user_id: {
						$eq: auth.user_id,
					},
				},
			},
			orderBy: [
				{
					createdAt: "desc", // 创建时间，倒序
				},
			],
			pageSize: 100,
			getCount: true, // 开启用来获取总数
		});

		runInAction(() => {
			console.debug("response", response);
			this.totalHistoryRewards = response.data.records;
		});
	},

	fetchUserInviteData: async function () {},

	// 获取用户提现记录
	fetchUserWithdrawHistory: async function () {
		const auth = wx.getStorageSync("dgx-auth");
		console.debug("auth", auth);
		if (!auth.user_id) {
			return;
		}

		const response = await models.withdrawals.list({
			// 只查询必要的字段
			select: {
				_id: true,
				amount: true,
				method: true,
				remark: true,
				status: true,
				createdAt: true,
				completed_at: true,
			},
			filter: {
				// 可以根据条件过滤
				where: {
					user_id: {
						$eq: auth.user_id,
					},
				},
			},
			orderBy: [
				{
					createdAt: "desc", // 创建时间，倒序
				},
			],
			pageSize: 10,
			getCount: true, // 开启用来获取总数
		});

		runInAction(() => {
			console.debug("response", response);
			this.withdrawHistory = response.data.records;
		});
	},

	// 获取用户累计提现金额
	fetchTotalWithdrawedAmount: async function () {
		const auth = wx.getStorageSync("dgx-auth");
		console.debug("auth", auth);
		if (!auth.user_id) {
			return;
		}

		const response = await models.withdrawals.list({
			select: {
				amount: true,
			},
			filter: {
				where: {
					user_id: {
						$eq: auth.user_id,
					},
				},
			},
		});

		runInAction(() => {
			console.debug("withdrawals response", response);
			this.totalWithdrawedAmount =
				Math.round(
					response.data.records.reduce((acc, curr) => acc + curr.amount, 0) *
						100,
				) / 100;
		});
	},

	// 创建一条提现记录
	createWithdrawRecord: async function (params) {
		const auth = wx.getStorageSync("dgx-auth");
		console.debug("auth", auth);
		if (!auth.user_id) {
			return;
		}

		// await this.fetchUserBalance();
		// console.debug("authStore.userInfo:", authStore.userInfo)

		if (!params.amount) {
			return;
		}

		const { data: user } = await models.users.get({
			select: {
				nickname: true,
				openid: true,
				unionid: true,
				avatar: true,
				phone: true,
				user_id: true,
				balance: true,
				total_withdrawals: true,
				total_rewards: true,
				uid: true,
				_id: true,
			},
			filter: {
				where: {
					_id: {
						$eq: auth.user_id,
					},
				},
			},
		});
		console.debug("user:", user);

		// console.debug("data", data)
		// wx.setStorageSync("dgx-auth", res.result.data);

		const { data: withdrawRecord } = await models.withdrawals.create({
			data: {
				// ...params,
				user_id: {
					_id: auth.user_id,
				},
				transfer_bill_no: params.transfer_bill_no,
				out_bill_no: params.out_bill_no,
				amount: params.amount,
				method: "wechat",
				status: "completed", // withdrawal_status: pending,processing,completed,failed,cancelled
				completed_at: new Date().getTime(),
			},
		});
		console.debug("withdrawRecord:", withdrawRecord);

		// 更新交易记录
		const balanceBefore = Number(user.balance);
		const balanceAfter = Number(user.balance) - Number(params.amount);
		if (balanceAfter < 0) {
			return;
		}

		const { data: transactionRecord } = await models.transactions.create({
			data: {
				amount: params.amount,
				type: "withdraw",
				withdrawal_id: {
					_id: withdrawRecord.id,
				},
				user_id: {
					_id: auth.user_id,
				},
				status: "completed",
				balance_before: balanceBefore,
				balance_after: balanceAfter,
			},
		});

		// 更新用户余额
		const { data: updateUserResp } = await models.users.update({
			data: {
				balance: balanceAfter,
			},
			filter: {
				where: {
					_id: {
						$eq: user._id,
					},
				},
			},
		});

		console.debug("transactionRecord:", transactionRecord);
		runInAction(() => {
			if (transactionRecord.id) {
				this.withdrawRecordCreated = true;
			}
		});
	},

	fetchTeamMembers: async function (params) {
		const auth = wx.getStorageSync("dgx-auth");
		console.debug("auth", auth);
		if (!auth.user_id) {
			return;
		}
		let where = {
			referrer_id: {
				$eq: auth.user_id,
			},
		};
		if (params) {
			if (params.status === "all") {
			}

			if (params.status === "active") {
				where.effective = {
					$eq: true,
				};
			}

			if (params.status === "inactive") {
				where.effective = {
					$eq: false,
				};
			}
		}

		console.debug("where:", where);

		const response = await models.users.list({
			// 只查询必要的字段
			select: {
				_id: true,
				nickname: true,
				avatar: true,
				createdAt: true,
				updatedAt: true,
				referrer_id: true,
				effective: true,
			},
			filter: {
				// 可以根据条件过滤
				where: where,
			},
			orderBy: [
				{
					createdAt: "desc", // 创建时间，倒序
				},
			],
			pageSize: 10,
			getCount: true, // 开启用来获取总数
		});

		runInAction(() => {
			console.debug("response", response);
			this.teamMembers = response.data.records;
		});
	},

	// 获取用户团员数量
	fetchTeamMemberCounts: async function () {
		const auth = wx.getStorageSync("dgx-auth");
		console.debug("auth", auth);
		if (!auth.user_id) {
			return;
		}

		wx.cloud.callFunction({
			name: "team_members_count",
			data: {
				user_id: auth.user_id,
			},
			success: (res) => {
				console.debug("fetchTeamMemberCounts:", res);
				if (res.errMsg === "cloud.callFunction:ok") {
					// 个人中心 有效团员数
					this.effective_members_count = res.result.effective_members_count;
					this.ineffective_members_count = res.result.ineffective_members_count;
				}
			},
			fail: console.error,
		});
	},

	// 获取用户邀请奖励
	// TODO: 可能优化
	fetchReferralRewards: async function () {
		const auth = wx.getStorageSync("dgx-auth");
		console.debug("auth", auth);
		if (!auth.user_id) {
			return;
		}

		wx.cloud.callFunction({
			name: "referral_rewards",
			data: {
				user_id: auth.user_id,
			},
			success: (res) => {
				console.debug("fetchReferralRewards:", res);
				if (res.errMsg === "cloud.callFunction:ok") {
					// 个人中心 团长收益
					this.totalReferralRewards = res.result.total_referral_rewards;
				}
			},
			fail: console.error,
		});
	},

	// 获取用户手机号
	updateUserPhone: async function (params) {
		const auth = wx.getStorageSync("dgx-auth");
		console.debug("auth", auth);
		if (!auth.user_id) {
			return;
		}

		return models.users.update({
			data: {
				phone: params.phone,
			},
			filter: {
				where: {
					_id: { $eq: auth.user_id },
				},
			},
		});
	},

	// 积分兑换探店券， 10积分兑换一张探店券
	redeemCouponsWithPoints: async function (params) {
		const auth = wx.getStorageSync("dgx-auth");
		console.debug("auth", auth);
		if (!auth.user_id) {
			return;
		}
		const { data: user } = await models.users.get({
			select: {
				nickname: true,
				openid: true,
				unionid: true,
				avatar: true,
				phone: true,
				user_id: true,
				balance: true,
				total_withdrawals: true,
				total_rewards: true,
				uid: true,
				_id: true,
			},
			filter: {
				where: {
					_id: {
						$eq: auth.user_id,
					},
				},
			},
		});
		console.debug("user:", user);

		// 扣10个积分
		await models.points.create({
			data: {
				amount: 10,
				type: "out",
				user_id: {
					_id: user._id,
				},
			},
		});
		console.debug("扣10个积分");
		// 加一个探店券
		const res = await authStore.refreshUserInfo();
		console.debug("RES", res);
		const { data: updateUserResp } = await models.users.update({
			data: {
				remain_coupons: res.remain_coupons + 1,
			},
			filter: {
				where: {
					_id: {
						$eq: user._id,
					},
				},
			},
		});
		console.debug("加一张探店券");
		console.debug("updateUserResp:", updateUserResp);
		return new Promise((resolve) => {
			resolve(updateUserResp);
		});
	},

	// 获取积分列表
	fetchPoints: async function (params) {
		const auth = wx.getStorageSync("dgx-auth");
		console.debug("auth", auth);
		if (!auth.user_id) {
			return;
		}

		const response = await models.points.list({
			// 只查询必要的字段
			select: {
				_id: true,
				amount: true,
				type: true,
				createdAt: true,
			},
			filter: {
				// 可以根据条件过滤
				where: {
					user_id: {
						$eq: auth.user_id,
					},
				},
			},
			orderBy: [
				{
					createdAt: "desc", // 创建时间，倒序
				},
			],
			pageSize: 10,
			getCount: true, // 开启用来获取总数
		});

		runInAction(() => {
			console.debug("response", response);
			this.points = response.data.records;
		});
	},

	// 计算用户积分余额
	calcUserPoints: async function (params) {
		const auth = wx.getStorageSync("dgx-auth");
		console.debug("auth", auth);
		if (!auth.user_id) {
			return;
		}

		await wx.cloud.callFunction({
			name: "calc_user_points",
			data: {
				user_id: auth.user_id,
			},
			success: (res) => {
				console.debug("calc_user_points:", res);
			},
		});
	},

	transactionType: function (transaction_type) {
		switch (transaction_type) {
			case "reward":
				return "rewards";
			case "withdraw":
				return "withdrawals";
			case "referral":
				return "rewards";
			case "referral_order_commission":
				return "rewards";
			case "team_commission":
				return "rewards";
			case "self_order_commission":
				return "rewards";
			default:
				break;
		}
	},

	// 明细
	fetchTransactionDetail: async function (params) {
		const transaction_type = this.transactionType(params.transaction_type);
		const { data: transaction } = await models.transactions.get({
			select: {
				_id: true,
				createdAt: true,
			},
			filter: {
				where: {
					_id: {
						$eq: params.id,
					},
				},
			},
		});

		const detail = {};
		if (transaction_type === "rewards") {
			const result = await models.rewards.runSQLTemplate({
				templateName: "get_reward_by_transaction_id",
				envType: "prod",
				params: {
					id: params.id,
				},
			});

			console.debug("item:", result.data.records[0]);
			const item = result.data.records[0];
			detail.order_id = item.order_id;
			detail.user_id = item.user_id;
			detail.amount = item.amount;
			detail.transaction_type = params.transaction_type;
			detail.transaction_time = transaction.createdAt;

			if (item.order_id) {
				const { data: order } = await models.orders.get({
					select: {
						_id: true,
						user_id: true,
						order_no: true,
						item_id: true,
						promotion_id: true,
					},
					filter: {
						where: {
							_id: {
								$eq: item.order_id,
							},
						},
					},
				});

				detail.order_no = order.order_no;
				// detail.product_name = order.name
				detail.shop = order.shop_id;
				detail.order = order;
				detail.promotion = order.promotion_id[0];
				console.debug("order", order);
				detail.user = order.user_id;

				// const { data: shop } = await models.shops.get({
				//   select: {
				//     _id: true,
				//     name: true,
				//   },
				//   filter: {
				//     where: {
				//       _id: {
				//         $eq: order.shop_id._id
				//       },
				//     },
				//   },
				// })
				// detail.shop_name = shop.name

				// const { data: user } = await models.users.get({
				//   select: {
				//     nickname: true,
				//     avatar: true,
				//     phone: true,
				//     user_level: true,
				//     uid: true,
				//     _id: true,
				//   },
				//   filter: {
				//     where: {
				//       _id: {
				//         $eq: order.user_id._id
				//       },
				//     },
				//   },
				// })
				// detail.phone = user.phone
				// detail.nickname = user.nickname
				// detail.uid = user.uid
			}

			runInAction(async () => {
				this.transactionDetail = detail;
			});
		}

		if (transaction_type === "withdrawals") {
			const result = await models.withdrawals.runSQLTemplate({
				templateName: "get_withdrawal_by_transaction_id",
				envType: "prod",
				params: {
					id: params.id,
				},
			});

			console.debug(result.data.records[0]);
			const item = result.data.records[0];
			detail.order_id = item.order_id;
			detail.user_id = item.user_id;
			detail.amount = item.amount;
			detail.transaction_type = params.transaction_type;
			detail.transaction_time = transaction.createdAt;

			runInAction(async () => {
				this.transactionDetail = detail;
			});
		}
	},

	// 收藏/取消收藏店铺
	toggleFavorite: async function (params) {
		const auth = wx.getStorageSync("dgx-auth");
		console.debug("auth", auth);
		if (!auth.user_id) {
			return;
		}
		console.debug(params);
		return new Promise(async (resolve, reject) => {
			if (params.favorited) {
				try {
					const { data } = await models.favorites.create({
						data: {
							user_id: {
								_id: auth.user_id,
							},
							shop_id: {
								_id: params.shop_id,
							},
						},
					});
					runInAction(async () => {
						if (data && data.id) {
							this.isShopFavorited = true;
							resolve(data);
						}
					});
				} catch (error) {
					reject(error);
				}
			} else {
				try {
					const { data } = await models.favorites.delete({
						filter: {
							where: {
								user_id: {
									$eq: auth.user_id,
								},
								shop_id: {
									$eq: params.shop_id,
								},
							},
						},
					});
					runInAction(async () => {
						if (data && data.count === 1) {
							this.isShopFavorited = false;
							resolve(data);
						}
					});
				} catch (error) {
					reject(error);
				}
			}
		});
	},

	// 判断店铺是否收藏过
	hasFavorited: async function (params) {
		const auth = wx.getStorageSync("dgx-auth");
		console.debug("auth", auth);
		if (!auth.user_id) {
			return;
		}

		return new Promise(async (resolve, reject) => {
			const { data } = await models.favorites.get({
				select: {
					_id: true,
				},
				filter: {
					where: {
						user_id: {
							$eq: auth.user_id,
						},
						shop_id: {
							$eq: params.shop_id,
						},
					},
				},
			});
			console.debug("data1111", data);
			if (data && data._id) {
				runInAction(async () => {
					this.isShopFavorited = true;
					console.debug("true");
					resolve(true);
				});
			} else {
				runInAction(async () => {
					this.isShopFavorited = false;
					console.debug("false");
					resolve(false);
				});
			}
		});
	},

	// 获取收藏店铺
	fetchFavorites: async function () {
		const auth = wx.getStorageSync("dgx-auth");
		if (!auth.user_id) {
			return;
		}

		try {
			const { data } = await models.favorites.list({
				select: {
					shop_id: true,
				},
				filter: {
					where: {
						user_id: {
							$eq: auth.user_id,
						},
					},
				},
				pageSize: 100,
				getCount: true,
			});

			// 获取所有店铺数据
			const shopRecords = data.records.map((x) => x.shop_id);

			// 处理店铺数据，添加 logo
			const processedShops = shopRecords.map((shop) => {
				let logo = shop.main_image[0];
				return {
					...shop,
					logo,
				};
			});

			// 使用 runInAction 一次性更新状态
			runInAction(() => {
				this.favorites = processedShops;
			});
		} catch (error) {
			console.error("获取收藏店铺失败:", error);
		}
	},

	// 商机合作表单
  createInquiryRecord: async function (params) {
		const auth = wx.getStorageSync("dgx-auth");
		const userId = auth?.user_id || "";

		return new Promise( async (resolve, reject) => {
			try {
				const { data } = await models.inquiries.create({
					data: {
						...params,
						user_id: userId,
					},
				});
	
				if(data) {
					resolve(data)
				}

			} catch (error) {
				console.error("创建inquiry失败: ", error);
				reject(error)
			}
		})
	}
});
