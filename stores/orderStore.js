import { observable, action, runInAction } from "mobx-miniprogram";
import { init } from "@cloudbase/wx-cloud-client-sdk";
import dayjs from "dayjs";

const client = init(wx.cloud);
// const client = init(wx.cloud, {
// 	env: "cloud1-0gpy573m8caa7db3", // 动态传入环境 ID
// });
const models = client.models;

export const orderStore = observable({
	orders: [], // list page
	total: 0, // list page
	pageSize: 5, // list page
	pageNumber: 1, // list page
	status: "-1", // list page

	order: {}, // detail page
	shop: {}, // detail page
	product: {}, // detail page
	promotion: {}, // detail page

	whenVerificationSucceed: false, // detai page

	paid_orders_count: 0, // usercenter
	pending_upload_orders_count: 0, // usercenter
	under_review_orders_count: 0, // usercenter
	audit_failed_orders_count: 0, // usercenter

	// 下单
	placeOrder: async function (data) {
		const orderNo =
			dayjs().format("YYYYMMDDHHmmss") + Math.floor(Math.random() * 1000000);
		console.debug("orderNo", orderNo);
		const response = await models.orders.create({
			data: {
				...data,
				order_no: orderNo,
				status: "0",
			},
		});

		console.debug(response);

		return {
			id: response.data.Id,
			orderNo: orderNo,
		};
	},
	// 减库存
	reduceStock: async function (promotion_id, realtime_quantity) {
		// 无数据更新权限，暂时还不支持安全规则
		if (realtime_quantity - 1 >= 0) {
			await models.promotions.update({
				data: {
					realtime_quantity: realtime_quantity - 1,
				},
				filter: {
					where: {
						_id: {
							$eq: promotion_id,
						},
					},
				},
			});
		}
	},
	// 增加库存
	addStock: async function (promotion_id, realtime_quantity) {
		await models.promotions.update({
			data: {
				realtime_quantity: realtime_quantity + 1,
			},
			filter: {
				where: {
					_id: {
						$eq: promotion_id,
					},
				},
			},
		});
	},

	// 同1个用户1个店铺15天内只能下一单
	checkIfBuyedRecently: async function (shop_id) {
		const auth = wx.getStorageSync("dgx-auth");
		if (!auth) {
			return false;
		}

		// 计算15天前的时间戳
		const fifteenDaysAgo = new Date();
		fifteenDaysAgo.setDate(fifteenDaysAgo.getDate() - 15);

		const response = await models.orders.list({
			select: {
				_id: true,
				order_no: true,
				order_type: true,
				createdAt: true,
			},
			filter: {
				where: {
					user_id: {
						$eq: auth.user_id,
					},
					shop_id: {
						$eq: shop_id,
					},
					status: {
						$in: ["10", "20", "30", "31", "32", "33"],
					},
					createdAt: {
						$gte: fifteenDaysAgo.getTime(),
					},
				},
			},
		});

		if (response.data?.records?.length > 0) {
			return true;
		} else {
			return false;
		}
	},

	setPageNumber: function (n) {
		this.pageNumber = n;
	},
	setStatus: async function (status) {
		return new Promise((resolve) => {
			runInAction(() => {
				this.status = status;
				resolve();
			});
		});
	},
	fetchOrders: async function (status) {
		const user_id = wx.getStorageSync("dgx-auth").user_id;
		if (!user_id) return [];

		let where = {
			user_id: {
				$eq: user_id,
			},
		};

		if (status != "-1") {
			where.status = {
				$eq: "" + status,
			};
		}

		// if (!pageNumber) {
		//   pageNumber = 1
		// }

		console.debug("where", where);
		console.debug("pageNumber:", this.pageNumber, "pageSize:", this.pageSize);

		const response = await models.orders.list({
			select: {
				_id: true,
				total_amount: true,
				paid_amount: true,
				refund_amount: true,
				promotion_id: true,
				order_no: true,
				order_type: true,
				status: true,
				createdAt: true,
				paidAt: true,
				refundedAt: true,
				verifiedAt: true,
				updatedAt: true,
				auditedAt: true,
			},
			filter: {
				where: {
					...where,
				},
			},
			orderBy: [
				{
					createdAt: "desc", // 创建时间，倒序
				},
			],
			pageSize: 5,
			pageNumber: this.pageNumber || 1,
			getCount: true, // 开启用来获取总数
		});

		runInAction(() => {
			if (this.pageNumber > 1) {
				this.orders = [...this.orders, ...response.data.records];
			} else {
				this.orders = response.data.records;
			}
			this.total = response.data.total;
		});

		for (let x of this.orders) {
			orderStore.getPromotionDetail(x);
		}
	},

	fetchOrderDetail: async function (orderId) {
		if (!orderId) return;
		const response = await models.orders.get({
			select: {
				_id: true,
				total_amount: true,
				paid_amount: true,
				refund_amount: true,
				promotion_id: true,
				order_no: true,
				order_type: true,
				qrcode: true,
				status: true,
				paidAt: true,
				refundedAt: true,
				verifiedAt: true,
				createdAt: true,
				updatedAt: true,
			},
			filter: {
				where: {
					_id: {
						$eq: orderId,
					},
				},
			},
			orderBy: [
				{
					createdAt: "desc", // 创建时间，倒序
				},
			],
			// pageSize: 5,
			// pageNumber: pageNumber || 1,
			// getCount: true, // 开启用来获取总数
		});

		console.debug(response);

		runInAction(() => {
			this.order = response.data;
		});

		const promotion = await models.promotions.get({
			select: {
				_id: true,
				name: true,
				product_id: true,
				shop_id: true,
				platform: true,
				topics: true,
				text_count: true,
				images_count: true,
				requires: true,
				level: true,
				followers_count: true,
				createdAt: true,
				updatedAt: true,
				need_to_sync: true,
				promotion_type: true,
			},
			filter: {
				where: {
					_id: {
						$eq: this.order.promotion_id[0]._id,
					},
				},
			},
		});

		runInAction(() => {
			console.debug("promotion: ", promotion.data);
			this.shop = promotion.data.shop_id;
			this.product = promotion.data.product_id;
			this.promotion = promotion.data;
		});
	},

	// 支付的时候取消付款
	cancelPayment: async function (orderNo) {
		const response = await models.orders.update({
			data: {
				status: "81",
			},
			filter: {
				where: {
					order_no: {
						$eq: orderNo,
					},
				},
			},
		});
		console.debug(response);
	},
	// 用户主动取消订单
	cancelOrder: async function (orderNo) {
		const res = await wx.cloud.callFunction({
			name: "refund_order",
			data: {
				order_no: orderNo,
			},
		});
		console.debug(res);
		return res;
	},

	getPromotionDetail: async function (order) {
		const { data } = await models.promotions.get({
			// 只查询必要的字段
			select: {
				_id: true,
				name: true,
				original_price: true,
				discount_price: true,
				current_price: true,
				// quantity: true,
				shop_id: true,
				product_id: {
					_id: true,
					name: true,
					images: true,
					original_price: true,
					quantity: true,
					shop_id: true,
					available: true,
				},
				platform: true,
				followers_count: true,
				promotion_type: true,
				level: true,
				requires: true,
				available: true,
				updatedAt: true,
				createdAt: true,
			},
			filter: {
				where: {
					_id: {
						$eq: order.promotion_id[0]._id,
					},
				},
			},
			// getCount: true, // 开启用来获取总数
		});

		runInAction(() => {
			this.orders = this.orders.map((item) => {
				// order._id === store_id ? { ...shop, promotions: promotions } : shop
				if (item._id === order._id) {
					return {
						...item,
						promotion: data,
					};
				} else {
					return item;
				}
				// if (order.promotion) {
				//   return order
				// } else {
				//   return {
				//     ...order,
				//     promotion: data
				//   }
				// }
			});
		});
	},

	// 核销订单
	verifyOrder: async function (orderNo) {
		const { data } = await models.orders.update({
			data: {
				status: "20", // 已核销, 即待上传
				verifiedAt: new Date().getTime(),
				verify_method: 1, // 1: 用户端点立即核销, 2: 商家端扫码核销
			},
			filter: {
				where: {
					order_no: {
						$eq: orderNo,
					},
				},
			},
		});

		// 返回更新成功的条数
		console.log(data);

		wx.cloud.callFunction({
			name: "verify_order",
			data: {
				order_no: orderNo,
			},
			success: (res) => {
				if (res.errMsg === "cloud.callFunction:ok") {
					console.debug("notify success", res);
				}
			},
			fail: (err) => {
				console.error("notify fail", err);
			},
		});

		// { count: 1}
		// runInAction(() => {
		//   if (data.count === 1) {
		//     this.whenVerificationSucceed = true;
		//   }
		// })
		return data;
	},

	// 订单数量（角标)
	fetchOrderCounts: async function () {
		// $runSQL only runs on cloud function
		// const result = await models.$runSQL(
		//   "SELECT status, COUNT(*) AS count FROM orders GROUP BY status HAVING status IN('10', '20', '30')",
		//   {}
		// );
		const userInfo = wx.getStorageSync("dgx-auth");
		if (!userInfo.user_id) {
			return;
		}
		wx.cloud.callFunction({
			name: "order_counts",
			data: {
				user_id: userInfo.user_id,
			},
			success: (res) => {
				console.debug("fetchOrderCounts:", res);
				if (res.errMsg === "cloud.callFunction:ok") {
					// 个人中心 订单角标
					this.paid_orders_count = res.result.paid_orders_count;
					this.pending_upload_orders_count =
						res.result.pending_upload_orders_count;
					this.under_review_orders_count = res.result.under_review_orders_count;
					this.audit_failed_orders_count = res.result.audit_failed_orders_count;
				}
			},
			fail: console.error,
		});
	},
});
