// https://github.com/wechat-miniprogram/mobx-miniprogram-bindings
import { observable, action, runInAction } from "mobx-miniprogram";
import { init } from "@cloudbase/wx-cloud-client-sdk";

// 创建 store 时可以采用任何 mobx 的接口风格
// 这里以传统的 observable 风格为例

// const app = getApp();
// const models = app.dataModel;

// let config = {};
// if (wx.getAccountInfoSync) {
// 	const accountInfo = wx.getAccountInfoSync();
// 	const env = accountInfo.miniProgram.envVersion || "release"; // 获取当前环境
// 	if (env === "develop") {
// 		config = require("../config/config.dev.js");
// 	} else if (env === "trial") {
// 		config = require("../config/config.test.js");
// 	} else {
// 		config = require("../config/config.prod.js");
// 	}
// }

const client = init(wx.cloud, {
	env: "cloud1-0gpy573m8caa7db3", // 动态传入环境 ID
});
const models = client.models;

export const authStore = observable({
	userInfo: {
		avatar: "",
		phone: "",
		nickname: null,
	},

	referrerUser: null,

	login: action(async () => {
		console.debug("authStore.login");
		const auth = wx.getStorageSync("dgx-auth");
		// if(authInfo) {
		// 	auth = JSON.parse(authInfo)
		// 	console.debug(auth);
		// 	if(auth.OPENID) {
		// 		return
		// 	}
		// }

		if (auth.openid) {
			return;
		}

		wx.login({
			success(res) {
				if (res.code) {
					// console.log("登录成功, code:", res.code);
					let data = {
						// code: res.code
					};
					const referrer_id = wx.getStorageSync("dgx_referrer_id");
					data.referrer_id = referrer_id;
					// 将 code 发送到云函数或后端换取 openid
					wx.cloud.callFunction({
						name: "login",
						data: data,
						async success(res) {
							console.debug(res);
							if (res.result.code === 200) {
								// console.log("云函数返回:", res);
								const { openid } = res.result.data;
								const { data } = await models.users.list({
									// 只查询必要的字段
									select: {
										_id: true,
										openid: true,
										createdAt: true,
										updatedAt: true,
									},
									filter: {
										// 可以根据条件过滤
										where: {
											openid: {
												$eq: openid,
											},
										},
									},
									getCount: true, // 开启用来获取总数
								});

								console.debug(data);

								if (data.total > 0) {
									wx.setStorageSync("dgx-auth", res.result.data);
									// wx.showToast({
									// 	title: '已登录',
									// 	icon: 'success'
									// })
								} else {
									console.debug("create user");
									const { openid, unionid } = res.result.data;
									try {
										// const result = await models.users.create({
										// 	data: {
										// 		openid: openid,
										// 		unionid: unionid,
										// 	},
										// });
										// console.debug("result", result);
										wx.setStorageSync("dgx-auth", res.result.data);
										// wx.showToast({
										// 	title: '已登录',
										// 	icon: 'success'
										// })
									} catch (error) {
										console.error("创建用户失败：", error);
									}
								}

								setTimeout(() => {
									authStore.refreshUserInfo();
								}, 100);
								// 默认头像和默认昵称替换跳转
								// setTimeout(() => {
								// 	if (!authStore.userInfo?.nickname) {
								// 		wx.navigateTo({
								// 			url: '/pages/usercenter/person-info/index',
								// 			success: (res) => {
								// 			}
								// 		})
								// 		wx.showToast({
								// 			title: '请完善个人资料',
								// 			icon: 'none',
								// 			duration: 3000
								// 		})
								// 		return
								// 	}
								// }, 1000)
							}
						},
						fail(err) {
							console.error("云函数调用失败:", err);
						},
					});
				} else {
					console.error("登录失败:", res);
				}
			},
			fail(err) {
				console.error("wx.login 调用失败:", err);
			},
		});
	}),

	loadAuth: async function () {
		const userInfo = wx.getStorageSync("dgx-auth");
		if (userInfo) {
			console.log("读取用户信息成功:", userInfo);
			this.userInfo = userInfo;
		}
	},

	refreshUserInfo: async function () {
		console.debug("refreshUserInfo");
		const userInfo = wx.getStorageSync("dgx-auth");
		if (userInfo) {
			const { data } = await models.users.get({
				select: {
					nickname: true,
					openid: true,
					unionid: true,
					avatar: true,
					phone: true,
					balance: true,
					total_withdrawals: true,
					total_rewards: true,
					total_points: true,
					daily_coupons: true,
					remain_coupons: true,
					user_level: true,
					uid: true,
					banned: true,
					referrer_id: true,
					_id: true,
				},
				filter: {
					where: {
						_id: {
							$eq: userInfo.user_id || userInfo._id,
						},
					},
				},
			});
			// console.debug("data", data)
			// wx.setStorageSync("dgx-auth", res.result.data);

			return new Promise((resolve, reject) => {
				runInAction(() => {
					data.user_id = data._id;
					console.debug("refreshUserInfo:", data);
					this.userInfo = data;
					wx.setStorageSync("dgx-auth", data);
					resolve(data);
				});
			});
		}
		// else {
		// 	wx.showModal({
		// 		title: '提示',
		// 		content: '请登录',
		// 		success(res) {
		// 			if (res.confirm) {
		// 				console.log('用户点击确定')
		// 				authStore.login();
		// 			} else if (res.cancel) {
		// 				console.log('用户点击取消')
		// 			}
		// 		}
		// 	})
		// }
	},
	// 保存昵称
	saveNickname: async function (nickname) {
		const userInfo = wx.getStorageSync("dgx-auth");
		if (!userInfo) {
			return;
		}
		const res = await models.users.update({
			data: {
				nickname: nickname,
			},
			filter: {
				where: {
					_id: {
						$eq: userInfo.user_id,
					},
				},
			},
		});
		console.debug("res", res);

		if (res.data.count > 0) {
			// wx.setStorageSync("dgx-auth", res.result.data);
		}
	},
	// 保存头像
	saveAvatar: async function (avatar) {
		const userInfo = wx.getStorageSync("dgx-auth");
		if (!userInfo) {
			return;
		}
		const res = await models.users.update({
			data: {
				avatar: avatar,
			},
			filter: {
				where: {
					_id: {
						$eq: userInfo.user_id,
					},
				},
			},
		});
		console.debug("res", res);
	},

	// 获取referrer_id
	getReferrerId: function () {
		return this.referrer_id;
	},

	// 设置referrer_id
	setReferrerId: function (referrer_id) {
		// 自己分享的链接再自己点进去购买不能算直卖，只能算自卖
		const userInfo = wx.getStorageSync("dgx-auth");
		if (userInfo?.user_id === referrer_id) {
			this.referrer_id = "";
		} else {
			this.referrer_id = referrer_id;
		}
	},

	// 获取referral_id对应的用户
	getReferrerUser: async function () {
		const userInfo = wx.getStorageSync("dgx-auth");
		if (userInfo && userInfo.referrer_id) {
			console.log("读取用户信息成功:", userInfo);

			const { data } = await models.users.get({
				select: {
					nickname: true,
					openid: true,
					unionid: true,
					avatar: true,
					phone: true,
					balance: true,
					total_withdrawals: true,
					total_rewards: true,
					total_points: true,
					daily_coupons: true,
					remain_coupons: true,
					user_level: true,
					uid: true,
					banned: true,
					referrer_id: true,
					_id: true,
				},
				filter: {
					where: {
						_id: {
							$eq: userInfo.referrer_id,
						},
					},
				},
			});
			runInAction(async () => {
				console.debug("getReferrerUser:", data);
				this.referrerUser = data;
			});
		}
	},
});
