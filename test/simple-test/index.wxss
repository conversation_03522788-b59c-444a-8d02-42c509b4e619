.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: white;
  border-radius: 10rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section, .debug-section {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.test-case {
  margin-bottom: 30rpx;
}

.case-title {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
  padding: 0 10rpx;
}

.debug-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.debug-btn {
  flex: 1;
  min-width: 200rpx;
  padding: 20rpx;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.debug-info {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 24rpx;
  color: #666;
}

.debug-info text {
  display: block;
  margin-bottom: 10rpx;
  padding: 5rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.debug-info text:last-child {
  border-bottom: none;
  margin-bottom: 0;
}
