<view class="container">
  <view class="header">
    <text class="title">简单测试</text>
  </view>

  <!-- 基础测试 -->
  <view class="test-section">
    <text class="section-title">基础显示测试</text>
    
    <!-- 最简单的测试数据 -->
    <view class="test-case">
      <text class="case-title">简单店铺数据</text>
      <shop-card 
        shop="{{simpleShop}}"
        bind:tapstore="handleTapStore"
        bind:tapproduct="handleTapProduct">
      </shop-card>
    </view>
  </view>

  <!-- 调试工具 -->
  <view class="debug-section">
    <text class="section-title">调试工具</text>
    <view class="debug-buttons">
      <button class="debug-btn" bindtap="debugShopCard">检查数据</button>
      <button class="debug-btn" bindtap="fixShopData">修复数据</button>
      <button class="debug-btn" bindtap="generateTestShop">生成测试数据</button>
    </view>
  </view>

  <!-- 调试信息 -->
  <view class="debug-section">
    <text class="section-title">调试信息</text>
    <view class="debug-info">
      <text>店铺ID: {{simpleShop._id}}</text>
      <text>店铺名称: {{simpleShop.name}}</text>
      <text>促销数量: {{simpleShop.promotions.length}}</text>
      <text>第一个促销: {{simpleShop.promotions[0].name}}</text>
    </view>
  </view>
</view>
