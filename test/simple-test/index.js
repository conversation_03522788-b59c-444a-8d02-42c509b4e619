const componentDebugger = require('../../utils/component-debugger');

Page({
  data: {
    // 最简单的测试数据
    simpleShop: {
      _id: "test001",
      name: "测试店铺",
      tag: "测试",
      district: "测试区",
      distance: "0.5",
      logo: "https://via.placeholder.com/60x60",
      promotions: [
        {
          _id: "promo001",
          name: "测试商品",
          current_price: "10.0",
          original_price: "20.0",
          realtime_quantity: 5,
          platform: "dianping",
          requires: "1",
          level: 1,
          followers_count: 100,
          tag: "热销",
          product_id: {
            _id: "prod001",
            images: ["https://via.placeholder.com/180x180"]
          }
        }
      ]
    }
  },

  onLoad() {
    console.log("简单测试页面加载");
    console.log("测试数据:", this.data.simpleShop);

    // 添加调试方法
    componentDebugger.addDebugMethods(this);

    // 验证数据
    componentDebugger.validateShopData(this.data.simpleShop);
    componentDebugger.showDebugReport(this.data.simpleShop);
  },

  handleTapStore(e) {
    console.log("点击店铺:", e.detail);
    wx.showToast({
      title: '点击了店铺',
      icon: 'success'
    });
  },

  handleTapProduct(e) {
    console.log("点击商品:", e.detail);
    wx.showToast({
      title: '点击了商品',
      icon: 'success'
    });
  }
});
