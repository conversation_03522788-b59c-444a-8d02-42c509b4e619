# Test Subpackage

这是一个专门用于测试和调试的子包，包含了各种测试页面和开发工具。

## 目录结构

```
test/
├── README.md                    # 说明文档
├── index.js                     # 子包入口文件
├── debug-menu/                  # 调试菜单
│   ├── index.wxml
│   ├── index.js
│   ├── index.wxss
│   └── index.json
└── test-shop-card/             # Shop Card 组件测试
    ├── index.wxml
    ├── index.js
    ├── index.wxss
    └── index.json
```

## 页面说明

### 调试菜单 (`/test/debug-menu/index`)

主要的调试入口页面，提供了：

- **组件测试**: 访问各种组件的测试页面
- **页面测试**: 测试特定页面功能
- **数据测试**: 测试数据加载和处理
- **开发工具**: 清除缓存、查看系统信息等实用工具
- **环境信息**: 显示当前运行环境的详细信息

### Shop Card 组件测试 (`/test/test-shop-card/index`)

专门用于测试 Shop Card 组件的页面，包含：

- **多种测试用例**: 正常数据、更新状态、售罄状态、空数据等
- **事件日志**: 实时显示组件触发的事件
- **交互测试**: 测试点击事件和数据传递

## 访问方式

### 开发环境

在开发工具中可以直接访问：

```javascript
// 访问调试菜单
wx.navigateTo({
  url: '/test/debug-menu/index'
});

// 访问组件测试
wx.navigateTo({
  url: '/test/test-shop-card/index'
});
```

### 生产环境

由于这是一个子包，在生产环境中可以通过以下方式访问：

1. **通过页面路径**: 在地址栏输入完整路径
2. **通过代码跳转**: 在需要调试时临时添加跳转代码
3. **通过扫码**: 生成对应页面的二维码

## 使用建议

### 开发阶段

1. **组件开发**: 使用组件测试页面验证组件功能
2. **功能调试**: 使用调试菜单中的工具进行问题排查
3. **数据测试**: 验证不同数据情况下的表现

### 测试阶段

1. **回归测试**: 使用测试页面验证功能是否正常
2. **兼容性测试**: 在不同设备上测试组件表现
3. **性能测试**: 监控组件的性能表现

### 生产环境

1. **问题排查**: 当生产环境出现问题时，可以使用调试工具
2. **用户反馈**: 帮助用户清除缓存或查看环境信息
3. **紧急调试**: 在紧急情况下快速定位问题

## 注意事项

### 安全性

- 测试页面包含敏感的调试功能，不应该暴露给普通用户
- 在生产版本中考虑隐藏或限制访问权限
- 清除缓存功能会影响用户的登录状态

### 性能

- 子包采用懒加载，只有在访问时才会下载
- 不会影响主包的大小和加载速度
- 测试页面的资源消耗相对较小

### 维护

- 定期更新测试用例以覆盖新功能
- 保持测试数据的真实性和有效性
- 及时清理过时的测试页面

## 扩展指南

### 添加新的测试页面

1. 在 `test/` 目录下创建新的页面目录
2. 在 `app.json` 的 subPackages 中添加页面路径
3. 在调试菜单中添加对应的入口

### 添加测试工具

1. 在 `test/index.js` 中添加通用的测试工具函数
2. 在调试菜单中添加对应的功能入口
3. 确保工具的安全性和可用性

### 自定义测试数据

1. 在测试页面中定义符合实际情况的测试数据
2. 考虑边界情况和异常情况
3. 保持数据的多样性和代表性

## 常见问题

### Q: 为什么要使用子包？

A: 主要原因是微信小程序对主包大小有限制（2MB），将测试相关的页面放到子包中可以：
- 减少主包大小
- 提高主要功能的加载速度
- 按需加载测试功能

### Q: 如何在生产环境中访问测试页面？

A: 可以通过以下方式：
- 在开发者工具中直接输入页面路径
- 临时添加跳转代码（记得发布前移除）
- 通过特定的入口（如长按某个按钮）

### Q: 测试页面会影响性能吗？

A: 不会，因为：
- 子包采用懒加载机制
- 只有在访问时才会下载和执行
- 不会影响主要功能的性能

### Q: 如何确保测试页面的安全性？

A: 建议：
- 在生产环境中隐藏测试入口
- 添加访问权限控制
- 定期审查测试页面的功能
