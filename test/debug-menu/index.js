Page({
  data: {
    envInfo: {}
  },

  onLoad() {
    this.loadEnvInfo();
  },

  // 加载环境信息
  loadEnvInfo() {
    const systemInfo = wx.getSystemInfoSync();
    const accountInfo = wx.getAccountInfoSync();
    
    this.setData({
      envInfo: {
        version: accountInfo.miniProgram.version || '开发版',
        SDKVersion: systemInfo.SDKVersion,
        platform: systemInfo.platform,
        system: systemInfo.system,
        model: systemInfo.model
      }
    });
  },

  // 导航到指定页面
  navigateTo(e) {
    const url = e.currentTarget.dataset.url;
    if (url) {
      wx.navigateTo({
        url: url,
        fail: (err) => {
          console.error('导航失败:', err);
          wx.showToast({
            title: '页面不存在或路径错误',
            icon: 'none'
          });
        }
      });
    }
  },

  // 清除本地存储
  clearStorage() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有本地存储数据吗？这将清除登录状态、位置信息等。',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.clearStorageSync();
            wx.showToast({
              title: '清除成功',
              icon: 'success'
            });
          } catch (error) {
            console.error('清除存储失败:', error);
            wx.showToast({
              title: '清除失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  // 显示系统信息
  showSystemInfo() {
    const systemInfo = wx.getSystemInfoSync();
    const accountInfo = wx.getAccountInfoSync();
    
    const info = [
      `设备型号: ${systemInfo.model}`,
      `系统版本: ${systemInfo.system}`,
      `微信版本: ${systemInfo.version}`,
      `基础库版本: ${systemInfo.SDKVersion}`,
      `小程序版本: ${accountInfo.miniProgram.version || '开发版'}`,
      `屏幕尺寸: ${systemInfo.screenWidth}x${systemInfo.screenHeight}`,
      `可用窗口尺寸: ${systemInfo.windowWidth}x${systemInfo.windowHeight}`,
      `像素比: ${systemInfo.pixelRatio}`,
      `状态栏高度: ${systemInfo.statusBarHeight}px`,
      `安全区域: ${JSON.stringify(systemInfo.safeArea)}`
    ].join('\n');

    wx.showModal({
      title: '系统信息',
      content: info,
      showCancel: false,
      confirmText: '复制信息',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: info,
            success: () => {
              wx.showToast({
                title: '已复制到剪贴板',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  }
});
