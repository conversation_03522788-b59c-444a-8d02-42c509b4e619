.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15rpx;
  color: white;
}

.title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
}

.section {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  padding: 0 10rpx;
}

.menu-list {
  background-color: white;
  border-radius: 15rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 25rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f8f8;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.menu-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 5rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.menu-arrow {
  font-size: 30rpx;
  color: #ccc;
  margin-left: 15rpx;
}

.env-info {
  background-color: white;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-top: 30rpx;
}

.env-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.env-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.env-item:last-child {
  border-bottom: none;
}

.env-label {
  font-size: 26rpx;
  color: #666;
}

.env-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}
