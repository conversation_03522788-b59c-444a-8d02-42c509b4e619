<view class="container">
  <view class="header">
    <text class="title">调试菜单</text>
    <text class="subtitle">开发和测试工具集合</text>
  </view>

  <!-- 组件测试 -->
  <view class="section">
    <text class="section-title">组件测试</text>
    <view class="menu-list">
      <view class="menu-item" bindtap="navigateTo" data-url="/test/simple-test/index">
        <view class="menu-icon">🔧</view>
        <view class="menu-content">
          <text class="menu-name">简单测试</text>
          <text class="menu-desc">基础的组件显示测试</text>
        </view>
        <view class="menu-arrow">></view>
      </view>

      <view class="menu-item" bindtap="navigateTo" data-url="/test/test-shop-card/index">
        <view class="menu-icon">🏪</view>
        <view class="menu-content">
          <text class="menu-name">Shop Card 组件测试</text>
          <text class="menu-desc">测试店铺卡片组件的各种状态和交互</text>
        </view>
        <view class="menu-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 页面测试 -->
  <view class="section">
    <text class="section-title">页面测试</text>
    <view class="menu-list">
      <view class="menu-item" bindtap="navigateTo" data-url="/pages/debug-pagination/index">
        <view class="menu-icon">📄</view>
        <view class="menu-content">
          <text class="menu-name">分页调试</text>
          <text class="menu-desc">测试首页分页加载功能</text>
        </view>
        <view class="menu-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 数据测试 -->
  <view class="section">
    <text class="section-title">数据测试</text>
    <view class="menu-list">
      <view class="menu-item" bindtap="navigateTo" data-url="/pages/test-business-types/index">
        <view class="menu-icon">🔄</view>
        <view class="menu-content">
          <text class="menu-name">业务类型测试</text>
          <text class="menu-desc">测试不同业务类型的数据加载</text>
        </view>
        <view class="menu-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 工具 -->
  <view class="section">
    <text class="section-title">开发工具</text>
    <view class="menu-list">
      <view class="menu-item" bindtap="clearStorage">
        <view class="menu-icon">🗑️</view>
        <view class="menu-content">
          <text class="menu-name">清除本地存储</text>
          <text class="menu-desc">清除所有本地缓存数据</text>
        </view>
        <view class="menu-arrow">></view>
      </view>
      
      <view class="menu-item" bindtap="showSystemInfo">
        <view class="menu-icon">ℹ️</view>
        <view class="menu-content">
          <text class="menu-name">系统信息</text>
          <text class="menu-desc">显示设备和小程序信息</text>
        </view>
        <view class="menu-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 环境信息 -->
  <view class="env-info">
    <text class="env-title">环境信息</text>
    <view class="env-item">
      <text class="env-label">小程序版本:</text>
      <text class="env-value">{{envInfo.version}}</text>
    </view>
    <view class="env-item">
      <text class="env-label">基础库版本:</text>
      <text class="env-value">{{envInfo.SDKVersion}}</text>
    </view>
    <view class="env-item">
      <text class="env-label">平台:</text>
      <text class="env-value">{{envInfo.platform}}</text>
    </view>
  </view>
</view>
