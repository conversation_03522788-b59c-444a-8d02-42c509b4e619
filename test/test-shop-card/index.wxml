<view class="container">
  <view class="header">
    <text class="title">Shop Card 组件测试</text>
  </view>

  <!-- 测试数据 -->
  <view class="test-section">
    <text class="section-title">测试用例</text>
    
    <!-- 正常数据测试 -->
    <view class="test-case">
      <text class="case-title">1. 正常数据显示</text>
      <shop-card 
        shop="{{testShop1}}"
        isUpdatingStock="{{false}}"
        miniProgram="{{miniProgram}}"
        bind:tapstore="handleTapStore"
        bind:tapproduct="handleTapProduct">
      </shop-card>
    </view>

    <!-- 更新库存状态测试 -->
    <view class="test-case">
      <text class="case-title">2. 更新库存状态</text>
      <shop-card 
        shop="{{testShop2}}"
        isUpdatingStock="{{true}}"
        miniProgram="{{miniProgram}}"
        bind:tapstore="handleTapStore"
        bind:tapproduct="handleTapProduct">
      </shop-card>
    </view>

    <!-- 售罄状态测试 -->
    <view class="test-case">
      <text class="case-title">3. 售罄状态</text>
      <shop-card 
        shop="{{testShop3}}"
        isUpdatingStock="{{false}}"
        miniProgram="{{miniProgram}}"
        bind:tapstore="handleTapStore"
        bind:tapproduct="handleTapProduct">
      </shop-card>
    </view>

    <!-- 空促销列表测试 -->
    <view class="test-case">
      <text class="case-title">4. 无促销商品（不显示）</text>
      <shop-card 
        shop="{{testShop4}}"
        isUpdatingStock="{{false}}"
        miniProgram="{{miniProgram}}"
        bind:tapstore="handleTapStore"
        bind:tapproduct="handleTapProduct">
      </shop-card>
    </view>
  </view>

  <!-- 事件日志 -->
  <view class="log-section">
    <text class="section-title">事件日志</text>
    <view class="log-container">
      <view class="log-item" wx:for="{{eventLogs}}" wx:key="index">
        <text class="log-time">{{item.time}}</text>
        <text class="log-content">{{item.content}}</text>
      </view>
      <view class="empty-log" wx:if="{{eventLogs.length === 0}}">
        <text>暂无事件日志</text>
      </view>
    </view>
    <button class="clear-btn" bindtap="clearLogs">清空日志</button>
  </view>
</view>
