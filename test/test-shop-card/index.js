Page({
  data: {
    eventLogs: [],
    miniProgram: {
      version: false // 测试版本
    },
    // 测试数据1：正常数据
    testShop1: {
      _id: "shop001",
      name: "美味餐厅",
      tag: "人气王",
      district: "武昌区",
      distance: "0.5",
      logo: "https://placehold.co/60x60",
      promotions: [
        {
          _id: "promo001",
          name: "招牌牛肉面套餐",
          current_price: "19.9",
          original_price: "39.8",
          realtime_quantity: 15,
          platform: "dianping",
          requires: "1",
          level: 3,
          followers_count: 1000,
          tag: "热销",
          product_id: {
            _id: "prod001",
            images: ["https://placehold.co/180x180"]
          }
        },
        {
          _id: "promo002",
          name: "特色小笼包",
          current_price: "12.8",
          original_price: "25.6",
          realtime_quantity: 8,
          platform: "xiaohongshu",
          requires: "2",
          level: 1,
          followers_count: 500,
          product_id: {
            _id: "prod002",
            images: ["https://placehold.co/180x180"]
          }
        }
      ]
    },
    // 测试数据2：更新库存状态
    testShop2: {
      _id: "shop002",
      name: "咖啡时光",
      tag: "新店",
      district: "洪山区",
      distance: "1.2",
      logo: "https://placehold.co/60x60",
      promotions: [
        {
          _id: "promo003",
          name: "手冲咖啡套餐",
          current_price: "25.0",
          original_price: "50.0",
          realtime_quantity: 5,
          platform: "dianping",
          requires: "3",
          level: 2,
          followers_count: 800,
          product_id: {
            _id: "prod003",
            images: ["https://placehold.co/180x180"]
          }
        }
      ]
    },
    // 测试数据3：售罄状态
    testShop3: {
      _id: "shop003",
      name: "甜品工坊",
      tag: "限时",
      district: "江汉区",
      distance: "2.1",
      logo: "https://placehold.co/60x60",
      promotions: [
        {
          _id: "promo004",
          name: "芝士蛋糕",
          current_price: "15.9",
          original_price: "31.8",
          realtime_quantity: 0, // 售罄
          platform: "xiaohongshu",
          requires: "1",
          level: 1,
          followers_count: 200,
          product_id: {
            _id: "prod004",
            images: ["https://placehold.co/180x180"]
          }
        }
      ]
    },
    // 测试数据4：无促销商品
    testShop4: {
      _id: "shop004",
      name: "普通餐厅",
      tag: "一般",
      district: "硚口区",
      distance: "3.5",
      logo: "https://placehold.co/60x60",
      promotions: [] // 空促销列表
    }
  },

  onLoad() {
    this.addLog("页面加载完成");
  },

  // 处理店铺点击事件
  handleTapStore(e) {
    const { storeId, shop } = e.detail;
    this.addLog(`点击店铺: ${shop.name} (ID: ${storeId})`);
    
    // 这里可以添加实际的导航逻辑
    wx.showToast({
      title: `点击了店铺: ${shop.name}`,
      icon: 'none'
    });
  },

  // 处理商品点击事件
  handleTapProduct(e) {
    const { storeId, promotionId, shop } = e.detail;
    // 找到对应的促销商品
    const promotion = shop.promotions.find(p => p._id === promotionId);
    const productName = promotion ? promotion.name : '未知商品';
    
    this.addLog(`点击商品: ${productName} (促销ID: ${promotionId})`);
    
    // 这里可以添加实际的导航逻辑
    wx.showToast({
      title: `点击了商品: ${productName}`,
      icon: 'none'
    });
  },

  // 添加日志
  addLog(content) {
    const now = new Date();
    const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
    
    const logs = this.data.eventLogs;
    logs.unshift({
      time: time,
      content: content
    });
    
    // 只保留最近20条日志
    if (logs.length > 20) {
      logs.splice(20);
    }
    
    this.setData({
      eventLogs: logs
    });
  },

  // 清空日志
  clearLogs() {
    this.setData({
      eventLogs: []
    });
    wx.showToast({
      title: '日志已清空',
      icon: 'success'
    });
  }
});
