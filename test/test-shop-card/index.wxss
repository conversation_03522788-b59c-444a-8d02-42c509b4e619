.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: white;
  border-radius: 10rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.test-case {
  margin-bottom: 30rpx;
}

.case-title {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
  padding: 0 10rpx;
}

.log-section {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
}

.log-container {
  max-height: 400rpx;
  overflow-y: auto;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 15rpx;
  margin-bottom: 20rpx;
  background-color: #fafafa;
}

.log-item {
  display: flex;
  margin-bottom: 10rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.log-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.log-time {
  font-size: 22rpx;
  color: #999;
  margin-right: 15rpx;
  flex-shrink: 0;
  width: 120rpx;
}

.log-content {
  font-size: 24rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.empty-log {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 26rpx;
}

.clear-btn {
  width: 100%;
  background-color: #ff5f15;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
}
