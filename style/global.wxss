/*
 * @Author: oliverppeng
 * @LastEditors: Please set LastEditors
 * @Date: 2021-12-01 17:33:43
 * @LastEditTime: 2021-12-03 15:31:17
 * @Description: 
 * @FilePath: /retail-mp/style/global.wxss
 */
.text-primary {
	color: #fa550f;
}
.text-success {
	color: #5fb446;
}
.text-warn {
	color: #ec8131;
}
.text-danger {
	color: #de1c24;
}
.text-title {
	color: #282828;
}
.text-normal {
	color: #5d5d5d;
}
.text-small {
	color: #9b9b9b;
}
.text-minor {
	color: #ececec;
}
.text-border {
	color: #eeeeee;
}
.text-white {
	color: #fff;
}
.bg-primary {
	background-color: #fa550f;
	color: #fff;
}
.bg-success {
	background-color: #5fb446;
	color: #fff;
}
.bg-warn {
	background-color: #ec8131;
	color: #fff;
}
.bg-danger {
	background-color: #de1c24;
	color: #fff;
}
.bg-title {
	background-color: #282828;
	color: #fff;
}
.bg-normal {
	background-color: #5d5d5d;
	color: #282828;
}
.bg-small {
	background-color: #9b9b9b;
	color: #5d5d5d;
}
.bg-minor {
	background-color: #ececec;
	color: #5d5d5d;
}
.bg-border {
	background-color: #eeeeee;
	color: #5d5d5d;
}
.bd-primary {
	color: #fa550f;
}
.bd-success {
	color: #5fb446;
}
.bd-warn {
	color: #ec8131;
}
.bd-danger {
	color: #de1c24;
}
.bd-title {
	color: #282828;
}
.bd-normal {
	color: #5d5d5d;
}
.bd-small {
	color: #9b9b9b;
}
.bd-minor {
	color: #ececec;
}
.bd-border {
	color: #eeeeee;
}
.ft-super {
	font-size: 40rpx;
}
.ft-main {
	font-size: 36rpx;
}
.ft-normal {
	font-size: 32rpx;
}
.ft-assist {
	font-size: 28rpx;
}
.ft-minor {
	font-size: 24rpx;
}
.ft-mini {
	font-size: 20rpx;
}
.fw-super {
	font-weight: 800;
}
.fw-main {
	font-weight: 600;
}
.fw-normal {
	font-weight: 400;
}
.fw-minor {
	font-weight: 300;
}
.mo-border-1rpx {
	position: relative;
	z-index: 0;
}
.mo-border-1rpx::before {
	content: "";
	position: absolute;
	left: 0;
	top: 0;
	width: 200%;
	height: 200%;
	border-width: 2rpx;
	border-style: solid;
	transform: scale(0.5);
	transform-origin: 0 0;
	z-index: -1;
	box-sizing: border-box;
}
/* 层级定义
  @z-index-0: 1;
  @z-index-1: 100;
  @z-index-2: 200;
  @z-index-5: 500;
  @z-index-component: 1000; // 通用组件级别
  @z-index-dropdown: @z-index-component;
  @z-index-sticky: @z-index-component + 20;
  @z-index-fixed: @z-index-component + 30;
  @z-index-modal-backdrop:@z-index-component + 40;
  @z-index-modal:@z-index-component + 50;
  @z-index-popover:@z-index-component + 60;
  @z-index-tooltip:@z-index-component + 70;
  */
page {
	height: 100%;
	font-family: "Microsoft YaHei", "微软雅黑", "MicrosoftJhengHei", "华文细黑",
		Helvetica, Arial, "sans-serif";
	font-size: 26rpx;
	background-color: #f8f8f8;
	font-weight: 400;
}
view,
image,
icon,
scroll-view,
text,
button,
checkbox,
form,
input,
label,
navigator,
audio,
video,
canvas {
	box-sizing: border-box;
	margin: 0;
	padding: 0;
	border: 0;
	outline: 0;
}
scroll-view {
	height: 100%;
}
form,
image {
	display: block;
}
button {
	padding: 0;
	margin: 0;
	border-radius: 0;
	height: 100%;
	display: block;
	line-height: inherit;
	font-size: inherit;
	color: inherit;
	background: none;
	-webkit-appearance: none;
	border: none;
}
button::after {
	content: none;
}
input,
textarea {
	font-family: "Microsoft YaHei", "微软雅黑", "MicrosoftJhengHei", "华文细黑",
		Helvetica, Arial, "sans-serif";
	font-size: 26rpx;
	z-index: 0;
}
.price {
	color: #ec8131;
	font-size: 32rpx;
	font-weight: 600;
}
.price-del {
	color: #9b9b9b;
	font-size: 24rpx;
	font-weight: 400;
}
.page {
	background: #fff;
}
.color-price {
	color: #ec8131;
}
.bg-cart {
	background-color: #ffc220;
	color: #fff;
}
.market-addcart {
	color: #ec8131;
	font-size: 42rpx;
}
.ovh {
	overflow: hidden;
}
.hidden {
	display: none;
}
.show {
	display: block;
}
.text {
	display: inline-block;
}
.inline {
	display: inline;
}
.minHeight {
	min-height: 101%;
}
.imgCover {
	width: 100%;
	padding-bottom: 100%;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	display: block;
	position: relative;
}
.imgCover-list {
	width: 195rpx;
	height: 260rpx;
	padding-bottom: 0;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	display: block;
	position: relative;
}
.circular {
	border-radius: 50%;
}
.text-line1 {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	word-break: break-all;
}
.text-line-1 {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	word-break: break-all;
}
.text-line2 {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	word-break: break-all;
}
.text-line3 {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	word-break: break-all;
}
.flex,
.box {
	display: flex;
	display: -webkit-flex;
}
.flex-v-center {
	align-items: center;
	-webkit-align-items: center;
}
.flex-center {
	justify-content: center;
	-webkit-justify-content: center;
	align-items: center;
	-webkit-align-items: center;
}
.flex-between {
	justify-content: space-between;
	-webkit-justify-content: space-between;
}
.flex-v-between {
	align-content: space-between;
	-webkit-align-content: space-between;
}
.flex-end {
	justify-content: flex-end;
	-webkit-justify-content: flex-end;
}
.flex-col {
	flex-direction: column;
	-webkit-flex-direction: column;
}
.flex1 {
	flex: 1;
	-webkit-flex: 1;
}
.flex0 {
	flex: none;
	-webkit-flex: none;
}
.flex-start {
	justify-content: flex-start;
	-webkit-justify-content: flex-start;
}
.border-around,
.border-bottom-1px,
.border-left-1px,
.border-right-1px,
.border-top-1px {
	position: relative;
	border: 1rpx solid #e9e9e9;
}
.border-top-1px {
	border-width: 1rpx 0 0 0;
}
.border-right-1px {
	border-width: 0 1rpx 0 0;
}
.border-bottom-1px {
	border-width: 0 0 1rpx 0;
}
.border-left-1px {
	border-width: 0 0 0 1rpx;
}
.border-t-1px,
.border-r-1px,
.border-b-1px,
.border-l-1px {
	position: relative;
}
.border-t-1px::after,
.border-r-1px::after,
.border-b-1px::after,
.border-l-1px::after {
	content: "";
	position: absolute;
	border-width: 2rpx;
	border-color: #efefef;
	border-style: solid;
}
.border-t-1px::after {
	left: 0;
	top: 0;
	width: 100%;
	transform: scaleY(0.5);
}
.border-b-1px::after {
	left: 0;
	bottom: 0;
	width: 100%;
	transform: scaleY(0.5);
}
.border-l-1px::after {
	left: 0;
	top: 0;
	height: 100%;
	transform: scaleX(0.5);
}
.border-r-1px::after {
	right: 0;
	top: 0;
	height: 100%;
	transform: scaleX(0.5);
}
.arrows {
	position: relative;
}
.arrows::after {
	content: "";
	display: inline-block;
	width: 6px;
	height: 6px;
	border: 2px solid #c3c3c3;
	border-width: 2px 2px 0 0;
	position: absolute;
	top: 50%;
	right: 26rpx;
	margin-top: -3px;
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
}
.arrows-inline::after {
	position: relative;
	left: auto;
	top: auto;
	right: auto;
	bottom: auto;
	margin-top: -4px;
	margin-left: 5px;
}
.pt-16,
.pt-8 {
	padding-top: 16rpx;
}
.pb-16,
.pb-8 {
	padding-bottom: 16rpx;
}
.pl-16,
.pl-8 {
	padding-left: 16rpx;
}
.pr-16,
.pr-8 {
	padding-right: 16rpx;
}
.pl-20,
.pl-10 {
	padding-left: 20rpx;
}
.pr-20,
.pr-10 {
	padding-right: 20rpx;
}
.pl-30 {
	padding-left: 30rpx;
}
.pr-30 {
	padding-right: 30rpx;
}
.pl-32,
.pl-15 {
	padding-left: 32rpx;
}
.pr-32,
.pr-15 {
	padding-right: 32rpx;
}
.pb360 {
	padding-bottom: 360rpx;
}
.PriceSwitch {
	line-height: 21px;
	font-size: 24rpx;
	padding: 0 8rpx;
}
.PriceSwitch .i {
	font-size: 30rpx;
}
.Original {
	font-style: normal;
	font-size: 24rpx;
	color: #9a9a9a;
	text-decoration: line-through;
	margin-left: 16rpx;
	display: inline-block;
}
.color1,
h1,
h2,
h3,
h4,
h5,
h6 {
	font-weight: normal;
	color: #1a1a1a;
	margin: 0;
	padding: 0;
}
.color-3 {
	color: #333;
}
.color-6 {
	color: #666;
}
.color-9 {
	color: #999;
}
.color-e9 {
	color: #e9e9e9;
}
.color-64 {
	color: #646464;
}
.color-b4 {
	color: #b4b4b4;
}
.color-97 {
	color: #979797;
}
.color-9b {
	color: #9b9b9b;
}
.color-white {
	color: #fff;
}
.color-black {
	color: #000;
}
.color-c {
	color: #ccc;
}
.bkg-white {
	background-color: #fff;
}
.font10 {
	font-size: 20rpx;
}
.font11 {
	font-size: 22rpx;
}
.font12 {
	font-size: 24rpx;
}
.font13 {
	font-size: 26rpx;
}
.font14 {
	font-size: 28rpx;
}
.font15 {
	font-size: 30rpx;
}
.font16 {
	font-size: 32rpx;
}
.font17 {
	font-size: 34rpx;
}
.font18 {
	font-size: 36rpx;
}
.font19 {
	font-size: 38rpx;
}
.font20 {
	font-size: 20rpx;
}
.font22 {
	font-size: 22rpx;
}
.font24 {
	font-size: 24rpx;
}
.font26 {
	font-size: 26rpx;
}
.font28 {
	font-size: 28rpx;
}
.font30 {
	font-size: 30rpx;
}
.font32 {
	font-size: 32rpx;
}
.font34 {
	font-size: 34rpx;
}
.font36 {
	font-size: 36rpx;
}
.font38 {
	font-size: 38rpx;
}
.font40 {
	font-size: 40rpx;
}
.font46 {
	font-size: 46rpx;
}
.font50 {
	font-size: 50rpx;
}
.font56 {
	font-size: 56rpx;
}
.font82 {
	font-size: 82rpx;
}
.bkg-white {
	background-color: #fff;
}
.fontWeight-l {
	font-weight: 500;
}
.fontWeight-n {
	font-weight: 300;
}
.fontWeight-nor {
	font-weight: normal;
}
.line-height1 {
	line-height: 1;
}
.btn-active {
	width: 40rpx;
	display: inline-block;
	text-align: center;
	height: 40rpx;
	background: #ff2e45;
	border-radius: 8rpx;
	color: #fff;
	line-height: 40rpx;
	font-size: 24rpx;
	font-weight: normal;
	font-style: normal;
	overflow: hidden;
}
.btn-auto {
	display: inline-block;
	font-size: 20rpx;
	border: 1px solid #ff2e45;
	border-radius: 8rpx;
	height: 36rpx;
	line-height: 36rpx;
	color: #ff2e45;
	padding: 0 12rpx;
	margin-right: 16rpx;
}
.btn-lg,
.btn-md,
.btn-sm,
.btn-xs {
	text-align: center;
	width: 100%;
	border-radius: 8rpx;
	color: #fff;
	line-height: 88rpx;
	font-size: 30rpx;
}
.btn-lg,
.btn-md {
	background-color: #ff2e45;
}
.btn-sm {
	border: 1px solid #999;
	color: #666;
	height: 60rpx;
	line-height: 60rpx;
}
.btn-xs {
	width: 100%;
	height: 68rpx;
	line-height: 68rpx;
	border: 1px solid #e2e2e2;
	color: #666;
}
.btn-dashed,
.btn-md-dashed {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	color: #ff2e45;
	border-radius: 8rpx;
	border: 1px solid #ff2e45;
	font-size: 26rpx;
}
.btn-md-dashed {
	height: 50rpx;
	line-height: 50rpx;
	border-radius: 8rpx;
}
.btn-bj1 {
	background-color: #ff2e45;
}
.btn-bj2 {
	background-color: #ff8522;
}
.btn-dis {
	background-color: #999;
	color: #bbb;
}
.btn-dashed-dis {
	border: 1px solid #c8c8c8;
	color: #bbbbbb;
	border-radius: 8rpx;
}
.titleTag {
	line-height: 28rpx;
	height: 28rpx;
	margin-right: 8rpx;
	color: #fff;
	padding: 0 8rpx;
	font-size: 18rpx;
	border-radius: 4rpx;
	font-weight: bold;
	display: inline-block;
}
.titleTag.memberTag {
	margin-top: 2rpx;
}
.amounts {
	font-style: normal;
	display: inline-block;
	height: 24rpx;
	min-width: 24rpx;
	padding: 0 6rpx;
	border-radius: 24rpx;
	background-color: #fd1d45 !important;
	color: #fff;
	text-align: center;
	font-size: 20rpx;
	font-weight: 600;
	position: absolute;
	top: 6rpx;
	line-height: 24rpx;
	box-sizing: border-box;
}
.amounts:empty,
.titleTag:empty {
	display: none;
}
.loadMore {
	font-size: 26rpx;
	color: #c1c1c1;
	text-align: center;
	height: 80rpx;
	line-height: 58rpx;
	margin-top: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.loadings {
	margin-right: 10rpx;
	width: 40rpx;
	height: 40rpx;
	display: inline-block;
	vertical-align: sub;
	background: url(https://cdn.ghsmpwalmart.com/saas/market/image/loadings.png)
		no-repeat;
	background-size: 240rpx 40rpx;
	background-position: 0 0;
	animation: circle-loadings 1s steps(6) infinite;
}
@keyframes circle-loadings {
	from {
		background-position: 0 0;
	}
	to {
		background-position: -240rpx 0;
	}
}
.page-loading {
	display: inline-block;
	width: 72rpx;
	height: 75rpx;
	background: url("https://cdn.ghsmpwalmart.com/saas/market/image/page-loading-bc.png")
		no-repeat;
	background-position: 0 0;
	background-size: 72rpx 75rpx;
}
.page-loading::before {
	content: " ";
	display: inline-block;
	width: 72rpx;
	height: 75rpx;
	background: url("https://cdn.ghsmpwalmart.com/saas/market/image/page-loading-spin.png")
		no-repeat;
	background-position: 0 0;
	background-size: 720rpx 75rpx;
	animation: animate-page-loading 0.4s steps(10) infinite;
}
@keyframes animate-page-loading {
	from {
		background-position-x: 0rpx;
	}
	to {
		background-position-x: -720rpx;
	}
}
.page-loading-wrap {
	width: 100%;
	height: 1026rpx;
	display: flex;
	display: -webkit-flex;
	justify-content: center;
	-webkit-justify-content: center;
	padding-top: 46vh;
}
.grooms {
	margin: 0 16rpx;
	overflow: hidden;
	text-align: center;
	height: 52rpx;
}
.grooms label {
	display: inline-block;
	padding: 0 20rpx;
	height: 52rpx;
	line-height: 52rpx;
	position: relative;
	color: #3e3e3e;
	font-size: 26rpx;
}
.grooms label > p {
	overflow: hidden;
	max-width: 400rpx;
	min-width: 120rpx;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.grooms label > p::before {
	content: "";
	display: inline-block;
	position: absolute;
	width: 1000rpx;
	margin-left: -1000rpx;
	height: 0;
	left: 0;
	top: 50%;
	border: 1px solid #e9e9e9;
	border-width: 1px 0 0 0;
}
.grooms label > p::after {
	content: "";
	display: inline-block;
	position: absolute;
	width: 1000rpx;
	margin-left: -1000rpx;
	height: 0;
	left: 0;
	left: inherit;
	top: 50%;
	border: 1px solid #e9e9e9;
	border-width: 0 0 1px 0;
	right: 0;
	margin-right: -1000rpx;
}
.navHeight {
	height: 100rpx;
}
.widget-mask,
.widget_mask {
	position: fixed;
	left: 0;
	top: 0px;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	z-index: 200;
}
.theme-color {
	color: #f12d22;
}
.theme-bgc {
	background-color: #f12d22;
}
.theme-bdc {
	border-color: #f12d22;
}
.theme-bgcart {
	background-color: #ffb56c;
}
.IphoneX {
	padding-bottom: 68rpx !important;
}
.fixIphonex {
	bottom: 68rpx !important;
}
.fixIphonex::after {
	content: " ";
	position: fixed;
	bottom: 0 !important;
	height: 68rpx !important;
	width: 100%;
	background: #ffffff;
	left: 0;
}
.iphonexMenus {
	bottom: 166rpx !important;
}
.imgCover-oblong-sm .imgCover {
	width: 195rpx;
	height: 260rpx;
}
.imgCover-oblong-lg .imgCover {
	padding-bottom: 133%;
}
.fw-Light {
	font-weight: 300;
}
.fw-Regular {
	font-weight: 400;
}
.fw-Medium {
	font-weight: 600;
}
.fw-Semibold {
	font-weight: 800;
}
.icon-xingouwuche {
	font-size: 42rpx;
}
.color777 {
	color: #777777;
}
.popup-header {
	color: #000;
	font-size: 28rpx;
	text-align: center;
	height: 100rpx;
	line-height: 100rpx;
	position: relative;
	border-bottom: 1rpx solid #dbdbdb;
	font-weight: 400;
}
.popup-header .wr-close {
	position: absolute;
	left: 0;
	font-size: 26rpx;
	color: #5d5d5d;
	width: 60rpx;
	height: 60rpx;
	text-align: center;
	line-height: 60rpx;
	top: 20rpx;
	left: 10rpx;
}

/* 将品牌色改为红色 */
page {
	--td-brand-color: #f12d22;
}

.flex {
	display: flex;
}

.items-center {
	align-items: center;
}

.t-toast {
	--td-toast-max-width: 800rpx;
}
