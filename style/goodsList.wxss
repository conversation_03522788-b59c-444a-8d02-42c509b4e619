/* 层级定义
@z-index-0: 1;
@z-index-1: 100;
@z-index-2: 200;
@z-index-5: 500;
@z-index-component: 1000; // 通用组件级别
@z-index-dropdown: @z-index-component;
@z-index-sticky: @z-index-component + 20;
@z-index-fixed: @z-index-component + 30;
@z-index-modal-backdrop:@z-index-component + 40;
@z-index-modal:@z-index-component + 50;
@z-index-popover:@z-index-component + 60;
@z-index-tooltip:@z-index-component + 70;
*/
/* var() css变量适配*/
.goods-list-wrap {
  padding-left: 24rpx;
  background-color: #fff;
}
.goods-list-wrap .wr-goods-card {
  padding: 24rpx 24rpx 24rpx 0;
  border-bottom: 1rpx solid #e6e6e6;
  background-color: #fff;
}
.goods-list-wrap .wr-goods-card.no-border {
  border-bottom: none;
}
.goods-list-wrap .wr-goods-card .wr-goods-card__thumb {
  width: 200rpx;
  height: 200rpx;
  margin-right: 24rpx;
  border-radius: 8rpx;
  overflow: hidden;
}
.goods-list-wrap .wr-goods-card .wr-goods-card__content {
  position: relative;
  display: flex;
  flex-flow: column nowrap;
  justify-content: space-between;
  width: 478rpx;
  height: 200rpx;
}
.goods-list-wrap .wr-goods-card .wr-goods-card__content .wr-goods-card__title {
  overflow: hidden;
  margin-bottom: 24rpx;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  display: -webkit-box;
  font-size: 26rpx;
  line-height: 36rpx;
  font-weight: 400;
}
.goods-list-wrap .wr-goods-card .wr-goods-card__content .card-index--wr-goods-card__price {
  font-size: 32rpx;
}
.goods-list-wrap .wr-goods-card .wr-goods-card__content .card-index--wr-goods-card__price .symbol {
  font-size: 24rpx;
}
.goods-list-wrap .wr-goods-card .wr-goods-card__tags {
  flex-grow: 2;
}
.goods-list-wrap .wr-goods-card .goods-card-tags-wrap {
  color: #fa550f;
  color: var(--color-primary, #fa550f);
  display: flex;
  height: 30rpx;
  flex-flow: row wrap;
  text-align: center;
  width: 100%;
  flex-shrink: 0;
}
.goods-list-wrap .wr-goods-card .goods-card-tags-wrap .tag {
  box-sizing: border-box;
  font-size: 20rpx;
  border-radius: 4rpx;
  flex-shrink: 0;
  vertical-align: middle;
  margin-right: 8rpx;
  background-color: #fff;
}
.goods-list-wrap .wr-goods-card .goods-card-tags-wrap .tag::after {
  border-radius: 4rpx;
  border: 2rpx solid #fa550f;
  border: 2rpx solid var(--color-primary, #fa550f);
}
.goods-list-wrap .wr-goods-card .goods-add-cart {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 0;
  bottom: 0;
  width: 48rpx;
}
.goods-list-wrap .wr-goods-card .goods-add-cart .goods-add-cart {
  line-height: 48rpx;
  height: 48rpx;
}
.goods-list-wrap.vertical {
  padding: 20rpx 24rpx;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  background-color: transparent;
}
.goods-list-wrap.vertical .wr-goods-card {
  width: 340rpx;
  height: 574rpx;
  overflow: hidden;
  padding: 0;
  border-bottom: none;
  display: flex;
  flex-flow: column nowrap;
  border-radius: 8px;
  margin-bottom: 24rpx;
}
.goods-list-wrap.vertical .wr-goods-card .wr-goods-card__thumb {
  width: 100%;
  height: 340rpx;
}
.goods-list-wrap.vertical .wr-goods-card .wr-goods-card__content {
  width: 100%;
  padding: 20rpx;
  overflow: hidden;
  height: 234rpx;
  box-sizing: border-box;
}
.goods-list-wrap.vertical .wr-goods-card .wr-goods-card__content .wr-goods-card__title {
  -webkit-box-orient: horizontal;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  height: 36rpx;
  color: #333;
  white-space: nowrap;
  display: block;
}
.goods-list-wrap.vertical .wr-goods-card .wr-goods-card__content .card-index--wr-goods-card__origin-price {
  position: absolute;
  left: 20rpx;
  bottom: 72rpx;
  margin-left: 0;
  font-size: 24rpx;
  color: #aaaaaa;
}
.goods-list-wrap.vertical .wr-goods-card .wr-goods-card__content .wr-goods-card__groupon-price {
  position: absolute;
  left: 20rpx;
  bottom: 72rpx;
  margin-left: 0;
  font-size: 24rpx;
  color: #aaaaaa;
}
.goods-list-wrap.vertical .wr-goods-card .goods-add-cart {
  right: 20rpx;
  bottom: 20rpx;
}
.goods-list-wrap.vertical .grouponPrice {
  margin-bottom: 50rpx;
}
.goods-list-wrap .wr-goods-card__twoLine .wr-goods-card__title {
  -webkit-line-clamp: 2 !important;
  line-clamp: 2 !important;
  display: -webkit-box !important;
  white-space: normal !important;
  height: auto !important;
  -webkit-box-orient: vertical !important;
}
