{"editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.enable": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.tslint": "explicit", "source.fixAll.eslint": "explicit"}, "[javascript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[wxss]": {"editor.defaultFormatter": "HookyQR.beautify"}, "wxmlConfig.onSaveFormat": true, "wxmlConfig.format": {"brace_style": "collapse", "indent_inner_html": true, "indent_scripts": "keep", "indent_size": 2, "indent_char": " ", "unformatted": "['wxs']", "disable_automatic_closing_labels": false, "preserve_newlines": true, "wrap_attributes": "force-expand-multiline", "wrap_attributes_count": 4, "wrap_attributes_indent_size": 2}, "editor.tabSize": 2, "[wxml]": {"editor.defaultFormatter": "wechat.miniprogram.wxml-language-features"}, "[jsonc]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[html]": {"editor.defaultFormatter": "vscode.html-language-features"}}