{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": []}, "formatter": {"enabled": true, "indentStyle": "tab"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": false, "complexity": {"noUselessConstructor": "error", "noUselessTernary": "warn", "useLiteralKeys": "warn"}, "correctness": {"noConstAssign": "error", "noInvalidUseBeforeDeclaration": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "off", "noUnusedVariables": "error", "useArrayLiterals": "error"}, "style": {"noParameterAssign": "warn", "useBlockStatements": "off", "useConsistentBuiltinInstantiation": "warn", "useConst": "error", "useSingleVarDeclarator": "warn", "useTemplate": "error"}, "suspicious": {"noConsole": {"level": "off", "options": {"allow": ["warn", "error", "info"]}}, "noDebugger": "error", "noDoubleEquals": "warn", "noDuplicateClassMembers": "error", "noPrototypeBuiltins": "error"}}, "ignore": ["**/*.wxs"]}, "javascript": {"formatter": {"quoteStyle": "double"}, "globals": ["Behavior", "wx", "App", "getApp", "getCurrentPages", "Component", "Page", "__wxConfig", "global"]}}