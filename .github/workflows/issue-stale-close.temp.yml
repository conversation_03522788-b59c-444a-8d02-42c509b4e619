# force copy from tencent/tdesign
# 国际标准时间+8
name: Close stale issues and PRs
on:
  schedule:
    - cron: "50 5 * * *"

jobs:
  close-issues:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v4
        with:
          stale-issue-message: "这个 Issue 被标记为了过时 stale ，因为它已经持续 30 天没有任何活动了。删除 stale 标签或评论，否则将在 7 天内关闭。"
          stale-pr-message: '这个 PR 已经过时了，因为它已经持续 45 天没有任何活动了。 删除 stale 的标签或评论，否则将在 10 天内关闭。'
          close-issue-message: "此 Issue 被自动关闭，因为它自被标记为过时 stale 以来已闲置 7 天。"
          close-pr-message: "此 PR 被自动关闭，因为它已经 stable 停滞了 10 天，没有任何活动。"
          days-before-stale: 30
          days-before-close: 7
          days-before-pr-stale: 45
          days-before-pr-close: 10
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          exempt-issue-labels: 'WIP'
          exempt-pr-labels: 'WIP'