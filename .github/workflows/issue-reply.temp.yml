# force copy from tencent/tdesign
# 当被打上 Need Reproduce 标签时候，自动提示需要重现实例

name: ISSUE_REPLY

on:
  issues:
    types: [labeled]

jobs:
  issue-reply:
    runs-on: ubuntu-latest
    steps:
      - name: Need Reproduce
        if: github.event.label.name == 'Need Reproduce'
        uses: actions-cool/issues-helper@v2
        with:
          actions: 'create-comment'
          issue-number: ${{ github.event.issue.number }}
          body: |       
            你好 @${{ github.event.issue.user.login }}, 我们需要你提供一个在线的重现实例以便于我们帮你排查问题。你可以通过点击 [此处](https://codesandbox.io/) 创建一个 codesandbox 或者提供一个最小化的 GitHub 仓库。请确保选择准确的版本。
