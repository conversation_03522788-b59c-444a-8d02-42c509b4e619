import { cdnBase } from '../config/index';
const imgPrefix = cdnBase;

// const defaultDesc = [`${imgPrefix}/goods/details-1.png`];
const defaultShopLogo = "https://placehold.co/80x80"
const defaultProductImage = "https://placehold.co/140x140"

const allStores = [
  {
    id: 1,
    storeName: "煌璟尔灿地",
    tag: "金银酒家",
    rating: 5.0,
    location: "东西湖区",
    distance: "8.36km",
    popularity: "东西湖区小吃第1名",
    badges: ["随时退", "过期退"],
    logoUrl: "https://placehold.co/48x48/FFA07A/fff?text=餐",
    foodItems: [
      {
        id: 11,
        name: "【当天有暗选啥】超值单人餐wh",
        imageUrl: "https://placehold.co/160x160/FFB6C1/fff?text=美食",
        originalPrice: 20,
        currentPrice: 2,
        discount: 18,
        couponCount: 1,
        tag: "大众点评等级≥4",
      },
      {
        id: 12,
        name: "【当天有暗选啥】超值单人餐wh",
        imageUrl: "https://placehold.co/160x160/FFB6C1/fff?text=美食",
        originalPrice: 20,
        currentPrice: 2,
        discount: 18,
        couponCount: 2,
        tag: "小红书粉丝数≥150",
      },
    ],
  },
  {
    id: 2,
    storeName: "金牌小厨",
    tag: "中式快餐",
    rating: 4.8,
    location: "洪山区",
    distance: "3.2km",
    popularity: "洪山区快餐第2名",
    badges: ["随时退"],
    logoUrl: "https://placehold.co/48x48/90EE90/fff?text=厨",
    foodItems: [
      {
        id: 21,
        name: "双人超值套餐A",
        imageUrl: "https://placehold.co/160x160/ADD8E6/fff?text=套餐",
        originalPrice: 40,
        currentPrice: 25,
        discount: 15,
        couponCount: 1,
        tag: "大众点评等级≥4",
      },
    ],
  },
];

/**
 * @param {string} id
 * @param {number} [available] 库存, 默认1
 */
export function genStore(id, available = 1) {
  // const specID = ['135681624', '135681628'];
  // if (specID.indexOf(id) > -1) {
  //   return allStores.filter((good) => good.spuId === id)[0];
  // }
  const item = allStores[id % allStores.length];
  return {
    ...item,
    // spuId: `${id}`,
    // available: available,
    // desc: item?.desc || defaultDesc,
    // images: item?.images || [item?.primaryImage],
  };
}