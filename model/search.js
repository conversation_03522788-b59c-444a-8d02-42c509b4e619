import { getGoodsList } from "./goods";

/**
 * @param {number} sort
 * @param {number} pageNum
 * @param {number} pageSize
 * @param {number} minPrice
 * @param {number} maxPrice
 * @param {string} keyword
 */

export function getSearchHistory() {
	return {
		historyWords: [
			"鸡",
			"电脑",
			"iPhone12",
			"车载手机支架",
			"自然堂",
			"小米10",
			"原浆古井贡酒",
			"欧米伽",
			"华为",
			"针织半身裙",
			"氢跑鞋",
			"三盒处理器",
		],
	};
}

export function getSearchPopular() {
	return {
		popularWords: [
			"曾宴",
			"海鲜生",
			"自助餐",
			"火锅",
			"星聚会",
			"牛肉",
			"赤红",
			"咖啡",
			"如院",
			"木子木水",
			"蛋糕",
			"面包",
			"庭院",
			"烤肉",
		],
	};
}

export function getSearchResult() {
	return {
		saasId: null,
		storeId: null,
		pageNum: 1,
		pageSize: 30,
		totalCount: 1,
		spuList: getGoodsList(7),
		algId: 0,
	};
}
