# Shop Card 组件问题排查指南

## 问题描述
组件没有在页面上显示任何内容。

## 可能的原因和解决方案

### 1. 数据问题

#### 检查店铺数据结构
确保传入的 `shop` 对象包含必要的字段：

```javascript
{
  _id: "店铺ID",
  name: "店铺名称", 
  tag: "店铺标签",
  district: "所在区域",
  distance: "距离",
  logo: "Logo URL",
  promotions: [
    {
      _id: "促销ID",
      name: "促销名称",
      current_price: "当前价格",
      original_price: "原价",
      realtime_quantity: 库存数量,
      platform: "平台",
      requires: "要求类型",
      level: 等级要求,
      followers_count: 粉丝数要求,
      tag: "促销标签",
      product_id: {
        _id: "商品ID",
        images: ["图片URL数组"]
      }
    }
  ]
}
```

#### 检查促销数组
组件只在 `shop.promotions.length > 0` 时才显示。如果促销数组为空，组件不会渲染。

### 2. 组件引用问题

#### 检查组件配置
确保在页面的 JSON 文件中正确引用了组件：

```json
{
  "usingComponents": {
    "shop-card": "/components/shop-card/shop-card"
  }
}
```

#### 检查路径
确保组件路径正确，文件存在。

### 3. WXS 问题

#### 检查 WXS 文件
确保 `filters.wxs` 文件存在且语法正确：

```javascript
// filters.wxs 应该包含格式化函数
function formatDistance(distance) {
  // 实现代码
}

module.exports = {
  formatDistance: formatDistance,
  // 其他函数...
};
```

#### 检查 WXS 引用
确保在组件 WXML 中正确引用了 WXS：

```xml
<wxs src="./filters.wxs" module="filter" />
```

### 4. 样式问题

#### 检查样式文件
确保 `shop-card.wxss` 文件存在且样式正确。

#### 检查容器样式
可能组件被渲染了但是样式导致不可见：

```css
.store-card {
  /* 确保有合适的尺寸和背景 */
  background-color: #fff;
  margin-bottom: 20rpx;
  /* 其他样式... */
}
```

### 5. 依赖组件问题

#### 检查 TDesign 组件
确保项目中正确安装和配置了 TDesign 组件库：

- `t-image`
- `t-loading` 
- `t-icon`

#### 检查组件版本
确保 TDesign 组件库版本兼容。

## 调试步骤

### 1. 基础检查

```javascript
// 在页面的 onLoad 中添加调试代码
onLoad() {
  console.log("页面数据:", this.data);
  console.log("店铺数据:", this.data.shop || this.data.testShop1);
}
```

### 2. 组件内部调试

在组件的 `attached` 生命周期中添加调试：

```javascript
// shop-card.js
lifetimes: {
  attached() {
    console.log("组件已挂载");
    console.log("组件接收到的数据:", this.data.shop);
    console.log("促销数量:", this.data.shop?.promotions?.length || 0);
  }
}
```

### 3. 条件渲染检查

临时移除条件渲染来测试：

```xml
<!-- 临时移除这个条件 -->
<!-- <view class="store-card" wx:if="{{shop.promotions.length > 0}}"> -->
<view class="store-card">
  <!-- 组件内容 -->
</view>
```

### 4. 简化测试

创建最简单的测试数据：

```javascript
{
  _id: "test",
  name: "测试店铺",
  promotions: [
    {
      _id: "test-promo",
      name: "测试商品",
      current_price: "10",
      original_price: "20",
      realtime_quantity: 1,
      product_id: {
        images: ["https://via.placeholder.com/180x180"]
      }
    }
  ]
}
```

## 常见解决方案

### 方案1: 检查数据传递

```xml
<!-- 在使用组件的页面中添加调试信息 -->
<view>调试: {{shop.name}} - 促销数量: {{shop.promotions.length}}</view>
<shop-card shop="{{shop}}" />
```

### 方案2: 使用简单测试页面

访问 `/test/simple-test/index` 页面进行基础测试。

### 方案3: 检查控制台错误

打开开发者工具的控制台，查看是否有错误信息。

### 方案4: 重新编译

1. 清除缓存
2. 重新编译项目
3. 刷新页面

## 验证步骤

### 1. 数据验证
```javascript
// 确保数据结构正确
console.log("店铺数据验证:", {
  hasId: !!shop._id,
  hasName: !!shop.name,
  hasPromotions: Array.isArray(shop.promotions),
  promotionCount: shop.promotions?.length || 0
});
```

### 2. 组件验证
```javascript
// 在组件中验证
attached() {
  const shop = this.data.shop;
  if (!shop) {
    console.error("组件未接收到店铺数据");
    return;
  }
  
  if (!shop.promotions || shop.promotions.length === 0) {
    console.warn("店铺没有促销数据，组件不会显示");
    return;
  }
  
  console.log("组件数据验证通过");
}
```

### 3. 渲染验证
```xml
<!-- 添加可见的调试元素 -->
<view style="background: red; padding: 20rpx;">
  组件已渲染 - 店铺: {{shop.name}}
</view>
```

## 如果问题仍然存在

1. **检查小程序基础库版本**: 确保支持组件和 WXS 功能
2. **检查项目配置**: 确保 `app.json` 配置正确
3. **重新创建组件**: 如果怀疑组件文件损坏，重新创建
4. **查看官方文档**: 参考微信小程序组件开发文档
5. **寻求帮助**: 提供详细的错误信息和代码片段

## 预防措施

1. **数据验证**: 在使用组件前验证数据完整性
2. **错误处理**: 在组件中添加错误处理逻辑
3. **测试覆盖**: 为组件创建全面的测试用例
4. **文档维护**: 保持组件使用文档的更新
