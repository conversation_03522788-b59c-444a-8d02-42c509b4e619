Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 店铺数据
    shop: {
      type: Object,
      value: {}
    },
    // 是否正在更新库存
    isUpdatingStock: {
      type: Boolean,
      value: false
    },
    // 小程序信息
    miniProgram: {
      type: Object,
      value: {}
    },
    // 骨架屏配置
    rowColsImage: {
      type: Array,
      value: []
    },
    rowColsContent: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 组件内部数据
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击店铺
    handleTapStore(e) {
      this.triggerEvent('tapstore', {
        storeId: this.data.shop._id,
        shop: this.data.shop
      });
    },

    // 点击商品/促销
    handleTapProduct(e) {
      const promotionId = e.currentTarget.dataset.promotionId;
      this.triggerEvent('tapproduct', {
        storeId: this.data.shop._id,
        promotionId: promotionId,
        shop: this.data.shop
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被放入页面节点树后执行
    },
    detached() {
      // 组件实例被从页面节点树移除后执行
    }
  }
});
