# Shop Card 组件

一个用于显示店铺信息和促销商品的卡片组件。

## 功能特性

- 显示店铺基本信息（名称、标签、距离等）
- 展示店铺的促销商品列表
- 支持商品图片懒加载
- 内置价格格式化和距离格式化
- 支持点击事件（店铺点击、商品点击）
- 响应式设计，适配不同屏幕尺寸

## 使用方法

### 1. 在页面配置中引入组件

```json
{
  "usingComponents": {
    "shop-card": "/components/shop-card/shop-card"
  }
}
```

### 2. 在 WXML 中使用

```xml
<shop-card 
  shop="{{shop}}"
  isUpdatingStock="{{isUpdatingStock}}"
  miniProgram="{{miniProgram}}"
  rowColsImage="{{rowColsImage}}"
  rowColsContent="{{rowColsContent}}"
  bind:tapstore="handleTapStore"
  bind:tapproduct="handleTapProduct">
</shop-card>
```

### 3. 在 JS 中处理事件

```javascript
Page({
  // 处理店铺点击事件
  handleTapStore(e) {
    const { storeId, shop } = e.detail;
    console.log('点击了店铺:', storeId, shop);
    wx.navigateTo({
      url: `/pages/store/index?id=${storeId}`
    });
  },

  // 处理商品点击事件
  handleTapProduct(e) {
    const { storeId, promotionId, shop } = e.detail;
    console.log('点击了商品:', promotionId);
    wx.navigateTo({
      url: `/pages/promotion/index?id=${promotionId}`
    });
  }
});
```

## 属性说明

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| shop | Object | {} | 是 | 店铺数据对象 |
| isUpdatingStock | Boolean | false | 否 | 是否正在更新库存 |
| miniProgram | Object | {} | 否 | 小程序信息对象 |
| rowColsImage | Array | [] | 否 | 骨架屏图片配置 |
| rowColsContent | Array | [] | 否 | 骨架屏内容配置 |

### shop 对象结构

```javascript
{
  _id: "店铺ID",
  name: "店铺名称",
  tag: "店铺标签",
  district: "所在区域",
  distance: "距离（公里）",
  logo: "店铺Logo URL",
  promotions: [
    {
      _id: "促销ID",
      name: "促销名称",
      current_price: "当前价格",
      original_price: "原价",
      realtime_quantity: "实时库存",
      platform: "平台（dianping/xiaohongshu等）",
      requires: "要求类型（1:等级 2:粉丝 3:等级+粉丝）",
      level: "等级要求",
      followers_count: "粉丝数要求",
      product_id: {
        _id: "商品ID",
        images: ["商品图片URL数组"]
      }
    }
  ]
}
```

## 事件说明

### tapstore 事件

当用户点击店铺信息区域时触发。

**事件对象**:
```javascript
{
  detail: {
    storeId: "店铺ID",
    shop: "完整的店铺对象"
  }
}
```

### tapproduct 事件

当用户点击商品/促销信息时触发。

**事件对象**:
```javascript
{
  detail: {
    storeId: "店铺ID",
    promotionId: "促销ID",
    shop: "完整的店铺对象"
  }
}
```

## 样式定制

组件使用独立的样式文件，可以通过以下方式进行定制：

1. **修改组件样式**: 直接编辑 `shop-card.wxss` 文件
2. **外部样式覆盖**: 在使用组件的页面中定义同名样式类进行覆盖

### 主要样式类

- `.store-card`: 卡片容器
- `.store-info-compact`: 店铺信息区域
- `.food-list`: 商品列表容器
- `.food-card`: 单个商品卡片
- `.buy-button`: 购买按钮

## 依赖组件

组件依赖以下 TDesign 组件：

- `t-image`: 图片组件
- `t-loading`: 加载组件
- `t-icon`: 图标组件
- `t-skeleton`: 骨架屏组件

确保项目中已正确引入 TDesign 组件库。

## 注意事项

1. **数据格式**: 确保传入的 `shop` 对象包含必要的字段
2. **图片加载**: 组件内置了图片懒加载和加载状态显示
3. **事件处理**: 使用 `bind:` 语法绑定事件处理函数
4. **性能优化**: 组件内部使用了 `wx:key` 进行列表优化

## 更新日志

### v1.0.0
- 初始版本
- 支持店铺信息展示
- 支持促销商品列表
- 内置格式化函数
- 支持点击事件
