<!-- 店铺卡片组件 -->
<wxs src="./filters.wxs" module="filter" />
<view class="store-card" wx:if="{{shop.promotions.length > 0}}">
  <!-- 店铺信息 - 紧凑版 -->
  <view class="store-info-compact" hover-class="store-hover" bindtap="handleTapStore">

    <view class="store-logo">
      <t-image src="{{shop.logo}}" class="logo-image-small" shape="circle" loading="slot">
        <t-loading slot="loading" theme="spinner" size="30rpx" loading />
      </t-image>
    </view>

    <view class="store-header-row">
      <!-- 商店Logo + 名称 -->
      <view class="store-name-compact">{{shop.name}}</view>
      <view class="store-tag-compact">{{shop.tag}}</view>
    </view>

    <view class="store-right">
      <view class="location-distance">
        <text class="location-text">{{shop.district}}</text>
        <text class="distance-text">{{filter.formatDistance(shop.distance)}}</text>
      </view>

      <view class="badge-container">
        <block>
          <view class="badge-compact">
            <t-icon name="swap" size="20rpx" color="#FF5F15" /> 随时退
            <t-icon name="time" size="20rpx" color="#FF5F15" /> 过期退
          </view>
        </block>
      </view>
    </view>
  </view>

  <!-- 食品列表 -->
  <view class="food-list">
    <view class="food-card" wx:if="{{shop.promotions.length === 0}}">
      <t-skeleton class="group-avatar" rowCol="{{rowColsImage}}" loading></t-skeleton>
      <t-skeleton class="group-content" rowCol="{{rowColsContent}}" loading></t-skeleton>
    </view>

    <block wx:for="{{shop.promotions}}" wx:key="_id" wx:for-item="promotion">
      <view class="food-card" hover-class="food-card-hover" bindtap="handleTapProduct"
        data-promotion-id="{{promotion._id}}">
        <!-- 食品图片 -->
        <view class="food-image-container">
          <t-image src="{{promotion.product_id.images[0]}}" mode="aspectFill"
            id="{{promotion._id+promotion.product_id._id}}" shape="square" loading="slot" class="food-image">
            <t-loading slot="loading" theme="spinner" size="40rpx" loading />
          </t-image>
        </view>

        <view class="food-info">
          <!-- 第一行：标签和剩余份数放在同一行 -->
          <view class="food-top-row">
            <!-- 食品标签 -->
            <view class="food-tag-compact" wx:if="{{promotion.tag}}">
              <text>{{promotion.tag}}</text>
            </view>

            <!-- 剩余份数标签 -->
            <view class="coupon-count-container" wx:if="{{!miniProgram.version}}">
              <t-image
                src="https://636c-cloud1-0gpy573m8caa7db3-**********.tcb.qcloud.la/app/dazhongdianping.svg"
                width="16" height="16" mode="aspectFill" style="display: flex;"
                wx:if="{{promotion.platform === 'dianping'}}" />
              <t-image src="https://636c-cloud1-0gpy573m8caa7db3-**********.tcb.qcloud.la/app/xiaohongshu.svg"
                width="16" height="16" mode="aspectFill" style="display: flex;"
                wx:if="{{promotion.platform === 'xiaohongshu'}}" />
              <text class="coupon-count"
                wx:if="{{!miniProgram.version}}">{{filter.formatPlatform(promotion.platform)}}{{filter.formatRequirements(promotion.requires,
                promotion.level, promotion.followers_count)}}</text>
            </view>
          </view>

          <!-- 第二行：商品名称单独一行 -->
          <view class="food-title-area">
            <view class="food-name-compact">{{promotion.name}}</view>
          </view>

          <!-- 第三行：价格信息和按钮 -->
          <view class="food-footer-row">
            <!-- 左侧: 价格和优惠标签 -->
            <view class="price-discount-group">
              <text class="current-price">¥{{promotion.current_price}}</text>
              <text class="original-price">¥{{promotion.original_price}}</text>
              <view class="discount-tag-compact">
                <text>省¥{{filter.toFixed(promotion.original_price - promotion.current_price)}}</text>
              </view>
            </view>

            <!-- 右侧：抢单按钮 -->
            <block wx:if="{{isUpdatingStock}}">
              <view class="buy-button soldout" hover-class="buy-button-hover" catchtap="handleTapProduct"
                data-promotion-id="{{promotion._id}}">
                <text>更新中</text>
              </view>
            </block>
            <block wx:if="{{!isUpdatingStock}}">
              <view wx:if="{{promotion.realtime_quantity > 0}}" class="buy-button"
                hover-class="buy-button-hover" catchtap="handleTapProduct"
                data-promotion-id="{{promotion._id}}">
                <text>去抢单</text>
              </view>
              <view wx:else class="buy-button soldout" hover-class="buy-button-hover"
                catchtap="handleTapProduct" data-promotion-id="{{promotion._id}}">
                <text>抢完了</text>
              </view>
            </block>
          </view>

          <view class="flex quantity-info">剩{{promotion.realtime_quantity}}份</view>
        </view>
      </view>
    </block>
  </view>
</view>
