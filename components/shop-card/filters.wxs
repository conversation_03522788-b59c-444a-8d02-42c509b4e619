// 格式化函数 WXS 模块

// 格式化距离
function formatDistance(distance) {
  if (!distance) return '';
  var dist = parseFloat(distance);
  if (dist > 1) {
    return dist.toFixed(2) + 'km';
  } else {
    return (dist * 1000).toFixed(0) + 'm';
  }
}

// 格式化平台
function formatPlatform(platform) {
  var platformMap = {
    'dianping': '大众点评',
    'xiaohongshu': '小红书',
    'meituan': '美团'
  };
  return platformMap[platform] || platform;
}

// 格式化要求
function formatRequirements(requires, level, followers_count) {
  if (requires === '1') {
    return '等级' + level + '级以上';
  } else if (requires === '2') {
    return '粉丝' + followers_count + '+';
  } else if (requires === '3') {
    return '等级' + level + '级+粉丝' + followers_count + '+';
  }
  return '';
}

// 格式化价格
function toFixed(value) {
  return parseFloat(value).toFixed(2);
}

module.exports = {
  formatDistance: formatDistance,
  formatPlatform: formatPlatform,
  formatRequirements: formatRequirements,
  toFixed: toFixed
};
