import { TabMenu1, TabMenu2 } from './data';
Component({
  data: {
    active: 0,
    list: [],
  },

  methods: {
    onChange(event) {
      wx.vibrateShort({
        type: 'medium', // light, medium, heavy
      })
      this.setData({ active: event.detail.value });
      wx.switchTab({
        url: this.data.list[event.detail.value].url.startsWith('/')
          ? this.data.list[event.detail.value].url
          : `/${this.data.list[event.detail.value].url}`,
      });
    },

    init() {
      console.log("初始化TabBar");
      const { miniProgram } = wx.getAccountInfoSync();
      console.log(miniProgram);
      if (miniProgram.envVersion === "develop") {
        this.setData({ list: TabMenu1 });
      } else {
        this.setData({ list: TabMenu2 });
      }

      const page = getCurrentPages().pop();
      const route = page ? page.route.split('?')[0] : '';
      const active = this.data.list.findIndex(
        (item) =>
          (item.url.startsWith('/') ? item.url.substr(1) : item.url) ===
          `${route}`,
      );
      this.setData({ active });
    },
  },
});
