<t-tab-bar value="{{active}}" bindchange="onChange" fixed="{{true}}" split="{{false}}" theme="tag">
	<t-tab-bar-item wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index">
		<view class="custom-tab-bar-wrapper">
			<t-icon name="{{item.icon}}" size="48rpx" wx:if="{{item.icon === 'chat-message'}}" />
			<t-icon prefix="wr" name="{{item.icon || item.iconUrl}}" size="48rpx" wx:else />
			<view class="text">{{ item.text }}</view>
		</view>
	</t-tab-bar-item>
</t-tab-bar>