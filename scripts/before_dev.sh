#! /bin/bash

set -exuo pipefail

# fd wxml | xargs --exec sed -i '' 's/{{miniProgram\.version}}/{{miniProgram.version}}/g'
# fd wxml | xargs -n 1 sed -i '' 's/{{miniProgram\.version}}/{{!miniProgram.version}}/g' {} \;

echo -n "miniProgram.version count: "
fd -e wxml --type f | xargs grep 'miniProgram.version' | wc -l
fd -e wxml --type f --exec sed -i '' 's/miniProgram\.version/!miniProgram.version/g' {} \;
echo "after sed"
echo -n "!miniProgram.version count: "
fd -e wxml --type f | xargs grep '!miniProgram.version' | wc -l
# fd -e wxml --type f --exec sed -i '' 's/{{ miniProgram\.version &&/{{ !miniProgram.version &&/g' {} \;
