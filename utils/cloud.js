// utils/cloud.js
export const cloud = {
	envID: "cloud1-0gpy573m8caa7db3",

	// 转换为 CDN 链接（需文件公开）
	toCdnUrl: (fileID) => {
		const [prefix, ...paths] = fileID
			.replace(`cloud://${this.envID}.`, "")
			.split("/");
		console.debug("prefix", prefix);
		return `https://${prefix}.tcb.qcloud.la/${paths.join("/")}`;
	},

	// 获取临时链接（兼容私有文件）
	getTempURL: async (fileID) => {
		wx.cloud.init({
			env: "cloud1-0gpy573m8caa7db3", // 指定云开发环境 ID
			traceUser: true, // 是否记录用户访问
		});
		const res = await wx.cloud.getTempFileURL({ fileList: [fileID] });
		return res.fileList[0].tempFileURL;
	},
};

// envId
// cloud1-0gpy573m8caa7db3

// // 使用示例
// import { cloud } from './utils/cloud.js';

// // 公开文件直接转换
// const cdnUrl = cloud.toCdnUrl('cloud://prod-123456/images/example.jpg');

// // 私有文件获取临时链接
// const tempUrl = await cloud.getTempURL('cloud://prod-123456/private/image.jpg');
