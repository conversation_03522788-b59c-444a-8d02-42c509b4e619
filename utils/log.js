// 实时日志
// https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/extended/log/

const log = wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : null;

export const logger = {
	debug(...args) {
		if (!log) return;
		log.debug(...args);
	},
	info(...args) {
		if (!log) return;
		log.info(...args);
	},
	warn(...args) {
		if (!log) return;
		log.warn(...args);
	},
	error(...args) {
		if (!log) return;
		log.error(...args);
	},
	setFilterMsg(msg) {
		// 从基础库2.7.3开始支持
		if (!log || !log.setFilterMsg) return;
		if (typeof msg !== "string") return;
		log.setFilterMsg(msg);
	},
	addFilterMsg(msg) {
		// 从基础库2.8.1开始支持
		if (!log || !log.addFilterMsg) return;
		if (typeof msg !== "string") return;
		log.addFilterMsg(msg);
	},
};

export { log };
