import dayjs from "dayjs";

const formatTime = (date, template) => dayjs(date).format(template);

/**
 * 格式化价格数额为字符串
 * 可对小数部分进行填充，默认不填充
 * @param price 价格数额，以分为单位!
 * @param fill 是否填充小数部分 0-不填充 1-填充第一位小数 2-填充两位小数
 */
function priceFormat(price, fill = 0) {
	if (isNaN(price) || price === null || price === Infinity) {
		return price;
	}

	let priceFormatValue = Math.round(parseFloat(`${price}`) * 10 ** 8) / 10 ** 8; // 恢复精度丢失
	priceFormatValue = `${Math.ceil(priceFormatValue) / 100}`; // 向上取整，单位转换为元，转换为字符串
	if (fill > 0) {
		// 补充小数位数
		if (priceFormatValue.indexOf(".") === -1) {
			priceFormatValue = `${priceFormatValue}.`;
		}
		const n = fill - priceFormatValue.split(".")[1]?.length;
		for (let i = 0; i < n; i++) {
			priceFormatValue = `${priceFormatValue}0`;
		}
	}
	return priceFormatValue;
}

/**
 * 获取cdn裁剪后链接
 *
 * @param {string} url 基础链接
 * @param {number} width 宽度，单位px
 * @param {number} [height] 可选，高度，不填时与width同值
 */
const cosThumb = (url, width, height = width) => {
	if (url.indexOf("?") > -1) {
		return url;
	}

	if (url.indexOf("http://") === 0) {
		url = url.replace("http://", "https://");
	}

	return `${url}?imageMogr2/thumbnail/${~~width}x${~~height}`;
};

const get = (source, paths, defaultValue) => {
	if (typeof paths === "string") {
		paths = paths
			.replace(/\[/g, ".")
			.replace(/\]/g, "")
			.split(".")
			.filter(Boolean);
	}
	const { length } = paths;
	let index = 0;
	while (source != null && index < length) {
		source = source[paths[index++]];
	}
	return source === undefined || index === 0 ? defaultValue : source;
};
let systemWidth = 0;
/** 获取系统宽度，为了减少启动消耗所以在函数里边做初始化 */
export const loadSystemWidth = () => {
	if (systemWidth) {
		return systemWidth;
	}

	try {
		({ screenWidth: systemWidth, pixelRatio } = wx.getWindowInfo());
	} catch (e) {
		systemWidth = 0;
	}
	return systemWidth;
};

/**
 * 转换rpx为px
 *
 * @description
 * 什么时候用？
 * - 布局(width: 172rpx)已经写好, 某些组件只接受px作为style或者prop指定
 *
 */
const rpx2px = (rpx, round = false) => {
	loadSystemWidth();

	// px / systemWidth = rpx / 750
	const result = (rpx * systemWidth) / 750;

	if (round) {
		return Math.floor(result);
	}

	return result;
};

/**
 * 手机号码*加密函数
 * @param {string} phone 电话号
 * @returns
 */
const phoneEncryption = (phone) => {
	return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
};

// 内置手机号正则字符串
const innerPhoneReg =
	"^1(?:3\\d|4[4-9]|5[0-35-9]|6[67]|7[0-8]|8\\d|9\\d)\\d{8}$";

/**
 * 手机号正则校验
 * @param phone 手机号
 * @param phoneReg 正则字符串
 * @returns true - 校验通过 false - 校验失败
 */
const phoneRegCheck = (phone) => {
	const phoneRegExp = new RegExp(innerPhoneReg);
	return phoneRegExp.test(phone);
};

const generateRandomFileName = () => {
	const timestamp = Date.now(); // 获取当前时间戳
	const randomString = Math.random().toString(36).substring(2, 8); // 生成随机字符串
	return `${timestamp}_${randomString}`;
};

const getFileExtension = (filename) => {
	// 获取最后一个 '.' 的位置
	const lastDotIndex = filename.lastIndexOf(".");

	// 如果存在 '.' 并且不在开头或结尾
	if (lastDotIndex > 0 && lastDotIndex < filename.length - 1) {
		return filename.substring(lastDotIndex + 1); // 返回后缀名
	}

	return ""; // 如果没有后缀名，返回空字符串
};

const formatPlatform = (name) => {
	if (!name) return "";
	switch (name) {
		case "xiaohongshu":
			return "小红书";
		case "dianping":
			return "大众点评";
		default:
			return name;
	}
};

const formatDateTime = function (timestamp) {
	if (timestamp) {
		return dayjs(timestamp).format("YYYY-MM-DD HH:mm:ss");
		// var date = getDate(timestamp);
		// return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}`;
	} else {
		return "";
	}
};

const formatOrderType = function (order_type) {
	switch (order_type) {
		case "tandian1":
			return "探店订单（下单的次日内要核销）";
		case "tandian2":
			return "探店订单预约型（只要不下架就不过期）";
		case "ykj":
			return "特价团订单 (无需笔记)";
		default:
			return "";
	}
};

const formatRequirements = (requires, level, followers_count) => {
	console.log(requires, level, followers_count);
	switch (requires) {
		case "1":
			return `等级≥${level}`;
		case "2":
			return `粉丝数≥${followers_count}`;
		case "3":
			return `等级≥${level}级且粉丝数≥${followers_count}`;
		default:
			return "";
	}
};

// 计算返佣比例 (同步cloudfunction: calc_commission_rate)
// (购买方式：自购或分享、 和用户等级) 具体要看小程序上的团长分销规则页面
// 参数解释:
// method:  m1（自购） m2（分享购买即直卖）
// user_level: 用户等级 (自购看下单人的等级、直卖看推荐人的等级)
const calcCommissionRate = (user_level) => {
	let rate = 0;
	switch (user_level) {
		case 5:
			rate = 1;
			break;
		case 4:
			rate = 0.9;
			break;
		case 3:
			rate = 0.8;
			break;
		case 2:
			rate = 0.7;
			break;
		case 1:
			rate = 0.5;
			break;
		case 0:
			rate = 0.3;
			break;
		default:
			rate = 0.3;
			break;
	}
	return rate;
};

const estimateProfit = (price_spread, user_level) => {
	const commission_rate = calcCommissionRate(user_level);
	return Math.round(price_spread * commission_rate * 100) / 100;
};

module.exports = {
	formatTime,
	priceFormat,
	cosThumb,
	get,
	rpx2px,
	phoneEncryption,
	phoneRegCheck,
	generateRandomFileName,
	getFileExtension,
	formatRequirements,
	calcCommissionRate,
	estimateProfit,
	formatPlatform,
	formatDateTime,
	formatOrderType,
};
