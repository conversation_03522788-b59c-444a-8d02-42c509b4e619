/**
 * 调试辅助工具
 * 提供快速访问测试页面和调试功能的方法
 */

class DebugHelper {
  constructor() {
    this.isDebugMode = this.checkDebugMode();
  }

  /**
   * 检查是否为调试模式
   */
  checkDebugMode() {
    try {
      const accountInfo = wx.getAccountInfoSync();
      // 开发版和体验版启用调试模式
      return !accountInfo.miniProgram.version || accountInfo.miniProgram.version === 'develop';
    } catch (error) {
      console.warn('获取账号信息失败:', error);
      return false;
    }
  }

  /**
   * 快速跳转到调试菜单
   */
  openDebugMenu() {
    if (!this.isDebugMode) {
      console.warn('当前不是调试模式，无法访问调试菜单');
      return;
    }

    wx.navigateTo({
      url: '/test/debug-menu/index',
      fail: (error) => {
        console.error('打开调试菜单失败:', error);
        wx.showToast({
          title: '调试菜单不可用',
          icon: 'none'
        });
      }
    });
  }

  /**
   * 快速跳转到组件测试页面
   */
  openComponentTest(componentName = 'shop-card') {
    if (!this.isDebugMode) {
      console.warn('当前不是调试模式，无法访问组件测试');
      return;
    }

    const testPages = {
      'shop-card': '/test/test-shop-card/index'
    };

    const url = testPages[componentName];
    if (!url) {
      console.error('未找到组件测试页面:', componentName);
      return;
    }

    wx.navigateTo({
      url: url,
      fail: (error) => {
        console.error('打开组件测试失败:', error);
        wx.showToast({
          title: '测试页面不可用',
          icon: 'none'
        });
      }
    });
  }

  /**
   * 显示调试信息
   */
  showDebugInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      const accountInfo = wx.getAccountInfoSync();
      
      const info = {
        '小程序版本': accountInfo.miniProgram.version || '开发版',
        '基础库版本': systemInfo.SDKVersion,
        '设备型号': systemInfo.model,
        '系统版本': systemInfo.system,
        '微信版本': systemInfo.version,
        '屏幕尺寸': `${systemInfo.screenWidth}x${systemInfo.screenHeight}`,
        '像素比': systemInfo.pixelRatio,
        '调试模式': this.isDebugMode ? '是' : '否'
      };

      const content = Object.entries(info)
        .map(([key, value]) => `${key}: ${value}`)
        .join('\n');

      wx.showModal({
        title: '调试信息',
        content: content,
        showCancel: false,
        confirmText: '复制',
        success: (res) => {
          if (res.confirm) {
            wx.setClipboardData({
              data: content,
              success: () => {
                wx.showToast({
                  title: '已复制到剪贴板',
                  icon: 'success'
                });
              }
            });
          }
        }
      });
    } catch (error) {
      console.error('获取调试信息失败:', error);
      wx.showToast({
        title: '获取信息失败',
        icon: 'error'
      });
    }
  }

  /**
   * 清除所有本地存储
   */
  clearAllStorage() {
    if (!this.isDebugMode) {
      console.warn('当前不是调试模式，无法清除存储');
      return;
    }

    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有本地存储数据吗？这将清除登录状态、位置信息等。',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.clearStorageSync();
            wx.showToast({
              title: '清除成功',
              icon: 'success'
            });
          } catch (error) {
            console.error('清除存储失败:', error);
            wx.showToast({
              title: '清除失败',
              icon: 'error'
            });
          }
        }
      }
    });
  }

  /**
   * 添加调试按钮到页面（仅调试模式）
   * 在页面的 onLoad 中调用此方法
   */
  addDebugButton(page) {
    if (!this.isDebugMode) {
      return;
    }

    // 为页面添加调试相关的方法
    page.openDebugMenu = () => this.openDebugMenu();
    page.showDebugInfo = () => this.showDebugInfo();
    page.clearStorage = () => this.clearAllStorage();

    console.log('调试模式已启用，可使用以下方法:');
    console.log('- this.openDebugMenu() // 打开调试菜单');
    console.log('- this.showDebugInfo() // 显示调试信息');
    console.log('- this.clearStorage() // 清除本地存储');
  }

  /**
   * 监听特殊手势（如连续点击）来打开调试菜单
   */
  setupGestureListener(element, clickCount = 5) {
    if (!this.isDebugMode) {
      return;
    }

    let clicks = 0;
    let timer = null;

    const handleClick = () => {
      clicks++;
      
      if (timer) {
        clearTimeout(timer);
      }

      timer = setTimeout(() => {
        clicks = 0;
      }, 2000); // 2秒内的点击才算连续

      if (clicks >= clickCount) {
        clicks = 0;
        clearTimeout(timer);
        this.openDebugMenu();
      }
    };

    return handleClick;
  }

  /**
   * 记录调试日志
   */
  log(message, data = null) {
    if (!this.isDebugMode) {
      return;
    }

    const timestamp = new Date().toLocaleTimeString();
    console.log(`[DEBUG ${timestamp}] ${message}`, data || '');
  }

  /**
   * 性能监控
   */
  startPerformanceMonitor(name) {
    if (!this.isDebugMode) {
      return () => {};
    }

    const startTime = Date.now();
    console.log(`[PERF] ${name} 开始`);

    return () => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      console.log(`[PERF] ${name} 完成，耗时: ${duration}ms`);
    };
  }
}

// 创建全局实例
const debugHelper = new DebugHelper();

// 在全局对象上暴露调试方法（仅调试模式）
if (debugHelper.isDebugMode) {
  // 可以在控制台直接调用这些方法
  global.debug = debugHelper;
  global.openDebugMenu = () => debugHelper.openDebugMenu();
  global.showDebugInfo = () => debugHelper.showDebugInfo();
  global.clearStorage = () => debugHelper.clearAllStorage();
}

module.exports = debugHelper;
