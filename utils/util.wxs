// utils/filter.wxs
var filter = {
	// 示例1:金额保留两位小数
	numberToFixed: function (value) {
		return value.toFixed(2);
	},

	// 示例2:日期格式化
	formatDate: function (timestamp) {
		if (timestamp) {
			var date = getDate(timestamp);
			return (
				date.getFullYear() + "年" + (date.getMonth() + 1) + "月" + date.getDate() + "日"
			);
		} else {
			return null;
		}
	},

	// 示例2:日期格式化
	formatDateTime: function (timestamp) {
		if (timestamp) {
			var date = getDate(timestamp);
			return (
				date.getFullYear() +
				"-" +
				(date.getMonth() + 1) +
				"-" +
				date.getDate() +
				" " +
				date.getHours() +
				":" +
				date.getMinutes() +
				":" +
				date.getSeconds()
			);
		} else {
			return "";
		}
	},

	formatPlatform: function (name) {
		if (!name) return "";
		switch (name) {
			case "xiaohongshu":
				return "小红书";
			case "dianping":
				return "大众点评";
			default:
				return name;
		}
	},

	// 对应的另一个平台
	formatReversePlatform: function (name) {
		if (!name) return "";
		switch (name) {
			case "xiaohongshu":
				return "大众点评";
			case "dianping":
				return "小红书";
			default:
				return name;
		}
	},

	formatRequirements: function (requires, level, followers_count) {
		console.log(requires, level, followers_count);
		switch (requires) {
			case "1":
				return "等级≥" + level;
			case "2":
				return "粉丝数≥" + followers_count;
			case "3":
				return "等级≥" + level + "级" + "且粉丝数≥" + followers_count;
			default:
				return "";
		}
	},

	formatCardStatus: function (status) {
		switch (status) {
			case 0:
				return "审核中";
			case 1:
				return "审核通过";
			case 2:
				return "驳回";
			case 3:
				return "审核中";
			default:
				return "审核中";
		}
	},

	formatOrderStatus: function (status) {
		switch (status) {
			case "0":
				return "待支付";
			case "10":
				return "待核销";
			case "11":
				return "已过期";
			case "20":
				return "已核销"; // 即已核销
			case "30":
				return "审核中";
			case "31":
				return "审核通过";
			case "32":
				return "笔记被驳回";
			case "40":
				return "已取消";
			case "81":
				return "支付时取消付款";
			default:
				return "未知";
		}
	},

	formatDistance: function (distance) {
		if (distance > 1) {
			return distance.toFixed(2) + "km";
		} else {
			return (distance * 100).toFixed(0) + "m";
		}
	},

	formatDistanceInMeter: function (distance) {
		if (distance > 1) {
			return (distance / 1000.0).toFixed(2) + "km";
		} else {
			return distance.toFixed(0) + "m";
		}
	},

	formatDiscount: function (x, y) {
		return Number(x - y).toFixed(2);
	},

	formatTransactionType: function (type) {
		switch (type) {
			case "reward":
				return "订单笔记审核成功返现";
			case "withdraw":
				return "用户申请提现";
			case "referral":
				return "邀请好友首单奖励";
			case "referral_order_commission":
				return "直卖订单佣金";
			case "self_order_commission":
				return "自购订单佣金";
			case "team_commission":
				return "订单分销产生的团队佣金";
			default:
				return type;
		}
	},

	maskPhoneNumber: function (phone) {
		if (phone) {
			var masked = phone.substring(0, 3) + "****" + phone.substring(7);
			console.log(masked); // 输出: 138****5678
			return masked;
		}
		return "";
	},

	formatPromotionTypeExpirationDesc: function (promotion_type) {
		switch (promotion_type) {
			case "tandian1":
				return "下单后请在次日内完成核销";
			case "tandian2":
				return "下单后预约探店时间，只要不下线就不过期";
			case "ykj":
				return "下单后30天有效，过期作废";
			default:
				return "未知";
		}
	},

	toFixed: function(x) {
		return Math.round(x * 100) / 100
	}
};

// 导出模块
module.exports = {
	numberToFixed: filter.numberToFixed,
	formatDate: filter.formatDate,
	formatDateTime: filter.formatDateTime,
	formatPlatform: filter.formatPlatform,
	formatReversePlatform: filter.formatReversePlatform,
	formatRequirements: filter.formatRequirements,
	formatCardStatus: filter.formatCardStatus,
	formatOrderStatus: filter.formatOrderStatus,
	formatDistance: filter.formatDistance,
	formatDistanceInMeter: filter.formatDistanceInMeter,
	formatDiscount: filter.formatDiscount,
	formatTransactionType: filter.formatTransactionType,
	maskPhoneNumber: filter.maskPhoneNumber,
	toFixed: filter.toFixed,
	formatPromotionTypeExpirationDesc: filter.formatPromotionTypeExpirationDesc,
};
