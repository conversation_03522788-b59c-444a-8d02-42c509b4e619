/**
 * 组件调试工具
 * 用于检查和修复组件常见问题
 */

class ComponentDebugger {
  constructor() {
    this.errors = [];
    this.warnings = [];
  }

  /**
   * 检查店铺数据结构
   */
  validateShopData(shop) {
    this.errors = [];
    this.warnings = [];

    if (!shop) {
      this.errors.push("店铺数据为空");
      return false;
    }

    // 检查必需字段
    const requiredFields = ['_id', 'name'];
    requiredFields.forEach(field => {
      if (!shop[field]) {
        this.errors.push(`缺少必需字段: ${field}`);
      }
    });

    // 检查促销数据
    if (!shop.promotions) {
      this.warnings.push("没有促销数据，组件不会显示");
    } else if (!Array.isArray(shop.promotions)) {
      this.errors.push("促销数据不是数组");
    } else if (shop.promotions.length === 0) {
      this.warnings.push("促销数组为空，组件不会显示");
    } else {
      // 检查促销数据结构
      shop.promotions.forEach((promo, index) => {
        if (!promo._id) {
          this.warnings.push(`促销 ${index} 缺少 _id`);
        }
        if (!promo.name) {
          this.warnings.push(`促销 ${index} 缺少 name`);
        }
        if (!promo.product_id || !promo.product_id.images) {
          this.warnings.push(`促销 ${index} 缺少商品图片`);
        }
      });
    }

    return this.errors.length === 0;
  }

  /**
   * 生成测试数据
   */
  generateTestData() {
    return {
      _id: "debug_shop_" + Date.now(),
      name: "调试测试店铺",
      tag: "测试",
      district: "测试区",
      distance: "0.5",
      logo: "https://via.placeholder.com/60x60",
      promotions: [
        {
          _id: "debug_promo_" + Date.now(),
          name: "调试测试商品",
          current_price: "15.0",
          original_price: "30.0",
          realtime_quantity: 10,
          platform: "dianping",
          requires: "1",
          level: 1,
          followers_count: 100,
          tag: "测试",
          product_id: {
            _id: "debug_product_" + Date.now(),
            images: ["https://via.placeholder.com/180x180"]
          }
        }
      ]
    };
  }

  /**
   * 检查组件配置
   */
  checkComponentConfig(page) {
    const issues = [];

    try {
      // 检查页面是否正确引用了组件
      if (!page.selectComponent) {
        issues.push("页面不支持组件选择器");
        return issues;
      }

      // 尝试选择组件
      const component = page.selectComponent('shop-card');
      if (!component) {
        issues.push("未找到 shop-card 组件，请检查组件引用配置");
      } else {
        console.log("组件引用正常");
      }
    } catch (error) {
      issues.push("检查组件配置时出错: " + error.message);
    }

    return issues;
  }

  /**
   * 显示调试报告
   */
  showDebugReport(shop) {
    const isValid = this.validateShopData(shop);
    
    let report = "=== 组件调试报告 ===\n";
    report += `数据验证: ${isValid ? "通过" : "失败"}\n`;
    
    if (this.errors.length > 0) {
      report += "\n错误:\n";
      this.errors.forEach(error => {
        report += `- ${error}\n`;
      });
    }
    
    if (this.warnings.length > 0) {
      report += "\n警告:\n";
      this.warnings.forEach(warning => {
        report += `- ${warning}\n`;
      });
    }

    if (shop && shop.promotions) {
      report += `\n促销数量: ${shop.promotions.length}\n`;
    }

    console.log(report);
    
    // 在小程序中显示报告
    if (typeof wx !== 'undefined') {
      wx.showModal({
        title: '调试报告',
        content: report,
        showCancel: false,
        confirmText: '复制报告',
        success: (res) => {
          if (res.confirm) {
            wx.setClipboardData({
              data: report,
              success: () => {
                wx.showToast({
                  title: '报告已复制',
                  icon: 'success'
                });
              }
            });
          }
        }
      });
    }

    return report;
  }

  /**
   * 快速修复数据
   */
  quickFix(shop) {
    if (!shop) {
      return this.generateTestData();
    }

    const fixed = { ...shop };

    // 修复缺失的必需字段
    if (!fixed._id) {
      fixed._id = "fixed_" + Date.now();
    }
    if (!fixed.name) {
      fixed.name = "修复的店铺";
    }

    // 修复促销数据
    if (!fixed.promotions || !Array.isArray(fixed.promotions)) {
      fixed.promotions = [];
    }

    // 如果没有促销数据，添加一个测试促销
    if (fixed.promotions.length === 0) {
      fixed.promotions.push({
        _id: "fixed_promo_" + Date.now(),
        name: "修复的商品",
        current_price: "10.0",
        original_price: "20.0",
        realtime_quantity: 5,
        platform: "dianping",
        requires: "1",
        level: 1,
        followers_count: 100,
        product_id: {
          _id: "fixed_product_" + Date.now(),
          images: ["https://via.placeholder.com/180x180"]
        }
      });
    }

    return fixed;
  }

  /**
   * 为页面添加调试方法
   */
  addDebugMethods(page) {
    const debuggerInstance = this;

    // 添加调试方法到页面
    page.debugShopCard = function(shopData) {
      const shop = shopData || this.data.shop || this.data.testShop1;
      return debuggerInstance.showDebugReport(shop);
    };

    page.fixShopData = function(shopData) {
      const shop = shopData || this.data.shop || this.data.testShop1;
      const fixed = debuggerInstance.quickFix(shop);
      
      // 更新页面数据
      this.setData({
        shop: fixed,
        testShop1: fixed
      });
      
      wx.showToast({
        title: '数据已修复',
        icon: 'success'
      });
      
      return fixed;
    };

    page.generateTestShop = function() {
      const testData = debuggerInstance.generateTestData();
      this.setData({
        shop: testData,
        testShop1: testData
      });
      
      wx.showToast({
        title: '测试数据已生成',
        icon: 'success'
      });
      
      return testData;
    };

    console.log("调试方法已添加到页面:");
    console.log("- this.debugShopCard() // 显示调试报告");
    console.log("- this.fixShopData() // 修复数据问题");
    console.log("- this.generateTestShop() // 生成测试数据");
  }

  /**
   * 检查运行环境
   */
  checkEnvironment() {
    const env = {
      hasWx: typeof wx !== 'undefined',
      hasConsole: typeof console !== 'undefined',
      platform: 'unknown'
    };

    if (env.hasWx) {
      try {
        const systemInfo = wx.getSystemInfoSync();
        env.platform = systemInfo.platform;
        env.version = systemInfo.version;
        env.SDKVersion = systemInfo.SDKVersion;
      } catch (error) {
        console.warn("获取系统信息失败:", error);
      }
    }

    return env;
  }
}

// 创建全局实例
const componentDebugger = new ComponentDebugger();

// 在全局对象上暴露调试方法
if (typeof global !== 'undefined') {
  global.componentDebugger = componentDebugger;
}

module.exports = componentDebugger;
