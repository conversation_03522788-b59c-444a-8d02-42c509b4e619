-- 更新所有用户的积分
UPDATE users u
SET total_points = (
    SELECT 
        SUM(CASE WHEN type = 'in' THEN amount ELSE 0 END) - 
        SUM(CASE WHEN type = 'out' THEN amount ELSE 0 END)
    FROM 
        points p
    WHERE 
        p.user_id = u._id
)
WHERE EXISTS (
    SELECT 1 FROM points p WHERE p.user_id = u._id
);

SELECT _id, nickname, total_points from users order by total_points desc;



-- 更新单个用户的积分
UPDATE users 
    SET total_points = (
        SELECT SUM(CASE WHEN type = 'in' THEN amount ELSE 0 END) - 
               SUM(CASE WHEN type = 'out' THEN amount ELSE 0 END)
        FROM points
        WHERE user_id = 'B8Z98RCE1N'
    )
WHERE _id = 'B8Z98RCE1N';



SELECT _id, nickname, total_points from users where _id = 'B8Z98RCE1N'

UPDATE users set total_points = 100000 where _id = 'B8Z98RCE1N';