# ProductStore 优化说明

## 问题描述

原来的 `getNearestShops` 方法存在以下问题：
1. 所有不同 `business_type_tags` 的店铺数据都存储在同一个 `shops` 数组中
2. 不同业务类型的数据会相互混乱和覆盖
3. 无法独立管理不同业务类型的加载状态和分页信息

## 优化方案

### 1. 新增数据结构

在 `productStore` 中新增了 `shopsByBusinessType` 对象，为每种业务类型创建独立的数据存储：

```javascript
shopsByBusinessType: {
  tandian1: {
    shops: [],
    loadStatus: 0,
    pageNumber: 1,
    total: 0
  },
  tandian2: {
    shops: [],
    loadStatus: 0,
    pageNumber: 1,
    total: 0
  },
  ykj: {
    shops: [],
    loadStatus: 0,
    pageNumber: 1,
    total: 0
  }
}
```

### 2. 优化 `getNearestShops` 方法

- **独立数据存储**: 每种业务类型的数据存储在对应的独立空间中
- **独立状态管理**: 每种业务类型有自己的加载状态、页码和总数
- **错误处理**: 增加了更完善的错误处理机制
- **业务类型标记**: 为每个店铺添加 `business_type` 字段标识其类型

### 3. 优化 `getPromotionsForShop` 方法

- 根据 `promotion_type` 更新对应业务类型的店铺促销信息
- 保持向后兼容性，同时更新原有的 `shops` 数组

### 4. 新增辅助方法

#### `getShopsByBusinessType(business_type_tags)`
获取指定业务类型的店铺数据

#### `getLoadStatusByBusinessType(business_type_tags)`
获取指定业务类型的加载状态

#### `resetShopsByBusinessType(business_type_tags)`
重置指定业务类型的数据

#### `resetAllShopsByBusinessType()`
重置所有业务类型的数据

## 使用方法

### 基本用法

```javascript
// 加载不同业务类型的店铺
await productStore.getNearestShops("0", "tandian1");
await productStore.getNearestShops("0", "tandian2");
await productStore.getNearestShops("0", "ykj");

// 获取对应的数据
const tandian1Shops = productStore.getShopsByBusinessType("tandian1");
const tandian2Shops = productStore.getShopsByBusinessType("tandian2");
const ykjShops = productStore.getShopsByBusinessType("ykj");
```

### 状态检查

```javascript
// 检查加载状态
const status = productStore.getLoadStatusByBusinessType("tandian1");
// 0: 初始状态, 1: 加载中, 2: 没有更多数据, 3: 加载失败
```

### 数据重置

```javascript
// 重置特定业务类型
productStore.resetShopsByBusinessType("tandian1");

// 重置所有业务类型
productStore.resetAllShopsByBusinessType();
```

## 向后兼容性

- 保留了原有的 `shops` 数组，确保现有代码不会出错
- `getPromotionsForShop` 方法同时更新新旧数据结构
- 原有的方法调用方式仍然有效

## 优势

1. **数据隔离**: 不同业务类型的数据完全独立，避免混乱
2. **状态独立**: 每种业务类型有独立的加载状态和分页信息
3. **易于维护**: 清晰的数据结构，便于调试和维护
4. **扩展性强**: 可以轻松添加新的业务类型
5. **向后兼容**: 不影响现有功能的使用

## 注意事项

1. 在使用新的方法时，需要明确指定 `business_type_tags` 参数
2. 建议逐步迁移到新的数据获取方式，以获得更好的数据隔离效果
3. 在切换分类或重置数据时，建议使用新的重置方法来确保数据一致性
