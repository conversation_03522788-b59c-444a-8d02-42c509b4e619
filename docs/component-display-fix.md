# Shop Card 组件显示问题修复指南

## 问题分析

组件没有在页面上显示的主要原因是：

1. **条件渲染**: 组件只在 `shop.promotions.length > 0` 时才显示
2. **数据结构**: 传入的数据可能不符合组件期望的结构
3. **WXS 函数**: 模板中的格式化函数需要通过 WXS 实现
4. **组件引用**: 组件路径或配置可能有问题

## 已实施的修复

### 1. WXS 格式化函数

创建了 `components/shop-card/filters.wxs` 文件：

```javascript
// 格式化距离
function formatDistance(distance) {
  if (!distance) return '';
  var dist = parseFloat(distance);
  if (dist > 1) {
    return dist.toFixed(2) + 'km';
  } else {
    return (dist * 1000).toFixed(0) + 'm';
  }
}

// 其他格式化函数...
module.exports = {
  formatDistance: formatDistance,
  formatPlatform: formatPlatform,
  formatRequirements: formatRequirements,
  toFixed: toFixed
};
```

### 2. 组件模板更新

在 `shop-card.wxml` 中引入 WXS：

```xml
<wxs src="./filters.wxs" module="filter" />
```

### 3. 数据验证修复

修复了模板中的数据引用错误：

```xml
<!-- 修复前 -->
<view wx:if="{{food.tag}}">

<!-- 修复后 -->
<view wx:if="{{promotion.tag}}">
```

### 4. 调试工具

创建了完整的调试工具链：

- `utils/component-debugger.js`: 组件调试工具
- `test/simple-test/`: 简单测试页面
- `components/shop-card/TROUBLESHOOTING.md`: 问题排查指南

## 使用修复后的组件

### 1. 基本使用

```xml
<shop-card 
  shop="{{shop}}"
  bind:tapstore="handleTapStore"
  bind:tapproduct="handleTapProduct">
</shop-card>
```

### 2. 确保数据结构正确

```javascript
const shop = {
  _id: "shop001",
  name: "店铺名称",
  tag: "店铺标签",
  district: "所在区域", 
  distance: "0.5",
  logo: "logo URL",
  promotions: [
    {
      _id: "promo001",
      name: "商品名称",
      current_price: "10.0",
      original_price: "20.0", 
      realtime_quantity: 5,
      platform: "dianping",
      requires: "1",
      level: 1,
      followers_count: 100,
      tag: "促销标签",
      product_id: {
        _id: "prod001",
        images: ["图片URL"]
      }
    }
  ]
};
```

### 3. 使用调试工具

```javascript
// 引入调试工具
const componentDebugger = require('/utils/component-debugger');

Page({
  onLoad() {
    // 添加调试方法
    componentDebugger.addDebugMethods(this);
    
    // 验证数据
    componentDebugger.validateShopData(this.data.shop);
  },
  
  // 可以在控制台调用这些方法
  // this.debugShopCard() - 显示调试报告
  // this.fixShopData() - 修复数据问题  
  // this.generateTestShop() - 生成测试数据
});
```

## 测试步骤

### 1. 访问简单测试页面

```
/test/simple-test/index
```

这个页面包含：
- 基础的组件显示测试
- 调试工具按钮
- 实时数据验证

### 2. 使用调试菜单

```
/test/debug-menu/index
```

提供统一的测试入口和开发工具。

### 3. 检查控制台输出

在开发者工具中查看：
- 数据验证结果
- 组件生命周期日志
- 错误和警告信息

## 常见问题解决

### 问题1: 组件不显示

**原因**: 促销数组为空
**解决**: 确保 `shop.promotions.length > 0`

```javascript
// 检查数据
if (!shop.promotions || shop.promotions.length === 0) {
  console.warn("店铺没有促销数据，组件不会显示");
}
```

### 问题2: 格式化函数不工作

**原因**: WXS 文件路径错误或语法问题
**解决**: 检查 `filters.wxs` 文件

```xml
<!-- 确保正确引入 WXS -->
<wxs src="./filters.wxs" module="filter" />
```

### 问题3: 图片不显示

**原因**: TDesign 组件未正确引入
**解决**: 检查组件配置

```json
{
  "usingComponents": {
    "t-image": "tdesign-miniprogram/image/image",
    "t-loading": "tdesign-miniprogram/loading/loading",
    "t-icon": "tdesign-miniprogram/icon/icon"
  }
}
```

### 问题4: 事件不触发

**原因**: 事件绑定错误
**解决**: 使用正确的事件绑定语法

```xml
<shop-card 
  bind:tapstore="handleTapStore"
  bind:tapproduct="handleTapProduct">
</shop-card>
```

## 性能优化建议

### 1. 数据预处理

在传入组件前预处理数据：

```javascript
// 预处理店铺数据
const processedShop = {
  ...shop,
  promotions: shop.promotions.filter(p => p.available)
};
```

### 2. 图片懒加载

组件已内置图片懒加载，确保图片URL有效。

### 3. 条件渲染优化

只在有数据时渲染组件：

```xml
<shop-card wx:if="{{shop.promotions.length > 0}}" shop="{{shop}}" />
```

## 后续维护

### 1. 定期测试

- 使用测试页面验证组件功能
- 检查不同数据情况下的表现
- 验证新功能的兼容性

### 2. 文档更新

- 保持使用文档的更新
- 记录新发现的问题和解决方案
- 更新测试用例

### 3. 性能监控

- 监控组件渲染性能
- 优化数据处理逻辑
- 减少不必要的重新渲染

## 总结

通过以上修复，Shop Card 组件现在应该能够正常显示。主要改进包括：

1. **WXS 格式化**: 解决了模板函数调用问题
2. **数据验证**: 提供了完整的数据验证工具
3. **调试支持**: 创建了全面的调试工具链
4. **测试页面**: 提供了多个测试入口
5. **文档完善**: 详细的问题排查指南

如果仍有问题，请使用调试工具进行详细检查，或参考故障排除文档。
