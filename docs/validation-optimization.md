# 验证逻辑优化方案

## 问题分析

原始代码中的问题：
1. `checkIfUserBanned` 函数没有返回值，无法控制后续流程
2. 验证逻辑分散在多个地方，难以维护
3. 大量重复的 `return` 语句，代码冗长

## 优化方案

### 方案1: 简单的布尔返回值

```javascript
// 修改前
export const checkIfUserBanned = (userInfo) => {
  if (userInfo.banned) {
    wx.showToast({
      title: "账号已被封禁",
      icon: "error",
      duration: 3000,
    });
    return; // 没有返回值，无法控制流程
  }
}

// 修改后
export const checkIfUserBanned = (userInfo) => {
  if (userInfo.banned) {
    wx.showToast({
      title: "账号已被封禁",
      icon: "error",
      duration: 3000,
    });
    return true; // 返回 true 表示被封禁
  }
  return false; // 返回 false 表示正常
}

// 使用方式
if (checkIfUserBanned(this.data.userInfo)) {
  return; // 被封禁，停止执行
}
```

### 方案2: 验证管道（推荐）

创建统一的验证系统，返回结构化的结果：

```javascript
// 验证结果结构
{
  isValid: boolean,     // 是否通过验证
  action?: string,      // 失败时的处理动作
  message?: string,     // 错误消息
  data?: any           // 额外数据
}

// 验证管道
export const validateOrder = (userInfo, promotion, cards) => {
  const validations = [
    () => checkIfUserBanned(userInfo),
    () => checkCoupons(userInfo),
    () => checkPhone(userInfo),
    () => checkStock(promotion),
    () => checkCards(cards, promotion),
  ];

  for (const validate of validations) {
    const result = validate();
    if (!result.isValid) {
      return result;
    }
  }

  return { isValid: true };
}
```

### 方案3: 异常处理模式

```javascript
class ValidationError extends Error {
  constructor(message, action) {
    super(message);
    this.action = action;
  }
}

export const checkIfUserBanned = (userInfo) => {
  if (userInfo.banned) {
    throw new ValidationError("账号已被封禁", "showToast");
  }
}

// 使用方式
try {
  checkIfUserBanned(this.data.userInfo);
  checkPhone(this.data.userInfo);
  // ... 其他验证
} catch (error) {
  if (error instanceof ValidationError) {
    this.handleValidationError(error);
    return;
  }
  throw error; // 重新抛出非验证错误
}
```

## 优化后的代码结构

### 1. 验证函数模块化

```javascript
// pages/promotion/tandian.js
export const checkIfUserBanned = (userInfo) => { /* ... */ }
export const checkPhone = (userInfo) => { /* ... */ }
export const checkCoupons = (userInfo) => { /* ... */ }
export const checkStock = (promotion) => { /* ... */ }
export const validateOrder = (userInfo, promotion, cards) => { /* ... */ }
```

### 2. 页面中的简化使用

```javascript
// pages/promotion/index.js
async buyItNow() {
  const { miniProgram } = wx.getAccountInfoSync();
  if (miniProgram.version) {
    // 统一验证
    const validationResult = validateOrder(
      this.data.userInfo,
      this.data.promotion,
      this.data.cards
    );

    if (!validationResult.isValid) {
      this.handleValidationResult(validationResult);
      return;
    }

    // 验证通过，继续业务逻辑
    this.setData({
      isOrderPopupShow: true,
    });
  }
}
```

### 3. 统一的结果处理

```javascript
handleValidationResult(result) {
  switch (result.action) {
    case 'showToast':
      wx.showToast({
        title: result.message,
        icon: "error",
        duration: 3000,
      });
      break;
    case 'showBindPhoneDialog':
      this.setData({ showBindPhoneDialog: true });
      break;
    case 'showBindDialog':
      this.setData({ showBindDialog: true });
      break;
    default:
      console.warn('未知的验证动作:', result.action);
  }
}
```

## 优势

1. **可维护性**: 验证逻辑集中管理，易于修改和扩展
2. **可复用性**: 验证函数可以在多个地方使用
3. **可测试性**: 每个验证函数都可以独立测试
4. **可读性**: 主业务逻辑更清晰，验证逻辑分离
5. **一致性**: 统一的错误处理和用户反馈

## 扩展建议

1. **添加异步验证支持**: 对于需要网络请求的验证
2. **添加验证缓存**: 避免重复验证
3. **添加验证日志**: 便于调试和监控
4. **添加自定义验证器**: 支持业务特定的验证逻辑

## 使用建议

- 对于简单项目，使用方案1（布尔返回值）
- 对于复杂项目，推荐使用方案2（验证管道）
- 对于需要复杂错误处理的项目，考虑方案3（异常处理）
