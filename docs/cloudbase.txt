Q1、sql模板发布了，可以执行sql了，那是不是可以用 START TRANSACTION; xxx; COMMIT 来执行事务了？
A1. 一个模版仅可 执行 1 条sql，仅支持dml：insert、replace、update、delete、select， 还不支持 START TRANSACTION; xxx; COMMIT 这种 

Q2、mysql 云数据库 sdk还未支持事务吗？需确认下
A2: 现在没有支持，是个长线需求，短期内无法满足

Q3、关于如何调用sql模板 https://docs.cloudbase.net/model/sql-template
@cloudbase/node-sdk 升级到3.10.0 可以调用sql模板了
@cloudbase/wx-cloud-client-sdk 升级到 1.6.1，就可以调用了，只是需要更改下初始化方式 initHTTPOverCallFunction


Q4. 云数据库开放客户端链接，预计在下半年





商户号和订单信息不匹配？
https://developers.weixin.qq.com/community/develop/doc/0004ee850c8ac809dabe1d83652800?highLine=1800008281
希望微信云开发支持服务商模式下的订单退款
https://developers.weixin.qq.com/community/develop/doc/0006c098bd066836a8ad260f95bc00?highLine=1800008281



如何二次支付cloudPay.unifiedOrder下的订单
https://developers.weixin.qq.com/community/develop/doc/0008ee19e70828cb9f338f92a66000?jumpto=reply&parent_commentid=000a6e21aac9a059bb338a5bc634&commentid=00004e708741f8fdc233546d96b8


还有用的mysql云数据库  cloudbase sdk 还没支持事务
我函数里同时操作多张表的情况下 提心吊胆的
还得专门写定时程序 去验证数据的完整性