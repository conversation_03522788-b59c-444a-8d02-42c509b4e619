# 首页分页加载问题排查指南

## 问题描述
首页往下滚动时没有加载到数据，一直显示加载中。

## 已修复的问题

### 1. 业务类型参数缺失
**问题**: `onReachBottom` 和其他调用 `loadNearestShops` 的地方没有传递 `business_type_tags` 参数。

**修复**: 
```javascript
// 修复前
await this.loadNearestShops(this.data.selectedCategory);

// 修复后
await this.loadNearestShops(this.data.selectedCategory, "tandian1");
```

### 2. 分页逻辑错误
**问题**: `productStore` 中检查是否还有更多数据时，使用的是 `this.shops.length` 而不是对应业务类型的数组长度。

**修复**:
```javascript
// 修复前
if (this.total != 0 && this.shops.length >= this.total) {

// 修复后
const currentShops = this[targetShopsKey] || [];
if (this.total != 0 && currentShops.length >= this.total) {
```

### 3. 触底检测逻辑优化
**问题**: 原来的分页计算逻辑可能不准确。

**修复**:
```javascript
// 修复前
if (this.data.page_size * (this.data.page_number - 1) >= this.data.total) {

// 修复后
if (this.data.total > 0 && this.data.shops_home.length >= this.data.total) {
```

## 调试步骤

### 1. 检查控制台日志
在首页滚动时，查看控制台输出：
```
onReachBottom
分页信息: { current_shops_count: X, page_number: Y, page_size: Z, total: W, shopsLoadStatus: S }
loadNearestShops 开始: { category_id: "0", business_type_tags: "tandian1", ... }
loadNearestShops 完成: { shops_count: X, total: W, shopsLoadStatus: S }
```

### 2. 使用调试页面
访问 `/pages/debug-pagination/index` 页面，可以：
- 查看当前分页状态
- 手动触发加载
- 模拟触底行为
- 重置数据

### 3. 检查数据绑定
确认页面正确绑定了以下字段：
```javascript
fields: [
  "shops_home",    // 店铺数据
  "page_number",   // 当前页码
  "page_size",     // 每页大小
  "total",         // 总数
  "shopsLoadStatus", // 加载状态
]
```

## 可能的其他问题

### 1. 网络请求失败
**检查**: 云函数调用是否成功
```javascript
// 在 productStore.js 中查看
console.debug(`${cloudfunctionName} response:`, response);
```

### 2. 数据格式问题
**检查**: 返回的数据格式是否正确
```javascript
if (response.result.code === 200) {
  // 检查 response.result.data 是否为数组
}
```

### 3. 加载状态卡住
**检查**: `shopsLoadStatus` 是否正确更新
- 0: 可以加载更多
- 1: 加载中
- 2: 没有更多数据
- 3: 加载失败

### 4. 分类切换问题
**检查**: 切换分类时是否正确重置了数据
```javascript
if (this.selectedCategory === category_id) {
  // 同一个分类，追加数据
} else {
  // 切换分类，重置数据
}
```

## 测试建议

### 1. 基础功能测试
1. 打开首页，检查是否加载了第一页数据
2. 向下滚动，检查是否触发 `onReachBottom`
3. 查看控制台日志，确认调用流程

### 2. 边界情况测试
1. 测试没有更多数据时的行为
2. 测试网络错误时的行为
3. 测试快速滚动时的行为

### 3. 数据一致性测试
1. 检查页面显示的数据量与 store 中的数据量是否一致
2. 检查分页参数是否正确递增
3. 检查总数是否正确

## 监控要点

### 1. 关键日志
- `onReachBottom` 触发
- `loadNearestShops` 调用参数
- 云函数响应结果
- 数据更新结果

### 2. 性能指标
- 加载时间
- 数据量
- 内存使用

### 3. 用户体验
- 加载状态显示
- 错误提示
- 空状态处理

## 如果问题仍然存在

1. **检查小程序基础库版本**: 确保支持相关 API
2. **检查云函数配置**: 确认云函数部署正常
3. **检查网络环境**: 测试不同网络条件下的表现
4. **清除缓存**: 重启开发工具，清除本地缓存
5. **使用真机测试**: 在真实设备上测试分页功能
