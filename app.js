// import { init } from "@cloudbase/wx-cloud-client-sdk";
import updateManager from "./common/updateManager";
import { authStore } from "./stores/authStore";
import { locationStore } from "./stores/locationStore";

import { log } from "./utils/log";
// let config = {};
// if (wx.getAccountInfoSync) {
// 	const accountInfo = wx.getAccountInfoSync();
// 	const env = accountInfo.miniProgram.envVersion || "release"; // 获取当前环境
// 	if (env === "develop") {
// 		config = require("./config/config.dev.js");
// 	} else if (env === "trial") {
// 		config = require("./config/config.test.js");
// 	} else {
// 		config = require("./config/config.prod.js");
// 	}
// }

// 指定云开发环境 ID
wx.cloud.init({
	env: "cloud1-0gpy573m8caa7db3", // 指定云开发环境 ID
	traceUser: true, // 是否记录用户访问
});

App({
	// config,
	globalData: {
		userInfo: null,
		location: null,
	},
	async onLaunch() {
		console.debug("App.js onLaunch");
		// // 小程序启动时初始化逻辑
		// console.log('小程序启动');
		// console.log("当前环境:", config.env);
		// console.log("API地址:", config.apiUrl);
		// console.log('globalData:', this.globalData);

		const { miniProgram } = wx.getAccountInfoSync();
		const env = miniProgram.envVersion; // 获取当前环境
		if (env !== "release") {
			wx.setStorageSync("dgx-location", {
				address: "湖北省武汉市江汉区江汉路地铁站地下1层",
				latitude: 30.579331,
				longitude: 114.290771,
				name: "武汉江汉路步行街",
				address_component: {
					nation: "中国",
					province: "湖北省",
					city: "武汉市",
					district: "黄陂区",
					street: "黄陂大道辅路",
					street_number: "",
				},
			});
		} else {
			log.debug("call locationStore.locate in app.js onLaunch");
			locationStore.locate(true);
		}

		// 初始化 store
		// store.init();

		// locationStore.chooseLocation();

		// 读取本地缓存并存储到 globalData
		// this.loadStorageData();
	},

	onShow(options) {
		console.debug("App.js onShow");
		updateManager();

		console.debug("options:", options);
		if (options.query?.referrer_id) {
			authStore.setReferrerId(options.query.referrer_id);
			wx.setStorageSync("dgx_referrer_id", options.query.referrer_id);
		}

		log.debug("call authStore.login in app.js onShow");
		authStore.login();
	},

	onHide() {
		console.debug("App.js onHide");
	},

	onError(msg) {
		console.error("App.js onError:", msg);
		log.error(msg);
	},
});

// note:
// onLaunch 不能用箭头函数，否则在函数体里取不到globalData
// https://developers.weixin.qq.com/miniprogram/dev/reference/api/App.html
