page {
  height: 100%;
  background: #f5f5f5;
}

.tabs-external__inner {
  height: 88rpx;
  width: 100%;
  line-height: 88rpx;
  z-index: 100;
  font-size: 26rpx;
  color: #333333;
  position: fixed;
  width: 100vw;
  top: 0;
  left: 0;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tabs-external__inner .tabs-external__track {
  background: #fa4126 !important;
  height: 4rpx !important;
  border-radius: 2rpx;
}

.tabs-external__inner .tabs-external__item {
  color: #666;
  transition: all 0.3s;
}

.tabs-external__inner .tabs-external__active {
  font-size: 28rpx;
  color: #fa4126 !important;
  font-weight: 500;
}

.tabs-external__inner.order-nav .order-nav-item .bottom-line {
  bottom: 12rpx;
}

.coupon-list-wrap {
  padding: 20rpx;
  margin-top: 18rpx;
}

.coupon-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 140rpx;
}

.coupon-list-item {
  width: 100%;
}

.coupon-card {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.coupon-card:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.coupon-left {
  width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  position: relative;
}

.coupon-left::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 1rpx;
  background: repeating-linear-gradient(
    transparent,
    transparent 10rpx,
    #eee 10rpx,
    #eee 20rpx
  );
}

.coupon-amount {
  display: flex;
  align-items: baseline;
}

.currency {
  font-size: 28rpx;
  margin-right: 4rpx;
}

.amount {
  font-size: 48rpx;
  font-weight: bold;
}

.coupon-condition {
  font-size: 24rpx;
  margin-top: 10rpx;
}

.coupon-right {
  flex: 1;
  padding: 30rpx;
  position: relative;
  border-left: 1px dashed #e0e0e0;
}

.coupon-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.coupon-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.coupon-tag {
  font-size: 22rpx;
  color: #fa4126;
  background: rgba(250, 65, 38, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.coupon-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.coupon-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #e0e0e0;
}

.coupon-time {
  font-size: 24rpx;
  color: #999;
}

.coupon-status {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 24rpx;
}

.coupon-status text {
  color: inherit;
}

.center-entry {
  box-sizing: content-box;
  border-top: 1rpx solid #dce0e4;
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

.center-entry-btn {
  color: #fa4126;
  font-size: 28rpx;
  text-align: center;
  line-height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100rpx;
}

.coupon-list-wrap .t-pull-down-refresh__bar {
  background: #f5f5f5 !important;
}

.t-class-indicator {
  color: #b9b9b9 !important;
}

.t-button--default {
  color: #666 !important;
}
/* 对话框样式 */
.t-dialog__btn--cancel {
  color: #666 !important;
}

.t-dialog__btn--confirm {
  color: #fa4126 !important;
}

.t-dialog__btn {
  font-size: 28rpx !important;
  font-weight: 500 !important;
}
