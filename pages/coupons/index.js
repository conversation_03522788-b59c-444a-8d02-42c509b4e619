import { fetchCouponList } from '../../services/coupon/index';
import dayjs from 'dayjs';
import { createStoreBindings } from "mobx-miniprogram-bindings";
import { authStore } from "../../stores/authStore";
import { userStore } from "../../stores/userStore";
import { Toast } from 'tdesign-miniprogram';

Page({
  data: {
    status: 0,
    list: [
      {
        text: '可使用',
        key: 0,
      },
      {
        text: '已使用',
        key: 1,
      }
    ],
    availableCoupon: {
      name: '探店券',
      description: "有探店券方可下单探店，每日三张，可以10积分兑换一张（没使用完隔日不叠加）",
      condition: 100,
      amount: 10,
      status: 'available',
      expireTime: dayjs().endOf('day').format('YYYY/MM/DD HH:mm:ss')
    },
    availableCoupons: [],
    usedCoupon: {
      name: '探店券',
      description: "有探店券方可下单探店，每日三张，可以10积分兑换一张（没使用完隔日不叠加）",
      condition: 100,
      amount: 10,
      status: 'used',
      expireTime: dayjs().endOf('day').format('YYYY/MM/DD HH:mm:ss')
    },
    usedCoupons: [],
    couponList: [],
    showExchangeDialog: false,
  },

  async onLoad() {
    this.authStoreBindings = createStoreBindings(this, {
      store: authStore,
      fields: ["userInfo"],
      actions: ["refreshUserInfo"],
    });
    this.userStoreBindings = createStoreBindings(this, {
      store: userStore,
      fields: [""],
      actions: ["redeemCouponsWithPoints"],
    });

    this.init();
  },

  onUnload() {
    this.authStoreBindings.destroyStoreBindings();
  },

  async init() {
    await this.refreshUserInfo();
    setTimeout(() => {
      console.debug("INIT", this.data.userInfo)
      const daily_coupons = this.data.userInfo.daily_coupons;
      const remain_coupons = this.data.userInfo.remain_coupons;
      const used_coupons = daily_coupons - remain_coupons;
      console.debug(daily_coupons, remain_coupons, used_coupons)
      let c1 = [], c2 = []
      for (let index = 0; index < remain_coupons; index++) {
        c1.push(this.data.availableCoupon)
      }
      for (let index = 0; index < used_coupons; index++) {
        c2.push(this.data.usedCoupon)
      }
      this.setData({
        availableCoupons: c1,
        usedCoupons: c2,
        couponList: c1,
      })
    })
  },

  tabChange(e) {
    const { value } = e.detail;
    console.debug("value", value)
    this.setData({ status: value });
    if (value === 0) {
      this.setData({
        couponList: this.data.availableCoupons
      })
    } else if (value === 1) {
      this.setData({
        couponList: this.data.usedCoupons
      })
    }
    // this.fetchList(value);
  },

  goCouponCenterHandle() {
    wx.showToast({ title: '去领券中心', icon: 'none' });
  },

  async onPullDownRefresh_() {
    this.setData({ enable: true });
    setTimeout(() => {
      this.setData({ enable: false });
    }, 1000);

    await this.init();
    // this.init(this.data.status);

    // this.setData(
    //   {
    //     couponList: [],
    //   },
    //   () => {
    //     this.init();
    //   },
    // );
  },

  async showExchangeDialog() {
    console.debug("兑换探店券")
    const res = await this.refreshUserInfo();
    console.error(res)
    if (res.total_points < 10) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: '积分不够，无法兑换',
      });
    } else {
      this.setData({
        showExchangeDialog: true
      });
    }
  },

  hideExchangeDialog() {
    this.setData({
      showExchangeDialog: false
    });
  },

  async handleExchange() {
    // 隐藏对话框
    this.hideExchangeDialog();

    // 显示加载中
    wx.showLoading({
      title: '兑换中...',
      mask: true
    });

    console.debug("兑换探店券")

    // const res = this.refreshUserInfo();
    // console.error(res)
    // return;

    const res = await this.redeemCouponsWithPoints();
    console.debug(res)

    const userInfo = wx.getStorageSync('dgx-auth');
    if (userInfo) {
      wx.cloud.callFunction({
        name: 'calc_user_points',
        data: {
          user_id: userInfo.user_id
        },
        success: res => {
          console.debug("calc_user_points:", res)
        }
      })
    }

    if (res) {
      wx.hideLoading();
      wx.showToast({
        title: '兑换成功',
        icon: 'success'
      });
      this.init();
    }
  },
});
