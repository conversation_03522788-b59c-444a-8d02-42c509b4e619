<t-tabs
  defaultValue="{{status}}"
  bind:change="tabChange"
  tabList="{{list}}"
  t-class="tabs-external__inner"
	t-class-item="tabs-external__item"
  t-class-active="tabs-external__active"
  t-class-track="tabs-external__track"
>
	<t-tab-panel
	  wx:for="{{list}}"
	  wx:for-index="index"
	  wx:for-item="tab"
	  wx:key="key"
	  label="{{tab.text}}"
	  value="{{tab.key}}"
	/>
</t-tabs>
<view class="coupon-list-wrap">
	<t-pull-down-refresh
		value="{{enable}}"
		loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}"
	  t-class-indicator="t-class-indicator"
	  id="t-pull-down-refresh"
	  bind:refresh="onPullDownRefresh_"
	  background="#f5f5f5"
	>
		<view class="coupon-list">
			<view class="coupon-list-item" wx:for="{{couponList}}" wx:key="_id">
				<view class="coupon-card">
					<view class="coupon-left">
						<t-icon name="discount" color="#fa4126" size="80rpx" />
					</view>
					<view class="coupon-right">
						<view class="coupon-header">
							<view class="coupon-name">{{item.name}}</view>
							<view class="coupon-tag" wx:if="{{item.type === 'new'}}">新人专享</view>
						</view>
						<view class="coupon-desc">{{item.description}}</view>
						<view class="coupon-footer">
							<view class="coupon-time">有效期至：{{item.expireTime}}</view>
							<!-- <view class="coupon-status" wx:if="{{item.status === 'used'}}">
								<t-icon name="check-circle" color="#999" size="32rpx" />
								<text>已使用</text>
							</view>
							<view class="coupon-status" wx:elif="{{item.status === 'expired'}}">
								<t-icon name="close-circle" color="#999" size="32rpx" />
								<text>已过期</text>
							</view>
							<view class="coupon-status" wx:else>
								<t-icon name="check-circle" color="#fa4126" size="32rpx" />
								<text>可使用</text>
							</view> -->
						</view>
					</view>
				</view>
			</view>
		</view>
	</t-pull-down-refresh>
	<view class="center-entry">
		<view class="center-entry-btn" bind:tap="showExchangeDialog">
			<view>使用积分兑换探店券</view>
			<t-icon
			  name="chevron-right"
			  color="#fa4126"
			  size="40rpx"
			  style="line-height: 28rpx;"
			/>
		</view>
	</view>
</view>

<t-dialog
  visible="{{showExchangeDialog}}"
  title="确认兑换"
  content="确定要使用10积分兑换一张探店券吗？"
  confirmBtn="确定兑换"
  cancelBtn="取消"
  bind:confirm="handleExchange"
  bind:cancel="hideExchangeDialog"
/>
<t-toast id="t-toast" />
