/* pages/points/index.wxss */
page {
  height: 100%;
  background: #f5f5f5;
}

.points-wrap {
  min-height: 100vh;
}

.points-header {
  background: #fa4126;
  padding: 40rpx 30rpx;
  color: #fff;
}

.points-balance {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.balance-label {
  font-size: 28rpx;
  margin-bottom: 16rpx;
}

.balance-value {
  font-size: 64rpx;
  font-weight: 500;
}

.tabs-external__inner {
  height: 88rpx;
  width: 100%;
  line-height: 88rpx;
  z-index: 100;
  font-size: 26rpx;
  color: #333333;
  position: sticky;
  top: 0;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tabs-external__inner .tabs-external__track {
  background: #fa4126 !important;
  height: 4rpx !important;
  border-radius: 2rpx;
}

.tabs-external__inner .tabs-external__item {
  color: #666;
  transition: all 0.3s;
}

.tabs-external__inner .tabs-external__active {
  font-size: 28rpx;
  color: #fa4126 !important;
  font-weight: 500;
}

.points-list-wrap {
  padding: 20rpx;
  height: 100vh;
}

.points-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.points-list-item {
  width: 100%;
}

.points-card {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.points-card:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.points-left {
  width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  position: relative;
}

.points-left::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 1rpx;
  background: repeating-linear-gradient(
    transparent,
    transparent 10rpx,
    #eee 10rpx,
    #eee 20rpx
  );
}

.points-right {
  flex: 1;
  padding: 30rpx;
  position: relative;
}

.points-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.points-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.points-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.points-desc {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
}

.points-amount {
  font-size: 32rpx;
  font-weight: 500;
  white-space: nowrap;
}

.points-amount.add {
  color: #fa4126;
}

.points-amount.minus {
  color: #666;
}

.points-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16rpx;
  border-top: 1rpx solid #f5f5f5;
}

.points-time {
  font-size: 24rpx;
  color: #999;
}

.t-class-indicator {
  color: #b9b9b9 !important;
}

.t-loading {
  margin: 20rpx 0;
  display: flex;
  justify-content: center;
}

.no-more {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  padding: 20rpx 0;
}