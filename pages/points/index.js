import { createStoreBindings } from "mobx-miniprogram-bindings";
import { authStore } from "../../stores/authStore";
import { userStore } from "../../stores/userStore";

Page({
	/**
	 * 页面的初始数据
	 */
	data: {
		points: 0,
		status: "all",
		list: [
			{ text: "全部", key: "all" },
			{ text: "收入", key: "add" },
			{ text: "支出", key: "minus" },
		],
		pointsList: [],
		enable: false,
		loading: false,
		hasMore: true,
		page: 1,
		pageSize: 10,
	},

	async onLoad() {
		this.authStoreBindings = createStoreBindings(this, {
			store: authStore,
			fields: ["userInfo"],
			actions: ["refreshUserInfo"],
		});
		this.userStoreBindings = createStoreBindings(this, {
			store: userStore,
			fields: ["points"],
			actions: ["fetchPoints", "calcUserPoints"],
		});

		await this.refreshUserInfo();
		setTimeout(async () => {
			// this.init();
			await this.fetchPoints();
			// this.fetchPointsList();
		}, 200);
	},

	onUnload() {
		this.authStoreBindings.destroyStoreBindings();
		this.userStoreBindings.destroyStoreBindings();
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {
		if (this.data.hasMore && !this.data.loading) {
			this.setData({
				page: this.data.page + 1,
			});
			// this.fetchPointsList();
		}
	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {},

	// fetchPoints() {
	//   // 模拟获取积分
	//   this.setData({
	//     points: 1280
	//   });
	// },

	// fetchPointsList() {
	//   if (this.data.loading) return;
	//   this.setData({ loading: true });

	//   // 模拟积分列表数据
	//   const mockData = [
	//     {
	//       id: 1,
	//       title: '签到奖励',
	//       points: 10,
	//       type: 'add',
	//       description: '每日签到获得积分',
	//       createTime: '2024-04-28 08:30'
	//     },
	//     {
	//       id: 2,
	//       title: '发布笔记',
	//       points: 50,
	//       type: 'add',
	//       description: '发布探店笔记获得积分',
	//       createTime: '2024-04-27 15:20'
	//     },
	//     {
	//       id: 3,
	//       title: '兑换优惠券',
	//       points: 100,
	//       type: 'minus',
	//       description: '使用积分兑换优惠券',
	//       createTime: '2024-04-26 10:15'
	//     },
	//     {
	//       id: 4,
	//       title: '邀请好友',
	//       points: 200,
	//       type: 'add',
	//       description: '成功邀请好友注册',
	//       createTime: '2024-04-25 14:40'
	//     },
	//     {
	//       id: 5,
	//       title: '兑换优惠券',
	//       points: 80,
	//       type: 'minus',
	//       description: '使用积分兑换优惠券',
	//       createTime: '2024-04-24 09:30'
	//     },
	//     {
	//       id: 6,
	//       title: '发布笔记',
	//       points: 50,
	//       type: 'add',
	//       description: '发布探店笔记获得积分',
	//       createTime: '2024-04-23 16:20'
	//     },
	//     {
	//       id: 7,
	//       title: '签到奖励',
	//       points: 10,
	//       type: 'add',
	//       description: '每日签到获得积分',
	//       createTime: '2024-04-22 08:30'
	//     }
	//   ];

	//   // 模拟分页
	//   const start = (this.data.page - 1) * this.data.pageSize;
	//   const end = start + this.data.pageSize;
	//   let filteredData = mockData;

	//   if (this.data.status !== 'all') {
	//     filteredData = mockData.filter(item => item.type === this.data.status);
	//   }

	//   const currentPageData = filteredData.slice(start, end);
	//   const hasMore = end < filteredData.length;

	//   setTimeout(() => {
	//     this.setData({
	//       pointsList: this.data.page === 1 ? currentPageData : [...this.data.pointsList, ...currentPageData],
	//       loading: false,
	//       hasMore
	//     });
	//   }, 500);
	// },

	// tabChange(e) {
	//   const { value } = e.detail;
	//   this.setData({
	//     status: value,
	//     page: 1,
	//     pointsList: []
	//   });
	//   this.fetchPoints();
	// },

	async onPullDownRefresh() {
		this.setData({
			enable: true,
		});
		// await this.fetchPointsList();
		await this.fetchPoints();
		await this.calcUserPoints();
		setTimeout(() => {
			this.setData({
				enable: false,
			});
		}, 100);
	},
});
