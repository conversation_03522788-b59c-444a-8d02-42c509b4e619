<wxs module="filter" src="../../utils/util.wxs"></wxs>

<!--pages/points/index.wxml-->
<view class="points-wrap">
  <!-- <view class="points-header">
    <view class="points-balance">
      <view class="balance-label">当前积分</view>
      <view class="balance-value">{{points}}</view>
    </view>
  </view> -->

  <t-tabs
    defaultValue="{{status}}"
    bind:change="tabChange"
    tabList="{{list}}"
    t-class="tabs-external__inner"
    t-class-item="tabs-external__item"
    t-class-active="tabs-external__active"
    t-class-track="tabs-external__track"
  >
    <t-tab-panel
      wx:for="{{list}}"
      wx:for-index="index"
      wx:for-item="tab"
      wx:key="key"
      label="{{tab.text}}"
      value="{{tab.key}}"
    />
  </t-tabs>

  <view class="points-list-wrap">
    <t-pull-down-refresh
      value="{{enable}}"
      loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}"
      t-class-indicator="t-class-indicator"
      id="t-pull-down-refresh"
      bind:refresh="onPullDownRefresh"
      background="#f5f5f5"
    >
      <view class="points-list">
        <view class="points-list-item" wx:for="{{points}}" wx:key="_id">
          <view class="points-card">
            <!-- <view class="points-left">
              <t-icon name="points" color="#fa4126" size="80rpx" />
            </view> -->
            <view class="points-right">
              <view class="points-content">
                <view class="points-desc">{{item.type === 'in' ? '完成探店笔记赠送积分' : '兑换探店券扣除积分'}}</view>
                <view class="points-amount {{item.type === 'add' ? 'add' : 'minus'}}">
                  {{item.type === 'in' ? '+' : '-'}}{{item.amount}}
                </view>
              </view>
              <view class="points-footer">
                <view class="points-time">{{filter.formatDateTime(item.createdAt)}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <t-loading wx:if="{{loading}}" theme="circular" size="40rpx" />
      <view class="no-more" wx:if="{{!hasMore}}">没有更多了</view>
    </t-pull-down-refresh>
  </view>
</view>