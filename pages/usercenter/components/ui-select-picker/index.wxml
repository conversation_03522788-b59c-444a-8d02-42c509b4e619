<t-popup visible="{{show}}" placement="bottom">
  <view class="city-picker-box" slot="content">
    <view wx:if="{{headerVisible}}" class="city-picker-header city-picker-more">
      <view class="btn" hover-class="btn__active" catch:tap="onClose">取消</view>
      <view wx:if="{{title}}" class="title">{{title}}</view>
      <view class="btn primary" hover-class="btn__active" catch:tap="onConfirm">确定</view>
    </view>
    <view wx:else class="city-picker-header">
      <view wx:if="{{title}}" class="title">{{title}}</view>
    </view>
    <picker-view class="picker" indicator-class="picker-center-row" value="{{pickerValue}}" bind:change="onChange">
      <picker-view-column class="picker-column">
        <view wx:for="{{ pickerOptions }}" wx:key="code">{{ item.name }}</view>
      </picker-view-column>
    </picker-view>
    <view class="city-picker-footer" wx:if="{{!headerVisible}}">
      <view class="btn" hover-class="btn__active" catch:tap="onClose">取消</view>
      <view class="btn primary" hover-class="btn__active" catch:tap="onConfirm">确定</view>
    </view>
  </view>
</t-popup>
