.user-center-card {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 480rpx;
	/* background-image: url('https://cdn-we-retail.ym.tencent.com/miniapp/template/user-center-bg-v1.png'); */
	background-size: cover;
	background-repeat: no-repeat;
	padding: 0 24rpx;
	background-color: #ffde59;
}
.user-center-card__header {
	margin-top: 192rpx;
	margin-bottom: 48rpx;
	height: 196rpx;
	line-height: 48rpx;
	display: flex;
	justify-content: flex-start;
	align-items: flex-start;
	color: #333;
	position: relative;
}
.user-center-card__header__avatar {
	width: 96rpx;
	height: 96rpx;
	border-radius: 48rpx;
	overflow: hidden;
}

.user-center-card__header__name {
	font-size: 36rpx;
	line-height: 48rpx;
	color: #333;
	font-weight: bold;
	margin-left: 24rpx;
	margin-right: 16rpx;
}
.user-center-card__header__transparent {
	position: absolute;
	left: 0;
	top: 0;
	background-color: transparent;
	height: 100%;
	width: 100%;
}
.user-center-card__icon {
	line-height: 96rpx;
}

.user-center-card__info {
	flex: 1;
	margin-left: 24rpx;
}

.user-center-card__info__top {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.user-center-card__info__name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.user-center-card__info__id {
	font-size: 28rpx;
	display: flex;
	align-items: center;
}

.user-center-card__info__id__text {
	margin-right: 20rpx;
}

.user-center-card__info__tags {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	margin-top: 12rpx;
	margin-bottom: 12rpx;
}

.credit-tag {
	display: flex;
	align-items: center;
	background-color: #e8f5e9;
	color: #4cd964;
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 24rpx;
	margin-right: 16rpx;
}

.store-btn {
	display: flex;
	align-items: center;
	background-color: #ff4e42;
	color: #ffffff;
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 24rpx;
}

.user-center-card__info__signature {
	font-size: 26rpx;
	color: #666;
	margin-top: 12rpx;
	line-height: 1.4;
}
