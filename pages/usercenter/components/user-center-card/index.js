const AuthStepType = {
  ONE: 1,
  TWO: 2,
  THREE: 3,
};

Component({
  options: {
    multipleSlots: true,
  },
  properties: {
    currAuthStep: {
      type: Number,
      value: AuthStepType.ONE,
    },
    userInfo: {
      type: Object,
      value: {},
    },
    isNeedGetUserInfo: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    defaultAvatarUrl:
      // 'https://cdn-we-retail.ym.tencent.com/miniapp/usercenter/<EMAIL>',
      'https://636c-cloud1-0gpy573m8caa7db3-1321286342.tcb.qcloud.la/app/default-avatar.svg',
    AuthStepType,
  },
  methods: {
    gotoUserEditPage() {
      console.debug("gotoUserEditPage", this.properties.userInfo)
      this.triggerEvent('gotoUserEditPage');
    },

    copyUID() {
      console.debug("copyUID", this.properties.userInfo)
      const uid = this.properties.userInfo.uid;
      wx.setClipboardData({
        data: String(uid),
        success: () => {
          console.debug("copyUID success", uid)
          wx.showToast({
            title: 'UID已复制',
            icon: 'success'
          });
        },
        fail: (err) => {
          console.error("copyUID fail", err)
        }
      });
    },

    gotoMyStore() {
      wx.navigateTo({
        url: '/pages/usercenter/my-store/index'
      });
    }
  },
});
