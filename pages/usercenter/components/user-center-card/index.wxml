<view class="user-center-card">
  <!-- 未登录的情况 -->
  <block wx:if="{{currAuthStep === AuthStepType.ONE}}">
    <view class="user-center-card__header" bind:tap="gotoUserEditPage">
      <t-avatar image="{{userInfo.avatar || defaultAvatarUrl}}" class="user-center-card__header__avatar" />
      <view class="user-center-card__header__name">{{'请登录'}}</view>
    </view>
  </block>
  <!-- 已登录但未授权用户信息情况 -->
  <block wx:if="{{currAuthStep === AuthStepType.TWO}}">
    <view class="user-center-card__header">
      <t-avatar image="{{userInfo.avatar || defaultAvatarUrl}}" class="user-center-card__header__avatar" />
      <view class="user-center-card__header__name">{{userInfo.nickname || '微信用户'}}</view>
      <!-- 需要授权用户信息，通过slot添加弹窗 -->
      <view class="user-center-card__header__transparent" wx:if="{{isNeedGetUserInfo}}">
        <slot name="getUserInfo" />
      </view>
      <!-- 不需要授权用户信息，仍然触发gotoUserEditPage事件 -->
      <view class="user-center-card__header__transparent" bind:tap="gotoUserEditPage" wx:else></view>
    </view>
  </block>
  <!-- 已登录且已经授权用户信息的情况 -->
  <block wx:if="{{currAuthStep === AuthStepType.THREE}}">
    <view class="user-center-card__header">
      <t-avatar
        t-class="avatar"
        mode="aspectFill"
        class="user-center-card__header__avatar"
        image="{{userInfo.avatar || defaultAvatarUrl}}"
        bind:tap="gotoUserEditPage"
      />
      <view class="user-center-card__info">
        <view class="user-center-card__info__top">
          <view class="user-center-card__info__name" bind:tap="gotoUserEditPage">{{userInfo.nickname || '优小探666'}}</view>
        </view>
        <view class="user-center-card__info__id">
          <text class="user-center-card__info__id__text">UID: {{userInfo.uid }}</text>
          <t-icon name="copy" size="32rpx" color="#8b8b8b" bind:tap="copyUID" />
        </view>

        <!-- <view class="user-center-card__info__tags">
          <view class="credit-tag">
            <t-icon name="check-circle-filled" size="28rpx" color="#4CD964" />
            <text>信用极好</text>
          </view>
          <view class="store-btn" bind:tap="gotoMyStore">
            <text>我的探店</text>
            <t-icon name="chevron-right" size="28rpx" color="#FFFFFF" />
          </view>
        </view>
        <view class="user-center-card__info__signature">
          <text>{{userInfo.signature || '我愿遵守xx公约！用心拍摄，用心创作！做温暖小羊！'}}</text>
        </view> -->
      </view>
    </view>
  </block>
</view>
