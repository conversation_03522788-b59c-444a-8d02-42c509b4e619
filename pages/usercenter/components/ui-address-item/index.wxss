.address-item-wrapper {
  overflow: hidden;
}
.address-item-wrapper .swipe-out .wr-swiper-cell {
  margin-top: 20rpx;
}
.address-item-wrapper .swipe-out .swipe-right-del {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 144rpx;
  height: 100%;
  background-color: #fa4126;
  color: #fff;
  font-size: 28rpx;
  line-height: 40rpx;
}
.address-item-wrapper .draw-line {
  position: relative;
}
.address-item-wrapper .draw-line::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 32rpx;
  width: 200%;
  height: 2rpx;
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
  border-bottom: #e5e5e5 2rpx solid;
}
.address-item-wrapper .address {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  background-color: #fff;
}
.address-item-wrapper .address .address-edit {
  padding: 20rpx 0 20rpx 46rpx;
}
.address-item-wrapper .address .address-left {
  width: 80rpx;
  display: flex;
  justify-content: center;
}
.address-item-wrapper .address .address-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}
.address-item-wrapper .address .address-content .title {
  font-size: 32rpx;
  line-height: 48rpx;
  margin-bottom: 16rpx;
  color: #333333;
  font-weight: bold;
  display: flex;
}
.address-item-wrapper .address .address-content .title .text-style {
  margin-right: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 280rpx;
}
.address-item-wrapper .address .address-content .label-adds {
  display: flex;
}
.address-item-wrapper .address .address-content .label-adds .adds {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  color: #999999;
}
.address-item-wrapper .address .address-content .label-adds .tag {
  display: inline-block;
  padding: 0rpx 8rpx;
  min-width: 40rpx;
  height: 32rpx;
  border-radius: 18rpx;
  font-size: 20rpx;
  line-height: 32rpx;
  text-align: center;
  margin-right: 8rpx;
  vertical-align: text-top;
}
.address-item-wrapper .address .address-content .label-adds .tag-default {
  background: #ffece9;
  color: #fa4126;
}
.address-item-wrapper .address .address-content .label-adds .tag-primary {
  background: #f0f1ff;
  color: #5a66ff;
}
.address-item-wrapper .address .address-content .label-adds .address-text {
  font-size: 28rpx;
  line-height: 40rpx;
  color: #999999;
}
