import { createStoreBindings } from "mobx-miniprogram-bindings";
import { userStore } from "../../../stores/userStore";
import Toast from "tdesign-miniprogram/toast/index";

Page({

  /**
   * 页面的初始数据
   */
  data: {
    shop_name: '',
    city: '',
    address: '',
    contact: '',
    phone: '',
    isSubmiting: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
		this.userStoreBindings = createStoreBindings(this, {
			store: userStore,
			fields: [],
			actions: ["createInquiryRecord"],
		});
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
		this.userStoreBindings.destroyStoreBindings();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 输入框通用处理
  onInput(e) {
    const name = e.currentTarget.dataset.name;
    const value = e.detail.value;
    console.debug("onInput: ", e, name, value)
    this.setData({ [name]: value });
  },

  async onSubmit() {
    if(this.data.isSubmiting) return;
    this.setData({
      isSubmiting: true
    })
    console.debug(this.data);
    const { shop_name, city, address, contact, phone } = this.data;
    if (!shop_name) {
      wx.showToast({ title: '请输入门店名称', icon: 'none' });
      this.setData({
        isSubmiting: false
      })
      return;
    }
    if (!city) {
      wx.showToast({ title: '请输入城市', icon: 'none' });
      this.setData({
        isSubmiting: false
      })
      return;
    }
    if (!address) {
      wx.showToast({ title: '请输入店铺地址', icon: 'none' });
      this.setData({
        isSubmiting: false
      })
      return;
    }
    if (!contact) {
      wx.showToast({ title: '请输入联系人', icon: 'none' });
      this.setData({
        isSubmiting: false
      })
      return;
    }
    if (!phone) {
      wx.showToast({ title: '请输入手机号码', icon: 'none' });
      this.setData({
        isSubmiting: false
      })
      return;
    }
    // if (!/^1[3-9]\d{9}$/.test(phone)) {
    //   wx.showToast({ title: '请输入正确的手机号码', icon: 'none' });
    //   return;
    // }

    const body = {
      shop_name: this.data.shop_name,
      city: this.data.city,
      address: this.data.address,
      contact: this.data.contact,
      phone: this.data.phone,
    }

    console.debug("body: ", body);
    // wx.showToast({ 
    //   title: '提交成功，商务会在一个工作日联系',
    //   icon: 'none',
    //   duration: 2000 
    // });
    
    // 这里可以调用后端API提交数据
    const result = await this.createInquiryRecord(body)
    if(result){
      setTimeout(() => {
        wx.switchTab({
          url: "/pages/usercenter/index",
        });
      }, 2000);

      Toast({
        context: this,
        selector: '#t-toast',
        message: '提交成功，商务会在一个工作日联系',
        icon: 'success',
        duration: 3000,
      });
    }
  }
})