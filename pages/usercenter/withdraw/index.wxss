/* pages/usercenter/withdraw/index.wxss */
.withdraw-container {
	padding: 30rpx 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 实名认证信息区域 */
.auth-info {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
	border-radius: 16rpx;
}

.auth-info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-bottom: 20rpx;
}

.auth-info-label {
	display: flex;
	align-items: center;
}

.auth-icon {
	color: #ff4d4f;
	margin-right: 10rpx;
}

.auth-info-value {
	color: #999;
}

.auth-tips {
	display: flex;
	align-items: flex-start;
	background-color: #fffbe8;
	padding: 20rpx;
	border-radius: 8rpx;
}

.tips-icon {
	display: inline-block;
	width: 30rpx;
	height: 30rpx;
	line-height: 30rpx;
	text-align: center;
	border-radius: 50%;
	background-color: #ffd21e;
	color: #fff;
	font-size: 24rpx;
	margin-right: 10rpx;
	margin-top: 4rpx;
}

.tips-text {
	color: #999;
	font-size: 24rpx;
	flex: 1;
	line-height: 36rpx;
}

/* 提现金额输入区域 */
.withdraw-amount-section {
	background-color: #fff;
	padding: 30rpx;
	/* margin-bottom: 20rpx; */
	border-radius: 16rpx 16rpx 0 0;
}

.amount-title {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 30rpx;
}

.amount-input-container {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.currency-symbol {
	font-size: 60rpx;
	font-weight: bold;
	margin-right: 10rpx;
}

.amount-input {
	flex: 1;
	font-size: 60rpx;
	font-weight: bold;
	height: 80rpx;
	border-bottom: 1px solid #cec7c7;
}

.withdraw-all-btn {
	padding: 10rpx 20rpx;
	/* background-color: #ffe7ba; */
	color: #fa8c16;
	border-radius: 30rpx;
	font-size: 24rpx;
}

.balance-info {
	font-size: 26rpx;
	color: #999;
}

.balance-value {
	color: #333;
	margin: 0 6rpx;
}

/* 提现规则提示 */
.withdraw-rules {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
	border-radius: 0 0 16rpx 16rpx;
}

.rule-item {
	display: flex;
	align-items: flex-start;
}

.rule-icon {
	display: inline-block;
	width: 30rpx;
	height: 30rpx;
	line-height: 30rpx;
	text-align: center;
	border-radius: 50%;
	background-color: #ffd21e;
	color: #fff;
	font-size: 24rpx;
	margin-right: 10rpx;
	margin-top: 4rpx;
}

.rule-text {
	color: #999;
	font-size: 24rpx;
	flex: 1;
	line-height: 36rpx;
	margin-left: 10rpx;
}

/* 提现至微信按钮 */
.withdraw-to-wechat {
	background-color: #fff;
	padding: 30rpx;
	/* margin-bottom: 20rpx;
  border-radius: 16rpx; */
}

.wechat-btn {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}

.wechat-icon {
	width: 60rpx;
	height: 60rpx;
	background-color: #07c160;
	color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	margin-right: 20rpx;
}

.withdraw-history {
	background-color: #fff;
	padding: 15px;
	border-radius: 16rpx;
}

/* 底部按钮区域 */
.bottom-actions {
	display: flex;
	padding: 30rpx;
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	z-index: 100;
	margin-bottom: 40rpx;
}

.service-btn {
	width: 120rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f5f5f5;
	border-radius: 40rpx;
	margin-right: 20rpx;
	font-size: 28rpx;
}

.apply-btn {
	flex: 1;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #ffe066;
	border-radius: 40rpx;
	font-size: 28rpx;
	font-weight: bold;
}
rule-text /* 提现历史记录 */ .withdraw-history {
	background-color: #fff;
	padding: 30rpx;
	border-radius: 16rpx;
}

.history-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.history-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.history-subtitle {
	font-size: 24rpx;
	color: #999;
	background-color: #fff7e6;
	padding: 4rpx 10rpx;
	border-radius: 4rpx;
	margin-left: 20rpx;
}

.refresh-btn {
	font-size: 24rpx;
	color: #999;
	margin-left: auto;
}

.history-list {
	padding-top: 20rpx;
}

.history-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}

.history-item-left {
	display: flex;
	flex-direction: column;
}

.history-status {
	font-size: 28rpx;
	color: #333;
	margin: 10rpx 0;
}

.history-time {
	font-size: 24rpx;
	color: #999;
}

.history-amount {
	font-size: 36rpx;
	font-weight: bold;
	color: #fa8c16;
}

.disabled {
	opacity: 0.6;
	cursor: not-allowed;
}
