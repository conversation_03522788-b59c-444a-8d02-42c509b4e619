// pages/usercenter/withdraw/index.js
import Toast from "tdesign-miniprogram/toast/index";
import { createStoreBindings } from "mobx-miniprogram-bindings";
import { userStore } from "../../../stores/userStore";
import { authStore } from "../../../stores/authStore";
import dayjs from "dayjs";

Page({
	/**
	 * 页面的初始数据
	 */
	data: {
		// userInfo: {
		//   realName: '真实姓名', // 用户实名认证的姓名
		//   isVerified: true, // 是否已实名认证
		// },
		withdrawAmount: 0.0, // 提现金额
		balance: 0, // 用户余额（羊毛）
		rules: {
			minAmount: 1, // 最小提现金额
			maxAmount: 200, // 最大提现金额
			rate: 1, // 提现比率，1羊毛=1元
			dailyLimit: 1, // 每日提现次数限制
		},
		disabled: true,
		submitting: false,
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	async onLoad(options) {
		this.userStoreBindings = createStoreBindings(this, {
			store: userStore,
			fields: ["withdrawHistory", "withdrawRecordCreated", "userInfo"],
			actions: ["fetchUserWithdrawHistory", "createWithdrawRecord"],
		});
		this.authStoreBindings = createStoreBindings(this, {
			store: authStore,
			fields: ["userInfo"],
			actions: ["refreshUserInfo"],
		});

		this.fetchUserWithdrawHistory();

		// 模拟获取用户余额数据
		// this.getUserBalance();
	},

	onUnload() {
		console.debug("destroyStoreBindings");
		this.userStoreBindings.destroyStoreBindings();
		this.authStoreBindings.destroyStoreBindings();
	},

	/**
	 * 输入提现金额
	 */
	onInput(e) {
		console.debug("onInput", e.detail.value);
		const value = Number(e.detail.value);
		if (value > 200) {
			Toast({
				context: this,
				selector: "#t-toast",
				message: "由于微信限制单次最多能提现200元",
				direction: "column",
				duration: 2000,
			});
		}
		this.setData({
			withdrawAmount: value,
		});
	},

	onFocus(e) {
		console.debug("onFocus", e.detail.value);

		// this.setData({
		// 	withdrawAmount: 1,
		// 	disabled: true,
		// });
	},

	onInputChange(e) {
		console.debug("onInputChange", e.detail.value);
		const value = Number(e.detail.value);
		this.setData({
			withdrawAmount: value,
			disabled: false,
		});

		if (value > 200) {
			this.setData({
				withdrawAmount: 0.0,
				disabled: true,
			});
			Toast({
				context: this,
				selector: "#t-toast",
				message: "由于微信限制单次最多能提现200元",
				direction: "column",
				duration: 2000,
			});
			return;
		}

		if (value < this.data.rules.minAmount) {
			Toast({
				context: this,
				selector: "#t-toast",
				message: "最小提现金额为1元",
			});
			this.setData({
				withdrawAmount: 1,
				disabled: true,
			});
			return;
		}

		if (value > this.data.userInfo.balance) {
			Toast({
				context: this,
				selector: "#t-toast",
				message: "可提现金额不足",
				direction: "column",
				duration: 2000,
			});
			this.setData({
				disabled: true,
			});
			return;
		}
	},

	/**
	 * 全部提现
	 */
	onWithdrawAll() {
		console.debug("onWithdrawAll");
		// 全部提现
		const { rules } = this.data;
		const { balance } = this.data.userInfo;
		let amount = balance * rules.rate;

		// 如果余额超过最大提现金额，则按最大提现金额处理
		if (amount > rules.maxAmount) {
			amount = rules.maxAmount;
		}

		console.debug("ammmmmmount", amount);

		if (amount > 0) {
			console.debug("amount", amount);
			this.setData({
				withdrawAmount: amount.toFixed(2),
				disabled: false, // 金额大于0，立即申请可以点击
			});
		} else {
			this.setData({
				withdrawAmount: 0,
				disabled: true, // 金额小于0，禁用提现按钮
			});
		}
	},

	/**
	 * 联系客服
	 */
	onContactService() {
		this.showToast("正在连接客服");
	},

	/**
	 * 刷新历史记录
	 */
	async onRefreshHistory() {
		console.debug("onRefreshHistory");
		this.showToast("正在刷新...");
		// 模拟刷新历史记录
		setTimeout(() => {
			this.showToast("刷新成功");
		}, 1000);
		await this.fetchUserWithdrawHistory();
	},

	/**
	 * 显示提示信息
	 */
	showToast(message) {
		Toast({
			context: this,
			selector: "#t-toast",
			message: message,
		});
	},

	/**
	 * 格式化时间
	 */
	formatTime(date) {
		const year = date.getFullYear();
		const month = (date.getMonth() + 1).toString().padStart(2, "0");
		const day = date.getDate().toString().padStart(2, "0");
		const hour = date.getHours().toString().padStart(2, "0");
		const minute = date.getMinutes().toString().padStart(2, "0");
		const second = date.getSeconds().toString().padStart(2, "0");
		return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	async onShow() {
		await this.refreshUserInfo();
		console.debug("userInfo", this.data.userInfo);
		if (this.data.userInfo && !this.data.userInfo.phone) {
			console.debug("userInfo", this.data.userInfo);
			wx.showToast({
				title: "请先绑定手机号",
				icon: "error",
				duration: 2000,
			});
			setTimeout(() => {
				wx.navigateTo({
					url: "/pages/usercenter/bind-phone/index",
				});
			}, 1000);
		}
	},

	async refreshWithdrawTimesInToday() {
		await this.fetchUserWithdrawHistory(); // 刷新提现记录
		await this.refreshUserInfo(); // 刷新用户余额

		const recordsInToday = this.data.withdrawHistory.filter(
			(x) =>
				dayjs(x.createdAt).format("YYYY-MM-DD") ===
				dayjs().format("YYYY-MM-DD"),
		);
		// return recordsInToday.length;
		this.setData({
			withdrawTimesInToday: recordsInToday.length,
		});
	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	async onPullDownRefresh() {
		this.setData({ enable: true });
		setTimeout(() => {
			this.setData({ enable: false });
		}, 300);
		// await this.fetchUserWithdrawHistory(); // 刷新提现记录
		// await this.refreshUserInfo(); // 刷新用户余额
		await this.refreshWithdrawTimesInToday();
	},

	navToCustomerService() {
		wx.navigateTo({
			url: "/pages/customer-service/index",
		});
	},

	async applyNow() {
		if (this.data.disabled || this.data.submitting) {
			return;
		}

		this.setData({
			submitting: true,
			disabled: true,
		});

		// 每天只能提现一次
		await this.refreshWithdrawTimesInToday();
		if (this.data.withdrawTimesInToday > 0) {
			Toast({
				context: this,
				selector: "#t-toast",
				message: "今天提现过了, 请明日再来",
				theme: "error",
				direction: "column",
				duration: 5000,
			});
			this.setData({
				disabled: true,
				submitting: false,
			});
			return;
		}

		// 检验余额
		if (this.data.withdrawAmount > this.data.userInfo.balance) {
			Toast({
				context: this,
				selector: "#t-toast",
				message: "余额不足",
				theme: "error",
				direction: "column",
				duration: 5000,
			});
			return;
		}

		console.debug("apply now");
		wx.showToast({
			title: "申请中",
			icon: "loading",
			duration: 5000,
		});

		// await this.createWithdrawRecord()
		// return

		const out_bill_no =
			"T" +
			dayjs().format("YYYYMMDDHHmmss") +
			Math.floor(Math.random() * 1000000);
		const transfer_amount = this.data.withdrawAmount;
		if (transfer_amount < 0.3) {
			// 最小提现金额0.3元
			return;
		}

		if (wx.canIUse("requestMerchantTransfer")) {
			wx.cloud.callFunction({
				name: "create_transfer_bill",
				data: {
					out_bill_no: out_bill_no,
					transfer_amount: Math.round(transfer_amount * 100),
				},
				success: async (res) => {
					wx.hideToast();
					console.log("res:", res);
					if (res.errMsg === "cloud.callFunction:ok") {
						if (
							res.result.code === 0 &&
							res.result.data.state === "WAIT_USER_CONFIRM"
						) {
							const package_info = res.result.data.package_info;
							const out_bill_no = res.result.data.out_bill_no;
							const transfer_bill_no = res.result.data.transfer_bill_no;

							await this.refreshWithdrawTimesInToday();
							if (this.data.withdrawTimesInToday > 0) {
								Toast({
									context: this,
									selector: "#t-toast",
									message: "今天提现过了, 请明日再来",
									theme: "error",
									direction: "column",
									duration: 5000,
								});
								this.setData({
									disabled: true,
									submitting: false,
								});
								return;
							}

							wx.requestMerchantTransfer({
								mchId: "**********",
								appId: wx.getAccountInfoSync().miniProgram.appId,
								package: package_info,
								success: async (res) => {
									// res.err_msg将在页面展示成功后返回应用时返回ok，并不代表付款成功
									console.log("success:", res);
									// 创建提现记录
									const params = {
										out_bill_no: out_bill_no,
										transfer_bill_no: transfer_bill_no,
										amount: transfer_amount,
									};

									const response = await this.createWithdrawRecord(params);
									console.debug("createWithdrawRecord res", response);

									wx.showToast({
										title: "提现成功",
										icon: "success",
										duration: 2000,
									});

									// 提完之后立马对该用户对一次账
									wx.cloud.callFunction({
										name: "calc_user_money",
										data: {
											user_id: this.data.userInfo._id,
										},
										success: async (res) => {
											console.log("res:", res);
										},
										fail: (res) => {
											console.log("fail:", res);
										},
									});

									this.setData({
										submitting: false,
										disabled: false,
									});

									wx.switchTab({
										url: "/pages/usercenter/index",
									});
									return;
								},
								fail: (res) => {
									console.log("fail:", res);
									if (res.errMsg === "requestMerchantTransfer:fail cancel") {
										Toast({
											context: this,
											selector: "#t-toast",
											message: "用户取消提现申请",
											theme: "error",
											direction: "column",
										});
										this.setData({
											submitting: false,
											disabled: false,
										});
									} else {
										Toast({
											context: this,
											selector: "#t-toast",
											message: "提现失败请联系客服",
											theme: "error",
											direction: "column",
										});
										this.setData({
											submitting: false,
											disabled: false,
										});
									}
								},
							});
						} else {
							// res: {"errMsg": "cloud.callFunction:ok", "result": {"code": -1, "message": "创建转账单失败: 请求失败: 403 {"code":"NOT_ENOUGH","message":"资金不足"}"}, "requestID": "76c52a33-d2f8-4608-8e46-40f3181fdf7c"}
							Toast({
								context: this,
								selector: "#t-toast",
								message: "提现失败请联系客服",
								theme: "error",
								direction: "column",
							});
							this.setData({
								submitting: false,
								disabled: false,
							});
						}
					} else {
						Toast({
							context: this,
							selector: "#t-toast",
							message: "提现超时请稍后再试",
							theme: "error",
							direction: "column",
						});
						this.setData({
							submitting: false,
							disabled: false,
						});
					}
				},
				fail: (err) => {
					console.error("create_transfer_bill失败:", err);
					Toast({
						context: this,
						selector: "#t-toast",
						message: "提现超时请稍后再试",
						theme: "error",
						direction: "column",
					});
					this.setData({
						submitting: false,
						disabled: false,
					});
				},
			});
		} else {
			wx.showToast({
				title: "你的微信版本过低，请更新至最新版本。",
				icon: "error",
			});
		}
	},
});
