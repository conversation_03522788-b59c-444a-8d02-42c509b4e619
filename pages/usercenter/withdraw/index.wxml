<wxs module="filter" src="../../../utils/util.wxs"></wxs>

<t-pull-down-refresh value="{{enable}}" loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}" usingCustomNavbar
  bind:refresh="onPullDownRefresh">

  <view class="withdraw-container">
    <!-- <view class="auth-info">
    <view class="auth-info-item">
      <view class="auth-info-label">
        <text class="auth-icon">*</text>
        <text>真实姓名</text>
      </view>
      <view class="auth-info-value">填写真实姓名</view>
    </view>
    <view class="auth-tips">
      <text class="tips-icon">i</text>
      <text class="tips-text">您已完成实名认证，提现时需使用与实名认证信息一致的账号提现，一个账号用户只能绑定一个实名信息。</text>
    </view>
  </view> -->

    <!-- 提现金额输入区域 -->
    <view class="withdraw-amount-section">
      <view class="amount-title">请输入提现金额</view>
      <view class="amount-input-container">
        <text class="currency-symbol">¥</text>
        <input class="amount-input" type="number" value="{{withdrawAmount}}" placeholder="0.00" bind:focus="onFocus"
          bind:change="onInputChange" bind:input="onInput" />
        <!-- <view class="withdraw-all-btn" bindtap="onWithdrawAll">全部提现</view> -->
      </view>
      <view class="balance-info">您有<text class="balance-value">{{userInfo.balance || 0}}</text>可提现余额</view>
    </view>

    <!-- 提现至微信按钮 -->
    <view class="withdraw-to-wechat">
      <view class="wechat-btn">
        <view class="wechat-icon">
          <t-icon name="check" size="48rpx" />
        </view>
        <text>提现至微信零钱</text>
      </view>
    </view>
    <!-- 提现规则提示 -->
    <view class="withdraw-rules">
      <view class="rule-item">
        <t-icon name="info-circle" size="48rpx" style="color: #999;" />
        <!-- <text class="rule-icon">i</text> -->
        <text class="rule-text">仅支持提现到微信零钱，单次提现最高200元，最少1元，每日仅可提现一次，提现可能到账有延迟（如有问题请联系客服）</text>
      </view>
    </view>


    <!-- 提现历史记录 -->
    <view class="withdraw-history">
      <view class="history-header">
        <text class="history-title">提现历史</text>
        <text class="history-subtitle">仅显示最近10条记录</text>
        <text class="refresh-btn" bindtap="onRefreshHistory">刷新</text>
      </view>

      <!-- 历史记录列表 -->
      <view class="history-list">

        <block wx:for="{{withdrawHistory}}" wx:key="_id">
          <view class="history-item">
            <view class="history-item-left">
              <view class="history-status">已成功提现</view>
              <view class="history-time">{{filter.formatDateTime(item.completed_at)}}</view>
            </view>
            <view class="history-amount">-{{item.amount}}</view>
          </view>
        </block>

        <!-- <view class="history-item">
        <view class="history-item-left">
          <view class="history-status">已成功提现</view>
          <view class="history-time">2025-03-07 17:07:47</view>
        </view>
        <view class="history-amount">-121</view>
      </view> -->

      </view>
    </view>
  </view>

</t-pull-down-refresh>

<!-- 底部按钮区域 -->
<view class="bottom-actions">
  <view class="service-btn" bind:tap="navToCustomerService">客服</view>
  <view class="apply-btn {{ disabled ? 'disabled' : '' }}" bind:tap="applyNow">{{submitting ? '申请中...' : '立即申请'}}</view>
</view>

<t-toast id="t-toast" />