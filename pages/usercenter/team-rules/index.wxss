/* pages/usercenter/team-rules/index.wxss */
.team-rules-page {
	padding: 24rpx 20rpx 40rpx 20rpx;
	background: #fffbe7;
	min-height: 100vh;
}
.header {
	text-align: center;
	margin-bottom: 32rpx;
}
.icon-area {
	font-size: 48rpx;
	margin-bottom: 8rpx;
}
.main-title {
	font-size: 60rpx;
	font-weight: bold;
	color: #222;
	/* margin-bottom: 8rpx; */
}
.sub-title {
	font-size: 60rpx;
	/* color: #ffb300; */
	font-weight: 600;
	margin-bottom: 8rpx;
	margin-top: -16rpx;
}
.desc {
	font-size: 24rpx;
	color: #666;
}
.section {
	margin-bottom: 32rpx;
}
.section-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}
.section-content {
	font-size: 24rpx;
	color: #444;
	line-height: 1.7;
}
.level-table {
	margin-top: 16rpx;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx #f5e6b3;
}
.table-row {
	display: flex;
	background: #fff;
	border-bottom: 1rpx solid #f5e6b3;
}
.table-row:last-child {
	border-bottom: none;
}
.table-header {
	background: #fff3c0;
	font-weight: bold;
}
.table-cell {
	flex: 1;
	padding: 18rpx 0;
	text-align: center;
	font-size: 24rpx;
	color: #333;
}
.update-table {
	margin-top: 16rpx;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx #f5e6b3;
	margin-bottom: 16rpx;
}
.update-row {
	display: flex;
	background: #fff;
	border-bottom: 1rpx solid #f5e6b3;
}
.update-row:last-child {
	border-bottom: none;
}
.update-header {
	background: #fff3c0;
	font-weight: bold;
}
.update-cell {
	flex: 1;
	padding: 18rpx 0;
	text-align: center;
	font-size: 24rpx;
	color: #333;
}
.update-cell .bold {
	font-weight: bold;
}
.update-cell.right-cell {
	flex: 3;
	text-align: left;
	padding-right: 12rpx;
}
.update-notice {
	background: #fffde6;
	border-radius: 8rpx;
	padding: 16rpx;
	margin-top: 12rpx;
	color: #b48a00;
	font-size: 24rpx;
}
.notice-title {
	font-weight: bold;
	margin-bottom: 8rpx;
}
.notice-list {
	margin-left: 8rpx;
}
.notice-item {
	margin-bottom: 4rpx;
}
.benefit-table {
	margin-top: 16rpx;
}
.benefit-table .table-header {
	background: #ffe9b3;
}
.benefit-table .table-cell {
	font-size: 22rpx;
	padding: 14rpx 0;
}
.detail-table .table-header {
	background: #f7f7f7;
}
.detail-table .table-cell {
	font-size: 22rpx;
	padding: 14rpx 0;
	text-align: left;
	word-break: break-all;
}
.detail-table .table-cell:first-child {
	width: 80rpx;
	color: #b48a00;
	font-weight: bold;
	text-align: center;
}

.t-divider__content {
	--td-divider-content-font-size: 48rpx;
	--td-divider-content-color: #000;
}

.sub-header {
	margin-top: 80rpx;
}

.items-center {
	display: flex;
	align-items: center;
}

.justify-center {
	display: flex;
	justify-content: center;
}

.tips {
	font-size: 20rpx;
}

.examples {
	font-size: 22rpx;
}

.exmaple-title {
	margin-top: 20rpx;
}

.rule {
	line-height: 40rpx;
}

.hide {
	display: none;
}
