.bind-phone-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 32rpx;
}

.bind-phone-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 48rpx;
  margin-top: 32rpx;
}

.bind-phone-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 24rpx;
}

.bind-phone-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 48rpx;
}

.bind-phone-btn {
  margin: 48rpx 0;
}

.bind-phone-agreement {
  margin-top: 32rpx;
  display: flex;
  justify-content: center;
}

.agreement-text {
  font-size: 24rpx;
  color: #666;
}

.agreement-link {
  font-size: 24rpx;
  color: #0052d9;
}