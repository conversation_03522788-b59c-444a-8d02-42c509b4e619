import { createStoreBindings } from "mobx-miniprogram-bindings";
import { userStore } from "../../../stores/userStore";

Page({
	/**
	 * 页面的初始数据
	 */
	data: {
		phoneNumber: "",
		isAgree: false,
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		this.userStoreBindings = createStoreBindings(this, {
			store: userStore,
			fields: ["userInfo"],
			actions: ["updateUserPhone"],
		});
	},

	onUnload() {
		this.userStoreBindings.destroyStoreBindings();
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {},

	// 获取手机号
	getPhoneNumber(e) {
		if (e.detail.errMsg === "getPhoneNumber:ok") {
			console.debug(e.detail);
			// 获取到加密数据
			const { code, encryptedData, iv } = e.detail;
			wx.showLoading();
			// 调用云函数解密手机号
			wx.cloud.callFunction({
				name: "get_phone_number",
				data: {
					code,
					encryptedData,
					iv,
				},
				success: async (res) => {
					console.debug(res);
					if (res.errMsg === "cloud.callFunction:ok") {
						console.log("解密后的手机号:", res.result.phoneNumber);
						// 这里可以处理获取到的手机号
						const response = await this.updateUserPhone({
							phone: res.result.phoneNumber,
						});
						console.debug("updateUserPhone res", response);
						if (response.data.count > 0) {
							wx.hideLoading();
							wx.showToast({
								title: "绑定成功",
								icon: "success",
							});
							// this.userInfo.phone = res.result.phoneNumber
							setTimeout(() => {
								wx.navigateBack({ backRefresh: true });
							}, 1500);
						}
					} else {
						wx.hideLoading();
						wx.showToast({
							title: "获取手机号失败",
							icon: "none",
						});
					}
				},
				fail: (err) => {
					console.error("获取手机号失败:", err);
				},
			});
			// this.bindPhoneNumber(code)
		} else {
			wx.showToast({
				title: "获取手机号失败",
				icon: "none",
			});
		}
	},

	// // 绑定手机号
	// async bindPhoneNumber(code) {
	//   try {
	//     // TODO: 调用后端接口进行手机号绑定
	//     wx.showToast({
	//       title: '绑定成功',
	//       icon: 'success'
	//     })
	//     setTimeout(() => {
	//       wx.navigateBack()
	//     }, 1500)
	//   } catch (error) {
	//     wx.showToast({
	//       title: '绑定失败',
	//       icon: 'none'
	//     })
	//   }
	// },

	// 同意协议
	onAgreeChange(e) {
		this.setData({
			isAgree: e.detail.value,
		});
	},
});
