import { fetchPerson } from "../../../services/usercenter/fetchPerson";
import {
	phoneEncryption,
	getFileExtension,
	generateRandomFileName,
} from "../../../utils/util";
import Toast from "tdesign-miniprogram/toast/index";
import { createStoreBindings } from "mobx-miniprogram-bindings";
import { authStore } from "../../../stores/authStore";

Page({
	data: {
		personInfo: {
			avatarUrl: "",
			nickName: "",
			gender: 0,
			phoneNumber: "",
		},
		showUnbindConfirm: false,
		pickerOptions: [
			{
				name: "男",
				code: "1",
			},
			{
				name: "女",
				code: "2",
			},
		],
		typeVisible: false,
		genderMap: ["", "男", "女"],
	},
	onLoad() {
		// this.init();
		this.authStoreBindings = createStoreBindings(this, {
			store: authStore,
			fields: ["userInfo", "referrerUser"],
			actions: ["refreshUserInfo", "saveAvatar", "getReferrerUser"],
		});
	},
	onUnload() {
		this.authStoreBindings.destroyStoreBindings();
	},
	async onShow() {
		this.init();
	},
	async init() {
		await this.refreshUserInfo();
		// await this.getReferrerUser();
	},
	fetchData() {
		fetchPerson().then((personInfo) => {
			this.setData({
				personInfo,
				"personInfo.phoneNumber": phoneEncryption(personInfo.phoneNumber),
			});
		});
	},
	onClickCell({ currentTarget }) {
		const { dataset } = currentTarget;
		const { nickname } = this.data.userInfo;

		switch (dataset.type) {
			case "gender":
				this.setData({
					typeVisible: true,
				});
				break;
			case "name":
				wx.navigateTo({
					url: `/pages/usercenter/name-edit/index?name=${nickname}`,
				});
				break;
			case "avatarUrl":
				this.toModifyAvatar();
				break;
			default: {
				break;
			}
		}
	},
	onBindPhone() {
		// if (!this.data.userInfo.phone) {
		wx.navigateTo({
			url: "/pages/usercenter/bind-phone/index",
		});
		// }
	},
	onClose() {
		this.setData({
			typeVisible: false,
		});
	},
	onConfirm(e) {
		const { value } = e.detail;
		this.setData(
			{
				typeVisible: false,
				"personInfo.gender": value,
			},
			() => {
				Toast({
					context: this,
					selector: "#t-toast",
					message: "设置成功",
					theme: "success",
				});
			},
		);
	},
	async toModifyAvatar() {
		try {
			const tempFilePath = await new Promise((resolve, reject) => {
				wx.chooseImage({
					count: 1,
					sizeType: ["compressed"],
					sourceType: ["album", "camera"],
					success: (res) => {
						const { path, size } = res.tempFiles[0];
						if (size <= 10485760) {
							resolve(path);
						} else {
							reject({ errMsg: "图片大小超出限制，请重新上传" });
						}
					},
					fail: (err) => reject(err),
				});
			});
			const tempUrlArr = tempFilePath.split("/");
			const tempFileName = tempUrlArr[tempUrlArr.length - 1];
			Toast({
				context: this,
				selector: "#t-toast",
				message: `已选择图片-${tempFileName}`,
				theme: "success",
			});
		} catch (error) {
			if (error.errMsg === "chooseImage:fail cancel") return;
			Toast({
				context: this,
				selector: "#t-toast",
				message: error.errMsg || error.msg || "修改头像出错了",
				theme: "error",
			});
		}
	},

	async onChooseAvatar(e) {
		const { avatarUrl } = e.detail;
		this.setData({
			"personInfo.avatarUrl": avatarUrl,
		});
		console.log("用户头像:", avatarUrl);

		// 获取文件后缀
		const fileExtension = getFileExtension(avatarUrl);
		console.log("文件后缀:", fileExtension);
		// 生成随机文件名并保留后缀
		const randomFileName = `${generateRandomFileName()}.${fileExtension}`;
		const cloudPath = `avatars/${this.data.userInfo._id}/${randomFileName}`; // 云存储路径
		console.debug("cloudPath:", cloudPath);

		wx.cloud.uploadFile({
			cloudPath: cloudPath, // 上传至云端的路径
			filePath: avatarUrl, // 小程序临时文件路径
			success: async (res) => {
				// 返回文件 ID
				console.log("fileID", res.fileID);
				console.debug(res);

				// cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-1321286342/example.png
				await this.saveAvatar(res.fileID);

				await this.refreshUserInfo();
			},
			fail: console.error,
		});
		// 上传成功后会获得文件唯一标识符，即文件 ID，后续操作都基于文件 ID 而不是 URL。

		// try {
		// 	const { tempFilePath } = await wx.chooseAvatar({
		// 		sourceType: ["album", "camera"],
		// 		sizeType: ["compressed"],
		// 	});
		// 	this.setData({
		// 		"personInfo.avatarUrl": tempFilePath,
		// 	});
		// } catch (error) {
		// 	if (error.errMsg === "chooseAvatar:fail cancel") return;
		// 	Toast({
		// 		context: this,
		// 		selector: "#t-toast",
		// 		message: error.errMsg || error.msg || "修改头像出错了",
		// 		theme: "error",
		// 	});
		// }
	},
});
