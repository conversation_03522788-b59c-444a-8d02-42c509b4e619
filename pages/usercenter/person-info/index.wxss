:host {
	background-color: #f5f5f5;
}
page view {
	box-sizing: border-box;
}
.person-info {
	padding-top: 20rpx;
}

.person-info__btn {
	width: 100%;
	border: 2rpx solid #ddd;
	border-radius: 48rpx;
	padding: 18rpx 0;
	display: flex;
	align-self: center;
	justify-content: center;
}
.person-info__wrapper {
	width: 100%;
	padding: 0 32rpx;
	padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
	position: absolute;
	bottom: 0;
	left: 0;
}

.avatarUrl {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50% !important;
	overflow: hidden;
}

.t-class-confirm {
	color: #fa550f !important;
}

.person-info .order-group__left {
	margin-right: 0;
}
.person-info .t-cell-class {
	height: 112rpx;
}

.choose-avatar {
	display: flex;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50% !important;
	overflow: hidden;
	position: relative;
	display: inline-block;
	margin: 0;
	padding: 0;
}

.section2 {
	margin-top: 20rpx;
}
