<wxs module="filter" src="../../../utils/util.wxs"></wxs>

<view class="person-info">
  <view class="seciton1">
    <t-cell-group>
      <t-cell
        title="头像"
        center="{{true}}"
        data-type="avatarUrl"
        arrow
        t-class-left="order-group__left"
      >
        <!-- <t-image slot="note" src="{{personInfo.avatarUrl}}" t-class="avatarUrl" mode="aspectFill" /> -->
        <button slot="note" class="choose-avatar" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
          <image slot="note" src="{{userInfo.avatar}}" t-class="avatarUrl" mode="aspectFill" style="width: 80rpx;height: 80rpx;"/>
        </button>
      </t-cell>
      <t-cell
        title="昵称"
        arrow
        note="{{userInfo.nickname}}"
        data-type="name"
        bind:click="onClickCell"
        t-class="t-cell-class"
        t-class-left="order-group__left"
      >
      </t-cell>
      <!-- <t-cell
        title="性别"
        arrow
        note="{{genderMap[userInfo.gender]}}"
        data-type="gender"
        bind:click="onClickCell"
        t-class="t-cell-class"
        t-class-left="order-group__left"
      /> -->
      <t-cell
        bordered="{{false}}"
        title="手机号"
        arrow
        note="{{userInfo.phone ? userInfo.phone : '去绑定手机号'}}"
        data-type="phone"
        bind:click="onBindPhone"
        t-class="t-cell-class"
        t-class-left="order-group__left"
      />
    </t-cell-group>
  </view>

  <view class="section2" wx:if="{{referrerUser}}">
    <t-cell-group>
      <t-cell
        title="上级邀请人头像"
        center="{{true}}"
        data-type="avatarUrl"
        t-class-left="order-group__left"
      >
        <t-image slot="note" src="{{referrerUser.avatar}}" t-class="avatarUrl" mode="aspectFill" />
      </t-cell>
      <t-cell
        title="上级邀请人昵称"
        note="{{referrerUser.nickname}}"
        data-type="name"
        bind:click="onClickCell"
        t-class="t-cell-class"
        t-class-left="order-group__left"
      >
      </t-cell>
      <t-cell
        title="上级邀请人UID"
        note="{{referrerUser.uid}}"
        data-type="name"
        bind:click="onClickCell"
        t-class="t-cell-class"
        t-class-left="order-group__left"
      >
      </t-cell>
      <t-cell
        title="上级邀请人等级"
        note="LV{{referrerUser.user_level}}"
        data-type="user-level"
        bind:click="onClickCell"
        t-class="t-cell-class"
        t-class-left="order-group__left"
      />
      <t-cell
        bordered="{{false}}"
        title="上级邀请人手机号"
        note="{{filter.maskPhoneNumber(referrerUser.phone)}}"
        data-type="phone"
        bind:click="onBindPhone"
        t-class="t-cell-class"
        t-class-left="order-group__left"
      />
    </t-cell-group>
  </view>

</view>
<!-- <view class="person-info__wrapper">
  <view class="person-info__btn" bind:tap="openUnbindConfirm"> 切换账号登录 </view>
</view> -->
<t-select-picker
  show="{{typeVisible}}"
  picker-options="{{pickerOptions}}"
  title="选择性别"
  value="{{personInfo.gender}}"
  bind:confirm="onConfirm"
  bind:close="onClose"
/>
<t-toast id="t-toast" />
