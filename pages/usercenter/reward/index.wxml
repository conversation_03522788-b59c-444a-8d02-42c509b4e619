<wxs module="filter" src="../../../utils/util.wxs"></wxs>

<t-pull-down-refresh
    value="{{enable}}"
    loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}"
    bind:refresh="onPullDownRefresh"
    bind:scroll="onScroll"
  >
  
  <view class="container">
    <view style="text-align: center; color: #b9b9b9" wx:if="{{pageLoading}}">
      <t-loading theme="circular" size="100rpx" text="加载中..." inherit-color />
    </view>
    <block wx:if="{{!pageLoading && totalHistoryRewards && totalHistoryRewards.length > 0}}">
      <t-cell-group>
        <t-cell
          wx:for="{{totalHistoryRewards}}"
          wx:key="index"
          t-class="reward-item"
          hover
          bind:tap="onTapItem"
          data-item="{{item}}"
        >
          <view slot="left-icon" class="reward-icon">
            <t-icon prefix="wr" name="wechat" size="68rpx" color="#07c160" />
          </view>
          <view slot="title" class="reward-info">
            <view class="reward-type">{{filter.formatTransactionType(item.type)}}</view>
            <view class="reward-time">{{filter.formatDateTime(item.createdAt)}}</view>
          </view>
          <view slot="note" class="reward-amount positive">
            {{item.type == 'withdraw' ? '-' : '+'}}{{item.amount}}
          </view>
        </t-cell>
      </t-cell-group>
    </block>

    <block wx:if="{{ !pageLoading && totalHistoryRewards && totalHistoryRewards.length === 0}}">
      <view class="empty-state" wx:if="{{!pageLoading}}">
        <text class="empty-text" >暂无数据</text>
      </view>
    </block>
   
  </view>

</t-pull-down-refresh>