import { createStoreBindings } from "mobx-miniprogram-bindings";
import { userStore } from "../../../stores/userStore";

// pages/usercenter/reward/index.js
Page({
	data: {
		enable: false,
		pageLoading: true,
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		this.userStoreBindings = createStoreBindings(this, {
			store: userStore,
			fields: ["totalHistoryRewards"],
			actions: ["fetchUserTotalHistoryRewards"],
		});

		this.fetchUserTotalHistoryRewards();
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {
		this.setData({
			pageLoading: false,
		});
	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {
		// 解绑 store
		this.userStoreBindings.destroyStoreBindings();
	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {
		// 模拟刷新数据
		setTimeout(() => {
			this.setData({ enable: false });
		}, 500);
		this.fetchUserTotalHistoryRewards();
	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {},

	onTapItem(e) {
		console.debug(e);
		console.debug(e.currentTarget.dataset.item);
		const item = e.currentTarget.dataset.item;
		wx.navigateTo({
			url: `/pages/usercenter/reward/detail/index?id=${item._id}&transaction_type=${item.type}`,
		});
	},
});
