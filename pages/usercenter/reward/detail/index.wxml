<wxs module="filter" src="../../../../utils/util.wxs"></wxs>

<view class="container">
  <block wx:if="{{transactionDetail.transaction_type === 'withdraw'}}">
    <view class="line">
      该笔交易来源: {{ filter.formatTransactionType(transactionDetail.transaction_type) }} 
    </view>
    
    <view class="line">
      产生时间: {{ filter.formatDateTime(transactionDetail.transaction_time) }}
    </view>
    
    <view class="line">
      金额: - {{ transactionDetail.amount }} 元
    </view>
  </block>

  <block wx:else>
    <view class="line">
      该笔交易来源: {{ filter.formatTransactionType(transactionDetail.transaction_type) }} 
    </view>
    
    <view class="line">
      产生时间: {{ filter.formatDateTime(transactionDetail.transaction_time) }}
    </view>
    
    <view class="line">
      金额: + {{ transactionDetail.amount }} 元
    </view>
    
    <view class="line">
      订单号: {{ transactionDetail.order_no }}
    </view>
    
    <view class="line">
      商家: {{ transactionDetail.shop.name }}
    </view>
    
    <view class="line">
      商品名: {{ transactionDetail.promotion.name }}
    </view>
    
    <view class="line">
      下单人的昵称: {{ transactionDetail.user.nickname }}
    </view>
    
    <view class="line">
      下单人的uid: {{ transactionDetail.user.uid }}
    </view>
    
    <view class="line">
      下单人的手机号: {{ filter.maskPhoneNumber(transactionDetail.user.phone)  }}
    </view>
  </block>
    
</view>

<!--
detail.phone = user.phone
detail.nickname = user.nickname
detail.uid = user.detail
-->
