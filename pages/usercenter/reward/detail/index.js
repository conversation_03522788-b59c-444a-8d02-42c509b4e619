import { createStoreBindings } from "mobx-miniprogram-bindings";
import { userStore } from "../../../../stores/userStore";

Page({
	/**
	 * 页面的初始数据
	 */
	data: {},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		console.debug(options);
		// const transaction_type = options.transaction_type
		// switch (transaction_type) {
		//   case "referral_order_commission":
		//     break;
		//   default:
		//     break;
		// }
		this.userStoreBindings = createStoreBindings(this, {
			store: userStore,
			fields: ["transactionDetail"],
			actions: ["fetchTransactionDetail"],
		});

		this.fetchTransactionDetail({
			id: options.id,
			transaction_type: options.transaction_type,
		});
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {
		// 解绑 store
		this.userStoreBindings.destroyStoreBindings();
	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {},
});
