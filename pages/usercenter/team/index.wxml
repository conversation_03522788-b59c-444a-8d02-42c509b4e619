<wxs module="filter" src="../../../utils/util.wxs"></wxs>

<view class="team-container">
  <!-- 标签页导航 -->
  <t-tabs
    defaultValue="{{activeTab}}"
    value="{{activeTab}}"
    bind:change="onTabsChange"
    t-class="tabs"
    t-class-active="tabs-active"
    t-class-track="tabs-track"
  >
    <t-tab-panel label="全部" value="all" />
    <t-tab-panel label="有效团员" value="active" />
    <t-tab-panel label="未激活团员" value="inactive" />
  </t-tabs>

<t-pull-down-refresh
  value="{{enable}}"
  loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}"
  usingCustomNavbar
  bind:refresh="onPullDownRefresh"
  bind:scrolltolower="onReachBottom"
>
  <!-- 内容区域 -->
  <view class="team-content">
    <!-- 加载状态 -->
    <view class="loading-wrapper" wx:if="{{loading}}">
      <t-loading theme="circular" size="40rpx" text="加载中..." inherit-color />
    </view>

    <!-- 空状态 - 使用TDesign组件 -->
    <view class="empty-wrapper" wx:if="{{!loading && teamMembers.length === 0}}">
      <t-empty 
        icon="info-circle-filled" 
        description="暂无团员" 
      />
    </view>

    <!-- 团员列表 - 当有数据时展示 -->
    <view class="team-list" wx:if="{{!loading && teamMembers.length > 0}}">
      <block wx:for="{{teamMembers}}" wx:key="_id">
        <view class="team-item">
          <view class="member-info">
            <t-avatar class="avatar" image="{{item.avatar}}" size="medium" />
            <view class="info-right">
              <view class="nickname">{{item.nickname}}</view>
              <view class="join-time">加入时间: {{filter.formatDateTime(item.createdAt)}}</view>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>

</t-pull-down-refresh>
</view>
