import { createStoreBindings } from "mobx-miniprogram-bindings";
import { userStore } from "../../../stores/userStore";

Page({
	data: {
		activeTab: "all", // 默认选中"有效团员"标签页
		teamMembers: [], // 团员列表，当前为空
		loading: false, // 是否正在加载数据
	},

	onLoad: function (options) {
		// 将 store 绑定到页面
		this.userStoreBindings = createStoreBindings(this, {
			store: userStore,
			fields: ["teamMembers", "total", "pageSize", "pageNumber", "status"],
			actions: ["setPageNumber", "setStatus", "fetchTeamMembers"],
		});

		console.debug("options:", options.tab);
		// 如果有参数指定标签页，则切换到指定标签页
		if (options.tab) {
			console.debug("如果有参数指定标签页，则切换到指定标签页");
			this.setData({
				activeTab: options.tab,
			});
		}

		this.fetchTeamMembers({ status: this.data.activeTab });
		// // 加载团员数据
		// this.loadTeamMembers();
	},

	onUnload() {
		this.userStoreBindings.destroyStoreBindings();
	},

	// 切换标签页
	onTabsChange(e) {
		console.debug(e.detail.value);
		const activeTab = e.detail.value;
		this.setData({
			activeTab,
			// loading: true
		});
		// this.loadTeamMembers();
		this.fetchTeamMembers({ status: e.detail.value });
	},

	// 加载团员数据
	loadTeamMembers() {
		const { activeTab } = this.data;

		if (!this.data.loading) {
			this.setData({ loading: true });
		}

		// 模拟请求后端数据
		setTimeout(() => {
			// 这里应该是实际的API请求
			// 目前使用空数组模拟暂无数据的情况
			const teamMembers = [];

			this.setData({
				teamMembers,
				loading: false,
			});

			// 如果是下拉刷新触发的，停止下拉刷新动画
			wx.stopPullDownRefresh();
		}, 1500); // 延长加载时间以便观察加载效果
	},

	// 下拉刷新
	onPullDownRefresh(e) {
		console.debug("onPullDownRefresh");
		console.debug(this.data.activeTab);
		this.setData({ enable: true });
		setTimeout(() => {
			this.setData({ enable: false });
		}, 1000);

		// this.init(this.data.status);
		// this.fetchOrders(this.data.status);
		// this.loadTeamMembers();
		this.fetchTeamMembers({ status: this.data.activeTab });
	},

	// 跳转到团员详情页
	navigateToMemberDetail(e) {
		const { id } = e.currentTarget.dataset;
		wx.navigateTo({
			url: `/pages/usercenter/team/detail/index?id=${id}`,
		});
	},
});
