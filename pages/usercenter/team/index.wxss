page {
  background-color: #f5f5f5;
}

.team-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 标签页样式 */
.tabs {
  background-color: #fff;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 28rpx;
  color: #666;
}

.tabs-active {
  color: #fa550f !important;
  font-weight: bold;
}

.tabs-track {
  background-color: #fa550f !important;
  height: 6rpx !important;
  border-radius: 6rpx !important;
  width: 40rpx !important;
}

/* 内容区域 */
.team-content {
  flex: 1;
  padding: 0 30rpx;
  margin-top: 20rpx;
}

/* 加载状态 */
.loading-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120rpx;
  margin-top: 30rpx;
}

/* 空状态 */
.empty-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 700rpx;
}

.empty-box {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
}

/* 团员列表 */
.team-list {
  padding: 20rpx 0;
}

.team-item {
  padding: 24rpx;
  background: #fff;
  margin-bottom: 24rpx;
  border-radius: 12rpx;
}

.member-info {
  display: flex;
  align-items: center;
}

.info-right {
  margin-left: 24rpx;
}

.nickname {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.join-time {
  font-size: 24rpx;
  color: #999;
}