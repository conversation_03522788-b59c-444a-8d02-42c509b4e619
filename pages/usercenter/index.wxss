page {
	background-color: #f5f5f5;
}

.content-wrapper {
	margin-top: 340rpx;
	position: relative;
	padding: 0 30rpx;
	padding-bottom: 140rpx;
}

.main-content {
	height: 500rpx;
}

/* 会员信息卡片 */
.member-card {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: #ffffff;
	padding: 24rpx 30rpx;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
}

.member-info {
	display: flex;
	align-items: center;
}

.member-icon {
	width: 60rpx;
	height: 60rpx;
	margin-right: 16rpx;
}

.member-icon image {
	width: 100%;
	height: 100%;
}

.member-level {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.member-action {
	display: flex;
	align-items: center;
	color: #666;
	font-size: 28rpx;
}

/* 卡券和红包区域 */
.coupon-area {
	display: flex;
	background: #ffffff;
	padding: 16rpx 30rpx;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
}

.coupon-item {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
}

.coupon-icon {
	width: 50rpx;
	height: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 10rpx;
}

.coupon-text {
	font-size: 28rpx;
	color: #333;
}

.badge {
	color: #ff4e42;
}

.divider {
	width: 2rpx;
	background-color: #eeeeee;
	margin: 0 30rpx;
}

/* 收益区域 - 精确匹配图片 */
.revenue-card {
	background: #ffffff;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
}

.revenue-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-bottom: 16rpx;
	margin-bottom: 10rpx;
	border-bottom: 1rpx solid #f2f2f2;
}

.revenue-title {
	display: flex;
	align-items: center;
}

.revenue-title text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-right: 12rpx;
}

.revenue-tag {
	font-size: 22rpx;
	color: #999;
	background-color: #f5f5f5;
	padding: 4rpx 10rpx;
	border-radius: 8rpx;
}

.revenue-detail {
	display: flex;
	align-items: center;
	color: #666;
	font-size: 26rpx;
}

.revenue-icons {
	display: flex;
	justify-content: space-between;
	padding: 16rpx 40rpx 24rpx;
}

.revenue-icon-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	min-width: 120rpx;
}

.revenue-icon-item t-icon {
	margin-bottom: 8rpx;
}

.revenue-icon-item text {
	font-size: 24rpx;
	color: #666;
}

.revenue-amounts {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 40rpx 24rpx;
	border-bottom: 1rpx solid #f2f2f2;
}

.revenue-number {
	font-size: 46rpx;
	font-weight: bold;
	color: #ff7000;
	display: flex;
	min-width: 120rpx;
	justify-content: center;
}

.revenue-button {
	background-color: #ff7000;
	color: #ffffff;
	font-size: 28rpx;
	padding: 6rpx 36rpx;
	border-radius: 30rpx;
	line-height: 1.6;
	border: none;
	font-weight: normal;
	min-height: unset;
	width: 200rpx;
	margin: 0;
}

.revenue-links {
	padding: 6rpx 0;
}

.revenue-link-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 18rpx 10rpx;
	font-size: 28rpx;
	color: #333;
}

.link-right {
	display: flex;
	align-items: center;
}

.link-right text {
	font-weight: bold;
	margin-right: 8rpx;
}

/* 团队数据 */
.revenue-team {
	padding: 0 20rpx;
}

.team-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid #eeeeee;
}

.team-row:last-child {
	border-bottom: none;
}

.team-name {
	font-size: 26rpx;
	color: #666;
}

.team-arrow {
	display: flex;
	align-items: center;
}

.team-num {
	font-size: 26rpx;
	font-weight: bold;
	color: #333;
	margin-right: 8rpx;
}

/* 订单区域 */
.order-group-wrapper {
	background-color: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	padding: 24rpx 30rpx;
}

.order-group-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-bottom: 24rpx;
	border-bottom: 1rpx solid #eeeeee;
}

.order-group-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.order-group-all {
	display: flex;
	align-items: center;
	color: #666;
	font-size: 28rpx;
}

.order-content {
	display: flex;
	justify-content: space-between;
}

.order-item {
	flex: 1;
	height: 180rpx;
	overflow: hidden;
	position: relative;
	text-align: center;
}

.order-content-box {
	margin: auto;
	position: absolute;
	width: 100%;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	padding: 20rpx 0;
}

.order-content-t {
	margin-top: 10rpx;
	font-size: 24rpx;
	color: #333;
	letter-spacing: 0;
	text-align: center;
}

/* 其他样式 */
.cell-box {
	border-radius: 10rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
}

.icon-color {
	color: #aaa;
}

.cell-class {
	height: 100rpx;
	display: flex;
	align-items: center;
}

.popup-content {
	background: #f5f5f5;
	margin-bottom: env(safe-area-inset-bottom);
	border-radius: 16rpx 16rpx 0 0;
}

.popup-content .popup-title {
	background: #fff;
	text-align: center;
	font-size: 24rpx;
	color: #999;
	height: 112rpx;
	text-align: center;
	line-height: 112rpx;
	border-radius: 16rpx 16rpx 0 0;
}

.border-bottom-1px {
	position: relative;
}

.border-bottom-1px::after {
	position: absolute;
	display: block;
	content: "";
	box-sizing: border-box;
	top: 0;
	left: 0;
	width: 200%;
	height: 200%;
	transform: scale(0.5);
	transform-origin: left top;
	border-bottom: 2rpx solid #e5e5e5;
}

.popup-content .popup-phone,
.popup-content .popup-close {
	background: #fff;
	height: 100rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	text-align: center;
	font-size: 30rpx;
	font-family: PingFangSC-Regular, PingFang SC;
	font-weight: 400;
	color: #333;
}

.popup-content .popup-phone.online {
	margin-bottom: 20rpx;
}

.popup-content .popup-phone.online::after {
	content: none;
}

.popup-content .popup-close {
	color: #333;
	border: 0;
	margin-top: 16rpx;
}

.my-order {
	border-radius: 10rpx;
}

.footer__version {
	text-align: center;
	margin-top: 80rpx;
	color: #999;
	margin-bottom: 84rpx;
	font-size: 24rpx;
	line-height: 32rpx;
}

.cell-box .order-group__left {
	margin-right: 0;
}

.cell-box .t-cell-padding {
	padding: 24rpx 18rpx 24rpx 32rpx;
}

.loading-wrapper {
	/* display: flex;
  justify-content: center;
  align-items: center; */
	width: 100%;
}

.grids {
	margin-bottom: 20rpx;
	border-radius: 16rpx;
}

.t-grid-item__image {
	background: none !important;
}

.t-toast {
	--td-toast-max-width: 800rpx;
}
