import { createStoreBindings } from "mobx-miniprogram-bindings";
import { userStore } from "../../../stores/userStore";

// pages/usercenter/favorites/index.js
Page({
	/**
	 * 页面的初始数据
	 */
	data: {},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		this.userStoreBindings = createStoreBindings(this, {
			store: userStore,
			fields: ["favorites"], // 需要绑定的字段
			actions: ["isShopFavorited", "toggleFavorite", "fetchFavorites"], // 需要绑定的 actions
		});

		this.fetchFavorites();
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {
		this.userStoreBindings.destroyStoreBindings();
	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onRefresh() {
		this.setData({
			enable: true,
		});
		this.fetchFavorites();
		setTimeout(() => {
			this.setData({
				enable: false,
			});
		}, 1000);
		// wx.hideLoading();
	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {},

	handleTapStore(e) {
		this.setData({
			backRefresh: false,
		});
		const storeId = e.currentTarget.dataset.storeId;
		wx.navigateTo({
			url: `/pages/store/index?id=${storeId}`,
		});
	},
});
