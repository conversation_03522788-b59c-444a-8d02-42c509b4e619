.result-container {
	display: block;
	padding: 0 24rpx;
	/* background: #f5f5f5; */
	background-color: #f7f8fa;
	padding-top: 20rpx;
}

/* 商品列表 */
.container {
	padding: 0;
	margin: 0;
	min-height: 100vh;
	margin-top: 20rpx;
}

/* 加载状态 */
.loading-container {
	display: flex;
	justify-content: center;
	padding: 24rpx 0;
}

.store-list {
	/* padding: 12rpx 0; */
	margin-top: 12rpx;
}

/* 商家卡片样式 - 紧凑版 */
.store-card {
	margin-bottom: 20rpx;
	background-color: #fff;
	border-radius: 18rpx;
	/* overflow: hidden; */
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	/* transition: transform 0.2s; */
}

.store-hover {
	transform: scale(0.98);
	background-color: #fafafa;
}

/* 店铺信息紧凑版样式 */
.store-info-compact {
	padding: 20rpx;
	border-bottom: 1rpx solid #f2f2f2;
	display: flex;
}

/* 商店头部行 */
.store-header-row {
	display: flex;
	/* justify-content: space-between; */
	/* align-items: center; */
	align-items: flex-start;
	/* margin-bottom: 8rpx; */
	flex-direction: column;
	gap: 10rpx;
}

.store-left {
	display: flex;
	align-items: center;
	flex: 1;
	overflow: hidden;
}

.store-logo {
	display: flex;
	margin-top: 7rpx;
}

.logo-image-small {
	width: 60rpx;
	height: 60rpx;
	/* border-radius: 50%; */
	margin-right: 12rpx;
	flex-shrink: 0;
}

.store-name-compact {
	font-size: 26rpx;
	font-weight: bold;
	color: #333;
	margin-right: 10rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 400rpx;
	padding-left: 8rpx;
}

.store-tag-compact {
	font-size: 20rpx;
	color: #ff5f15;
	/* background-color: #f7f7f7; */
	padding: 2rpx 8rpx;
	border-radius: 4rpx;
	flex-shrink: 0;
}

.store-right {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	margin-left: auto;
	gap: 15rpx;
}

.location-distance {
	display: flex;
	align-items: center;
	padding-top: 4rpx;
}

.location-text {
	font-size: 20rpx;
	color: #666;
	margin-right: 8rpx;
}

.distance-text {
	font-size: 20rpx;
	color: #999;
}

/* 第二行：排名 + 标签 */
.store-meta-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.store-meta-left {
	display: flex;
	align-items: center;
	flex: 1;
	overflow: hidden;
	margin-left: 70rpx;
}

.popularity-text-compact {
	font-size: 20rpx;
	color: #ff5f15;
	background-color: rgba(255, 95, 21, 0.1);
	padding: 2rpx 8rpx;
	border-radius: 6rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.badge-container {
	display: flex;
	align-items: center;
}

.badge-compact {
	font-size: 20rpx;
	color: #999;
	/* background-color: #f7f7f7; */
	/* padding: 2rpx 8rpx; */
	/* margin-left: 8rpx; */
	border-radius: 4rpx;
	display: flex;
	/* gap: 10rpx; */
	align-items: center;
	color: #ff5f15;
	gap: 2rpx;
}

.badge-compact t-icon {
	margin-right: 4rpx;
}

/* 食品列表样式 */
.food-list {
	padding: 0;
}

.food-card {
	display: flex;
	padding: 26rpx 20rpx;
	border-bottom: 1rpx solid #f5f5f5;
	position: relative;
}

.food-card:last-child {
	border-bottom: none;
}

.food-card-hover {
	background-color: #fafafa;
	transform: scale(0.98);
}

.food-image-container {
	width: 200rpx;
	height: 180rpx;
	margin-right: 20rpx;
	flex-shrink: 0;
	position: relative;
	overflow: hidden;
	border-radius: 8rpx;
}

.food-image {
	width: 100%;
	height: 100%;
	border-radius: 8rpx;
	transition: transform 0.3s;
}

.food-image:hover {
	transform: scale(1.05);
}

.food-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	/* height: 180rpx; */
	position: relative;
}

/* 标签和份数行 */
.food-top-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	margin-bottom: 8rpx;
}

/* 食品标签 */
.food-tag-compact {
	display: inline-flex;
	align-items: center;
	background-color: #fde7e2;
	color: #ff5f15;
	font-size: 22rpx;
	padding: 4rpx 10rpx;
	border-radius: 6rpx;
	font-weight: 500;
	flex-shrink: 0;
}

/* 剩余份数标签 */
.coupon-count-container {
	display: flex;
	align-items: center;
	flex-shrink: 0;
}

.brand-icon1 {
	margin: 0;
	padding: 0;
	margin-right: 10rpx;
	display: flex;
	align-items: center;
}

.coupon-count {
	font-size: 22rpx;
	color: #ff5f15;
	padding: 4rpx 10rpx;
	/* background-color: rgba(255, 95, 21, 0.08); */
	border-radius: 4rpx;
	display: inline-block;
}

/* 食品名称 */
.food-title-area {
	margin-bottom: 12rpx;
	width: 100%;
}

.food-name-compact {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* 价格信息和按钮 */
.food-footer-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 50rpx;
}

.quantity-info {
	position: absolute;
	right: 20rpx;
	bottom: 26px;
	font-size: 24rpx;
	color: #ff5f15;
}

.price-discount-group {
	display: flex;
	align-items: center;
}

.current-price {
	font-size: 32rpx;
	color: #ff5f15;
	font-weight: bold;
	margin-right: 8rpx;
}

.original-price {
	font-size: 22rpx;
	color: #999;
	text-decoration: line-through;
	margin-right: 10rpx;
}

.discount-tag-compact {
	display: flex;
	align-items: center;
	border: 1rpx solid #ff5f15;
	border-radius: 6rpx;
	background-color: rgba(255, 95, 21, 0.08);
	padding: 2rpx 8rpx;
}

.discount-tag-compact text {
	font-size: 20rpx;
	color: #ff5f15;
}

.buy-button {
	background: linear-gradient(to right, #ff7a45, #ff5f15);
	color: #fff;
	font-size: 24rpx;
	padding: 6rpx 24rpx;
	border-radius: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(255, 95, 21, 0.3);
	transition: transform 0.2s;
}

.buy-button-hover {
	transform: scale(0.95);
	opacity: 0.9;
}

.soldout {
	background: #b6b6b6 !important;
	box-shadow: none;
}

.empty-state {
	margin-top: 80rpx;
}

.empty-text {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 20rpx;
}
