<wxs module="filter" src="../../../utils/util.wxs"></wxs>

<t-pull-down-refresh value="{{enable}}" loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}" usingCustomNavbar
  bind:refresh="onRefresh" bind:scroll="onScroll">

  <view class="result-container">
    <view class="container">
      <!-- 顶部加载状态 -->
      <!-- <view class="loading-container" wx:if="{{shopsLoadStatus===1}}">
      <t-loading theme="circular" size="48rpx" text="数据加载中..." />
    </view> -->

      <view class="store-list">
        <block wx:for="{{favorites}}" wx:key="_id" wx:for-item="shop">
          <!-- 店铺卡片 -->
          <view class="store-card">
            <!-- 店铺信息 - 紧凑版 -->
            <view class="store-info-compact" hover-class="store-hover" bindtap="handleTapStore"
              data-store-id="{{shop._id}}">

              <view class="store-logo">
                <t-image src="{{shop.logo}}" class="logo-image-small" shape="square" loading="slot" width="34"
                  height="34">
                  <t-loading slot="loading" theme="spinner" size="30rpx" loading />
                </t-image>
              </view>

              <view class="store-header-row">
                <view class="store-name-compact">{{shop.name}}</view>
                <view class="store-tag-compact">{{shop.tag}}</view>
              </view>

              <view class="store-right">
                <view class="location-distance">
                  <text class="location-text">{{shop.district}}</text>
                  <text class="distance-text">{{filter.formatDistanceInMeter(shop.distance)}}</text>
                </view>

                <!-- <view class="badge-container">
                <block >
                  <view class="badge-compact">
                    <t-icon name="swap" size="20rpx" color="#FF5F15" /> 随时退
                    <t-icon name="time" size="20rpx" color="#FF5F15" /> 过期退
                  </view>
                </block>
              </view> -->
              </view>
              <!-- 第二行：排名 + 标签 -->
              <!-- <view class="store-meta-row">
              <view class="store-meta-left">
                <text class="popularity-text-compact">{{shop.tag}}</text>
              </view>
              <view class="badge-container">
                <block >
                  <view class="badge-compact">
                    <t-icon name="swap" size="20rpx" color="#FF5F15" /> 随时退
                    <t-icon name="time" size="20rpx" color="#FF5F15" /> 过期退
                  </view>
                </block>
              </view>
            </view> -->
            </view>
          </view>
        </block>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{shops.length === 0 && shopsLoadStatus != 1}}">
        <t-icon name="shop" size="80rpx" color="#cccccc" />
        <text class="empty-text">没有更多了</text>
      </view>

      <load-more list-is-empty="{{!shops.length}}" status="{{shopsLoadStatus}}" bind:retry="onReTry" />
    </view>
  </view>

</t-pull-down-refresh>