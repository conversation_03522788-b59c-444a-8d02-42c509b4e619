<view class="name-edit">
  <t-input
    borderless
    model:value="{{nickname}}"
    placeholder="请输入文字"
    label="昵称"
    clearable
    type="nickname"
    bind:clear="clearContent"
  />
  <!-- <input type="nickname" class="weui-input" placeholder-class="input" placeholder="请输入昵称" value="{{personInfo.nickName}}" bind:change="onChangeNickName"/> -->
  <view class="name-edit__input--desc"> 最多可输入15个字 </view>
  <view class="name-edit__wrapper">
    <t-button block theme="primary" shape="round" disabled="{{!nickname}}" bind:tap="onSubmit">保存</t-button>
  </view>
</view>
