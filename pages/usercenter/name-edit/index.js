import { createStoreBindings } from "mobx-miniprogram-bindings";
import { authStore } from "../../../stores/authStore";

Page({
	data: {
		nickname: "",
	},
	onLoad(options) {
		const { name } = options;
		this.setData({
			nickname: name,
		});

		this.storeBindings = createStoreBindings(this, {
			store: authStore,
			fields: ["userInfo"],
			actions: ["saveNickname"],
		});
	},
	onUnload() {
		this.storeBindings.destroyStoreBindings();
	},
	async onSubmit() {
		const { nickname } = this.data;
		console.debug(nickname);

		await this.saveNickname(nickname);

		// return

		wx.navigateBack({ backRefresh: true });
	},
	clearContent() {
		this.setData({
			nickname: "",
		});
	},
});
