// pages/invite/index.js
import { createStoreBindings } from "mobx-miniprogram-bindings";
import { authStore } from "../../stores/authStore";
import { orderStore } from "../../stores/orderStore";
import { userStore } from "../../stores/userStore";
Page({
	/**
	 * 页面的初始数据
	 */
	data: {
		// 团长收益数据
		leaderIncome: 0,
		// 团员数据
		activeMembers: 0,
		inactiveMembers: 0,
		// 等级规则
		levelRules: [
			{ level: "LV7-LV8", reward: "10元" },
			{ level: "LV5-LV6", reward: "6元" },
			{ level: "LV1-LV4", reward: "3元" },
		],
		// 粉丝规则
		fansRules: [
			{ range: "5001粉以上", reward: "10元" },
			{ range: "1001-5000粉", reward: "6元" },
			{ range: "0-1000粉", reward: "3元" },
		],
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad() {
		this.userStoreBindings = createStoreBindings(this, {
			store: userStore,
			fields: [
				"effective_members_count",
				"ineffective_members_count",
				"totalWithdrawedAmount",
				"totalReferralRewards",
			],
			actions: [
				"fetchTeamMemberCounts",
				"fetchTotalWithdrawedAmount",
				"fetchReferralRewards",
			],
		});
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {
		this.getTabBar().init();

		this.fetchTeamMemberCounts();
		this.fetchReferralRewards();
	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {
		this.userStoreBindings.destroyStoreBindings();
	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {
		console.debug("onShareAppMessage");
		let referrer_id = null;
		const userInfo = wx.getStorageSync("dgx-auth");
		if (userInfo) {
			referrer_id = userInfo.user_id;
		}
		return {
			title: "优探生活招募素人探店，全城免费吃！", // 分享标题
			path: `/pages/home/<USER>// 分享路径，通常是当前页面路径
			imageUrl:
				"cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-1321286342/app/share.jpg", // 分享图片
		};
	},

	// 跳转到团长收益
	gotoReferralRewards() {
		wx.navigateTo({
			url: "/pages/usercenter/reward/index",
		});
	},

	// 跳转到有效团员
	gotoActiveMembers() {
		wx.navigateTo({
			url: "/pages/usercenter/team/index?tab=active",
		});
	},

	// 跳转到未激活团员
	gotoInactiveMembers() {
		wx.navigateTo({
			url: "/pages/usercenter/team/index?tab=inactive",
		});
	},
});
