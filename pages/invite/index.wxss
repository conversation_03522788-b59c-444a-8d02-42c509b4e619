.invite-container {
	min-height: 100vh;
	padding: 20rpx;
	background: #f5f5f5;
	padding-top: 40rpx;
	padding-bottom: 100rpx;
}

.invite-banner {
	/* position: relative;
  background-image: url('https://636c-cloud1-0gpy573m8caa7db3-1321286342.tcb.qcloud.la/app/bg2.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat; */
	border-radius: 20rpx;
	/* padding: 40rpx 30rpx; */
	/* color: #fff; */
	margin-bottom: 40rpx;
	/* min-height: 300rpx; */
}

.banner-icon {
	width: 740rpx;
}

.banner-title {
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.banner-subtitle {
	font-size: 28rpx;
	margin-bottom: 20rpx;
}

.banner-button {
	position: absolute;
	right: 20rpx;
	top: 20rpx;
	background: rgba(255, 255, 255, 0.2);
	padding: 10rpx 20rpx;
	border-radius: 30rpx;
	font-size: 24rpx;
}

.stats-card {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.stats-item {
	text-align: center;
	flex: 1;
}

.stats-label {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.stats-value {
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
}

.reward-tip {
	background: #fff;
	border-radius: 20rpx;
	padding: 20rpx;
	padding-left: 40rpx;
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.tip-icon {
	width: 40rpx;
	height: 40rpx;
	background: #ff6347;
	border-radius: 50%;
	margin-right: 10rpx;
}

.tip-text {
	font-size: 26rpx;
	color: #666;
	margin-right: auto;
}

.tip-value {
	font-size: 26rpx;
	color: #ff6347;
	margin-right: 20rpx;
}

.invite-button {
	background: #ff6347;
	color: #fff;
	border-radius: 30rpx;
	font-size: 24rpx;
	margin-left: auto;
	margin-right: 0;
}

.rules-section {
	background: #fff;
	border-radius: 20rpx;
	padding: 20rpx 40rpx;
	margin-bottom: 20rpx;
}

.rules-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.rules-list {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.rule-item {
	display: flex;
	justify-content: space-between;
	font-size: 26rpx;
	color: #666;
	padding: 10rpx 0;
}

.bottom-note {
	font-size: 24rpx;
	color: #999;
	text-align: center;
	margin: 40rpx 0;
	margin-bottom: 120rpx;
}

.member-list {
	margin-bottom: 200rpx;
}

.empty {
	display: flex;
	align-items: items-center;
	justify-content: center;
	color: gray;
	padding-top: 80rpx;
}

.member-list-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	background: #fff;
	border-radius: 20rpx 20rpx 0 0;
}

.member-list-items {
	min-height: 200rpx;
	background: #fff;
}

.refresh-icon {
	font-size: 24rpx;
	color: #666;
}

.brand-icon {
	margin-right: 10rpx;
}

.hl {
	color: #fa8c16;
}
