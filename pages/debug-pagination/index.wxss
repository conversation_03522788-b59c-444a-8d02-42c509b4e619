.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.debug-panel {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.debug-info {
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.action-btn {
  flex: 1;
  min-width: 200rpx;
  padding: 20rpx;
  border: none;
  border-radius: 8rpx;
  background-color: #007aff;
  color: white;
  font-size: 26rpx;
  margin-bottom: 10rpx;
}

.shop-list {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.list-header {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.shop-item {
  padding: 15rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
  background-color: #fafafa;
}

.shop-info {
  display: flex;
  flex-direction: column;
}

.shop-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.shop-distance,
.shop-promotions {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.empty-state {
  text-align: center;
  padding: 60rpx;
  color: #999;
  font-size: 28rpx;
}

.load-more-info {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #eee;
  border-radius: 4rpx;
  margin-top: 10rpx;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: #007aff;
  transition: width 0.3s ease;
}
