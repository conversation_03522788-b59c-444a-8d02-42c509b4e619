import { createStoreBindings } from "mobx-miniprogram-bindings";
import { productStore } from "../../stores/index";

Page({
  data: {
    loadStatusText: "初始状态"
  },

  onLoad() {
    this.bindMbxStores();
    this.updateLoadStatusText();
  },

  onUnload() {
    this.productStoreBindings.destroyStoreBindings();
  },

  bindMbxStores() {
    this.productStoreBindings = createStoreBindings(this, {
      store: productStore,
      fields: [
        "shops_home",
        "page_number",
        "page_size",
        "total",
        "shopsLoadStatus",
        "selectedCategory",
      ],
      actions: [
        "getNearestShops",
        "setPageNumber",
        "setTotal",
        "resetShops",
      ],
    });
  },

  // 加载第一页
  async loadFirstPage() {
    console.log("=== 加载第一页 ===");
    this.resetData();
    this.setPageNumber(1);
    
    try {
      await this.getNearestShops("0", "tandian1");
      this.updateLoadStatusText();
      console.log("第一页加载完成:", {
        shops_count: this.data.shops_home.length,
        total: this.data.total,
        page_number: this.data.page_number
      });
    } catch (error) {
      console.error("加载第一页失败:", error);
    }
  },

  // 加载下一页
  async loadNextPage() {
    console.log("=== 加载下一页 ===");
    console.log("当前状态:", {
      shops_count: this.data.shops_home.length,
      total: this.data.total,
      page_number: this.data.page_number,
      shopsLoadStatus: this.data.shopsLoadStatus
    });

    if (this.data.shopsLoadStatus === 1) {
      console.log("正在加载中，跳过");
      return;
    }

    if (this.data.total > 0 && this.data.shops_home.length >= this.data.total) {
      console.log("已加载所有数据");
      return;
    }

    try {
      await this.getNearestShops("0", "tandian1");
      this.updateLoadStatusText();
      console.log("下一页加载完成:", {
        shops_count: this.data.shops_home.length,
        total: this.data.total,
        page_number: this.data.page_number
      });
    } catch (error) {
      console.error("加载下一页失败:", error);
    }
  },

  // 重置数据
  resetData() {
    console.log("=== 重置数据 ===");
    this.resetShops();
    this.setPageNumber(1);
    this.setTotal(0);
    this.updateLoadStatusText();
  },

  // 模拟触底加载
  async simulateReachBottom() {
    console.log("=== 模拟触底加载 ===");
    
    // 检查是否还有更多数据可以加载
    if (this.data.shopsLoadStatus === 1) {
      console.log("正在加载中，跳过");
      return;
    }

    if (this.data.total > 0 && this.data.shops_home.length >= this.data.total) {
      console.log("已加载所有数据，没有更多了");
      return;
    }

    console.log("开始加载更多数据");
    await this.loadNextPage();
  },

  // 更新加载状态文本
  updateLoadStatusText() {
    const status = this.data.shopsLoadStatus;
    let statusText = "";
    
    switch(status) {
      case 0:
        statusText = "可以加载更多";
        break;
      case 1:
        statusText = "加载中...";
        break;
      case 2:
        statusText = "没有更多数据";
        break;
      case 3:
        statusText = "加载失败";
        break;
      default:
        statusText = "未知状态";
    }

    this.setData({
      loadStatusText: statusText
    });
  },

  // 监听数据变化
  onShow() {
    this.updateLoadStatusText();
  }
});
