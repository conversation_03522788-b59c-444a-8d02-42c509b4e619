<view class="container">
  <view class="debug-panel">
    <text class="title">分页调试面板</text>
    
    <view class="debug-info">
      <view class="info-item">
        <text class="label">当前店铺数量:</text>
        <text class="value">{{shops_home.length}}</text>
      </view>
      <view class="info-item">
        <text class="label">总数:</text>
        <text class="value">{{total}}</text>
      </view>
      <view class="info-item">
        <text class="label">当前页码:</text>
        <text class="value">{{page_number}}</text>
      </view>
      <view class="info-item">
        <text class="label">每页大小:</text>
        <text class="value">{{page_size}}</text>
      </view>
      <view class="info-item">
        <text class="label">加载状态:</text>
        <text class="value">{{loadStatusText}}</text>
      </view>
      <view class="info-item">
        <text class="label">选中分类:</text>
        <text class="value">{{selectedCategory}}</text>
      </view>
    </view>

    <view class="actions">
      <button class="action-btn" bindtap="loadFirstPage">加载第一页</button>
      <button class="action-btn" bindtap="loadNextPage">加载下一页</button>
      <button class="action-btn" bindtap="resetData">重置数据</button>
      <button class="action-btn" bindtap="simulateReachBottom">模拟触底</button>
    </view>
  </view>

  <view class="shop-list">
    <view class="list-header">
      <text>店铺列表 ({{shops_home.length}}/{{total}})</text>
    </view>
    
    <view class="shop-item" wx:for="{{shops_home}}" wx:key="_id">
      <view class="shop-info">
        <text class="shop-name">{{index + 1}}. {{item.name}}</text>
        <text class="shop-distance">距离: {{item.distance}}</text>
        <text class="shop-promotions">促销: {{item.promotions ? item.promotions.length : 0}}</text>
      </view>
    </view>

    <view class="empty-state" wx:if="{{shops_home.length === 0}}">
      <text>暂无数据</text>
    </view>
  </view>

  <view class="load-more-info">
    <text>加载状态: {{loadStatusText}}</text>
    <view class="progress-bar">
      <view class="progress" style="width: {{(shops_home.length / total * 100) || 0}}%"></view>
    </view>
  </view>
</view>
