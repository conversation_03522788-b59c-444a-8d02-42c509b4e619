<!-- 分层购物车 -->
<block wx:if="{{cartGroupData.isNotEmpty}}">
  <cart-group
    store-goods="{{ cartGroupData.storeGoods }}"
    invalid-good-items="{{ cartGroupData.invalidGoodItems }}"
    bindselectgoods="onGoodsSelect"
    bindselectstore="onStoreSelect"
    bindchangequantity="onQuantityChange"
    bindgocollect="goCollect"
    bindgoodsclick="goGoodsDetail"
    bindclearinvalidgoods="clearInvalidGoods"
    binddelete="onGoodsDelete"
  />

  <view class="gap" />
  <!-- 商品小计以及结算按钮 -->
  <cart-bar
    is-all-selected="{{cartGroupData.isAllSelected}}"
    total-amount="{{cartGroupData.totalAmount}}"
    total-goods-num="{{cartGroupData.selectedGoodsCount}}"
    total-discount-amount="{{cartGroupData.totalDiscountAmount}}"
    fixed="{{true}}"
    bottomHeight="{{112}}"
    bindhandleSelectAll="onSelectAll"
    bindhandleToSettle="onToSettle"
  />
</block>
<!-- 购物车空态 -->
<cart-empty wx:else bind:handleClick="onGotoHome" />
<t-toast id="t-toast" />
<t-dialog id="t-dialog" />
