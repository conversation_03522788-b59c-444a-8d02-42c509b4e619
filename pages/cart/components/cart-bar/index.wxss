.cart-bar__placeholder {
  height: 100rpx;
}
.flex {
  display: flex;
}
.flex-v-center {
  align-items: center;
}
.flex1 {
  flex: 1;
}
.algin-bottom {
  text-align: end;
}
.cart-bar--fixed {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 99;
  bottom: calc(100rpx + env(safe-area-inset-bottom));
}

.cart-bar {
  height: 112rpx;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  padding: 16rpx 32rpx;
  box-sizing: border-box;
  font-size: 24rpx;
  line-height: 36rpx;
  color: #333;
}

.cart-bar .cart-bar__check {
  margin-right: 12rpx;
}

.cart-bar .cart-bar__total {
  margin-left: 24rpx;
}

.cart-bar .account-btn {
  width: 192rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #fa4126;
  font-size: 28rpx;
  font-weight: bold;
  line-height: 80rpx;
  color: #ffffff;
  text-align: center;
}
.cart-bar .disabled-btn {
  background-color: #cccccc !important;
}
.cart-bar .hover-btn {
  opacity: 0.5;
}

.cart-bar__total .cart-bar__total--bold {
  font-size: 28rpx;
  line-height: 40rpx;
  color: #333;
  font-weight: bold;
}
.cart-bar__total .cart-bar__total--normal {
  font-size: 24rpx;
  line-height: 32rpx;
  color: #999;
}

.cart-bar__total .cart-bar__total--price {
  color: #fa4126;
  font-weight: bold;
}

.text-padding-right {
  padding-right: 4rpx;
}
