<view class="cart-bar__placeholder" wx:if="{{fixed}}" />
<view class="cart-bar {{fixed ? 'cart-bar--fixed' : ''}} flex flex-v-center" style="bottom: {{fixed ? 'calc(' + bottomHeight + 'rpx + env(safe-area-inset-bottom))' : ''}};">
	<t-icon
	 size="40rpx"
	 color="{{isAllSelected ? '#FA4126' : '#BBBBBB'}}"
	 name="{{isAllSelected ? 'check-circle-filled' : 'circle'}}"
	 class="cart-bar__check"
	 catchtap="handleSelectAll"
	/>
	<text>全选</text>
	<view class="cart-bar__total flex1">
		<view>
			<text class="cart-bar__total--bold text-padding-right">总计</text>
			<price
			 price="{{totalAmount || '0'}}"
			 fill="{{false}}"
			 decimalSmaller
			 class="cart-bar__total--bold cart-bar__total--price"
			/>
			<text class="cart-bar__total--normal">（不含运费）</text>
		</view>
		<view wx:if="{{totalDiscountAmount}}">
			<text class="cart-bar__total--normal text-padding-right">已优惠</text>
			<price class="cart-bar__total--normal" price="{{totalDiscountAmount || '0'}}" fill="{{false}}" />
		</view>
	</view>
	<view catchtap="handleToSettle" class="{{!isDisabled ? '' : 'disabled-btn'}} account-btn" hover-class="{{!isDisabled ? '' : 'hover-btn'}}">
		去结算({{totalGoodsNum}})
	</view>
</view>

