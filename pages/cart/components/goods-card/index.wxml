<view
  id="{{independentID}}"
  class="wr-goods-card card-class {{ layout }} {{ centered ? 'center' : ''}}"
  bind:tap="clickHandle"
  data-goods="{{ goods }}"
  hidden="{{hiddenInData}}"
>
	<view class="wr-goods-card__main">
		<view class="wr-goods-card__thumb thumb-class" bind:tap="clickThumbHandle">
			<!-- data-src 是方便加购动画读取图片用的 -->
			<t-image
			  t-class="wr-goods-card__thumb-com"
			  wx:if="{{ !!goods.thumb && !goods.hideKey.thumb }}"
			  src="{{ goods.thumb }}"
			  mode="{{ thumbMode }}"
			  lazy-load="{{ lazyLoad }}"
			/>
			<slot name="thumb-cover" />
		</view>
		<view class="wr-goods-card__body">
			<view class="wr-goods-card__long_content">
				<view wx:if="{{ goods.title && !goods.hideKey.title }}" class="wr-goods-card__title title-class" style="-webkit-line-clamp: {{ goods.lineClamp }};">
					<slot name="before-title" />
					{{ goods.title }}
				</view>
				<slot name="after-title" />
				<view wx:if="{{ goods.desc && !goods.hideKey.desc }}" class="wr-goods-card__desc desc-class">{{ goods.desc }}</view>
				<slot name="after-desc" />
				<view wx:if="{{ goods.specs && goods.specs.length > 0 && !goods.hideKey.specs }}" class="wr-goods-card__specs__desc specs-class" bind:tap="clickSpecsHandle">
					<view class="wr-goods-card__specs__desc-text">{{ goods.specs }}</view>
					<t-icon name="chevron-down" size="32rpx" color="#999999" />
				</view>
				<view class="goods_tips" wx:if="{{goods.stockQuantity !== 0 && goods.quantity >= goods.stockQuantity}}">库存不足</view>
			</view>
			<view class="wr-goods-card__short_content">
				<block wx:if="{{goods.stockQuantity !== 0}}">
					<view wx:if="{{ pricePrefix }}" class="wr-goods-card__price__prefix price-prefix-class">{{ pricePrefix }}</view>
					<slot name="price-prefix" />
					<view wx:if="{{ goods.price && !goods.hideKey.price }}" class="wr-goods-card__price">
						<price
						  wr-class="price-class"
						  symbol="{{currency}}"
						  price="{{goods.price}}"
						  fill="{{priceFill}}"
						  decimalSmaller
						/>
					</view>
					<view wx:if="{{ goods.originPrice && !goods.hideKey.originPrice && isValidityLinePrice }}" class="wr-goods-card__origin-price">
						<price
						  wr-class="origin-price-class"
						  symbol="{{currency}}"
						  price="{{goods.originPrice}}"
						  fill="{{priceFill}}"
						/>
					</view>
					<slot name="origin-price" />
					<view wx:if="{{goods.num && !goods.hideKey.num}}" class="wr-goods-card__num num-class">
						<text class="wr-goods-card__num__prefix">x </text>
						{{ goods.num }}
					</view>
				</block>
				<block wx:else>
					<view class="no_storage">
						<view>请重新选择商品规格</view>
						<view class="no_storage__right">重选</view>
					</view>
				</block>
			</view>
			<slot name="append-body" />
		</view>
		<slot name="footer" />
	</view>
	<slot name="append-card" />
</view>

