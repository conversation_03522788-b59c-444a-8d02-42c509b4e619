.wr-goods-card {
  box-sizing: border-box;
  font-size: 24rpx;
}
/*  */
.wr-goods-card__main {
  position: relative;
  display: flex;
  padding: 0;
  background: transparent;
}

.wr-goods-card.center .wr-goods-card__main {
  align-items: flex-start;
  justify-content: center;
}

.wr-goods-card__thumb {
  flex-shrink: 0;
  position: relative;
  width: 140rpx;
  height: 140rpx;
}

.wr-goods-card__thumb-com {
  width: 192rpx;
  height: 192rpx;
  border-radius: 8rpx;
  overflow: hidden;
}
.wr-goods-card__thumb:empty {
  display: none;
  margin: 0;
}

.wr-goods-card__body {
  display: flex;
  margin: 0 0 0 20rpx;
  flex-direction: row;
  flex: 1 1 auto;
  min-height: 192rpx;
}

.wr-goods-card__long_content {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1 1 auto;
}
.wr-goods-card__long_content .goods_tips {
  width: 100%;
  margin-top: 16rpx;
  text-align: right;
  color: #fa4126;
  font-size: 24rpx;
  line-height: 32rpx;
  font-weight: bold;
}
.wr-goods-card__title {
  flex-shrink: 0;
  font-size: 28rpx;
  color: #333;
  line-height: 40rpx;
  font-weight: 400;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}
.wr-goods-card__title__prefix-tags {
  display: inline-flex;
}
.wr-goods-card__title__prefix-tags .prefix-tag {
  margin: 0 8rpx 0 0;
}
.wr-goods-card__desc {
  font-size: 24rpx;
  color: #f5f5f5;
  line-height: 40rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.wr-goods-card__specs__desc,
.wr-goods-card__specs__text {
  font-size: 24rpx;
  height: 32rpx;
  line-height: 32rpx;
  color: #999999;
  margin: 8rpx 0;
}
.wr-goods-card__specs__desc {
  display: flex;
  align-self: flex-start;
  flex-direction: row;
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 4rpx 8rpx;
}
.wr-goods-card__specs__desc-text {
  height: 100%;
  max-width: 380rpx;
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.wr-goods-card__specs__desc-icon {
  line-height: inherit;
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #bbb;
}
.wr-goods-card__specs__text {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
.wr-goods-card__tags {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin: 16rpx 0 0 0;
}
.wr-goods-card__tag {
  color: #fa550f;
  background: transparent;
  font-size: 20rpx;
  border: 1rpx solid #fa550f;
  padding: 0 8rpx;
  height: 30rpx;
  line-height: 30rpx;
  margin: 0 8rpx 8rpx 0;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  word-break: keep-all;
  text-overflow: ellipsis;
}
.wr-goods-card__short_content {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-end;
  margin: 0 0 0 46rpx;
}
.wr-goods-card__price__prefix {
  order: 0;
  color: #666;
  margin: 0;
}
.wr-goods-card__price {
  white-space: nowrap;
  font-weight: bold;
  order: 1;
  color: #fa4126;
  font-size: 36rpx;
  margin: 0;
  line-height: 48rpx;
}
.wr-goods-card__origin-price {
  white-space: nowrap;
  font-weight: normal;
  order: 2;
  color: #aaaaaa;
  font-size: 24rpx;
  margin: 0;
}
.wr-goods-card__num {
  white-space: nowrap;
  order: 4;
  font-size: 24rpx;
  color: #999;
  margin: 20rpx 0 0 auto;
}
.wr-goods-card__num__prefix {
  color: inherit;
}
.wr-goods-card__add-cart {
  order: 3;
  margin: auto 0 0 auto;
}
.wr-goods-card.horizontal-wrap .wr-goods-card__thumb {
  width: 192rpx;
  height: 192rpx;
  border-radius: 8rpx;
  overflow: hidden;
}
.wr-goods-card.horizontal-wrap .wr-goods-card__body {
  flex-direction: column;
}
.wr-goods-card.horizontal-wrap .wr-goods-card__short_content {
  flex-direction: row;
  align-items: center;
  margin: 16rpx 0 0 0;
}

.wr-goods-card.horizontal-wrap .wr-goods-card__num {
  margin: 0 0 0 auto;
}
.wr-goods-card.vertical .wr-goods-card__main {
  padding: 0 0 22rpx 0;
  flex-direction: column;
}
.wr-goods-card.vertical .wr-goods-card__thumb {
  width: 340rpx;
  height: 340rpx;
}
.wr-goods-card.vertical .wr-goods-card__body {
  margin: 20rpx 20rpx 0 20rpx;
  flex-direction: column;
}
.wr-goods-card.vertical .wr-goods-card__long_content {
  overflow: hidden;
}
.wr-goods-card.vertical .wr-goods-card__title {
  line-height: 36rpx;
}
.wr-goods-card.vertical .wr-goods-card__short_content {
  margin: 20rpx 0 0 0;
}
.wr-goods-card.vertical .wr-goods-card__price {
  order: 2;
  color: #fa4126;
  margin: 20rpx 0 0 0;
}
.wr-goods-card.vertical .wr-goods-card__origin-price {
  order: 1;
}
.wr-goods-card.vertical .wr-goods-card__add-cart {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
}

.wr-goods-card__short_content .no_storage {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40rpx;
  color: #333;
  font-size: 24rpx;
  line-height: 32rpx;
  width: 100%;
}

.no_storage .no_storage__right {
  width: 80rpx;
  height: 40rpx;
  border-radius: 20rpx;
  border: 2rpx solid #fa4126;
  line-height: 40rpx;
  text-align: center;
  color: #fa4126;
}
