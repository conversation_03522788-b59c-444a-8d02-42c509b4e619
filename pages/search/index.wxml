<view class="search-page">
  <t-search
    t-class-input-container="t-class__input-container"
    t-class-input="t-search__input"
    value="{{searchValue}}"
    leftIcon=""
    placeholder="搜索"
    bind:submit="handleSubmit"
    focus
  >
    <t-icon slot="left-icon" prefix="wr" name="search" size="40rpx" color="#bbb" />
  </t-search>
  <view class="search-wrap">
    <view class="history-wrap">
      <view class="search-header">
        <text class="search-title">历史搜索</text>
        <text class="search-clear" bind:tap="handleClearHistory">清除</text>
      </view>
      <view class="search-content">
        <view
          class="search-item"
          hover-class="hover-history-item"
          wx:for="{{searchHistory}}"
          bind:tap="handleHistoryTap"
          bindlongpress="deleteCurr"
          data-index="{{index}}"
          data-item="{{item}}"
          wx:key="*this"
        >
          {{item}}
        </view>
      </view>
    </view>
    <view class="popular-wrap">
      <view class="search-header">
        <text class="search-title">热门搜索</text>
      </view>
      <view class="search-content">
        <view
          class="search-item"
          hover-class="hover-history-item"
          wx:for="{{popularWords}}"
          bind:tap="handleHistoryTap"
          data-index="{{index}}"
          data-item="{{item}}"
          wx:key="*this"
        >
          {{item}}
        </view>
      </view>
    </view>
  </view>
  <t-dialog
    visible="{{dialogShow}}"
    content="{{dialog.message}}"
    bindconfirm="confirm"
    bind:close="close"
    confirm-btn="确定"
    cancel-btn="{{dialog.showCancelButton ? '取消' : null}}"
    t-class-confirm="dialog__button-confirm"
    t-class-cancel="dialog__button-cancel"
  />
</view>
