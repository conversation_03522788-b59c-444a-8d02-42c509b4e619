/* eslint-disable no-param-reassign */
import { getSearchResult } from "../../../services/good/fetchSearchResult";
import Toast from "tdesign-miniprogram/toast/index";
import { createStoreBindings } from "mobx-miniprogram-bindings";
import { productStore } from "../../../stores/productStore";

Page({
	data: {},
	onLoad(options) {
		const { miniProgram } = wx.getAccountInfoSync();
		this.setData({
			miniProgram: miniProgram,
		});

		// 将 store 绑定到页面
		this.productStoreBindings = createStoreBindings(this, {
			store: productStore,
			fields: ["shops"],
			actions: ["search"],
		});
		const { searchValue = "" } = options || {};
		console.debug("search", searchValue);
		this.setData(
			{
				keywords: searchValue,
			},
			() => {
				this.init(true);
			},
		);
	},

	onUnload() {
		this.productStoreBindings.destroyStoreBindings();
	},

	async init(reset = true) {
		console.debug("init", this.data.keywords);
		wx.showLoading();
		await this.search(this.data.keywords);
		wx.hideLoading();
		// const { loadMoreStatus, goodsList = [] } = this.data;
		// const params = this.generalQueryData(reset);
		// if (loadMoreStatus !== 0) return;
		// this.setData({
		// 	loadMoreStatus: 1,
		// 	loading: true,
		// });
		// try {
		// 	const result = await getSearchResult(params);
		// 	const code = "Success";
		// 	const data = result;
		// 	if (code.toUpperCase() === "SUCCESS") {
		// 		const { spuList, totalCount = 0 } = data;
		// 		if (totalCount === 0 && reset) {
		// 			this.total = totalCount;
		// 			this.setData({
		// 				emptyInfo: {
		// 					tip: "抱歉，未找到相关商品",
		// 				},
		// 				hasLoaded: true,
		// 				loadMoreStatus: 0,
		// 				loading: false,
		// 				goodsList: [],
		// 			});
		// 			return;
		// 		}

		// 		const _goodsList = reset ? spuList : goodsList.concat(spuList);
		// 		_goodsList.forEach((v) => {
		// 			v.tags = v.spuTagList.map((u) => u.title);
		// 			v.hideKey = { desc: true };
		// 		});
		// 		const _loadMoreStatus = _goodsList.length === totalCount ? 2 : 0;
		// 		this.pageNum = params.pageNum || 1;
		// 		this.total = totalCount;
		// 		this.setData({
		// 			goodsList: _goodsList,
		// 			loadMoreStatus: _loadMoreStatus,
		// 		});
		// 	} else {
		// 		this.setData({
		// 			loading: false,
		// 		});
		// 		wx.showToast({
		// 			title: "查询失败，请稍候重试",
		// 		});
		// 	}
		// } catch (error) {
		// 	this.setData({
		// 		loading: false,
		// 	});
		// }
		// this.setData({
		// 	hasLoaded: true,
		// 	loading: false,
		// });
	},

	handleTapStore(e) {
		this.setData({
			backRefresh: false,
		});
		const storeId = e.currentTarget.dataset.storeId;
		wx.navigateTo({
			url: `/pages/store/index?id=${storeId}`,
		});
	},

	handleTapProduct(e) {
		this.setData({
			backRefresh: false,
		});
		console.debug(e);
		const Id = e.currentTarget.dataset.promotionId;
		wx.navigateTo({
			url: `/pages/promotion/index?id=${Id}`,
			// url: "/pages/goods/details/index",
		});
	},

	handleInput(e) {
		console.debug("handleInput", e);
		const { value } = e.detail;
		this.setData({
			keywords: value,
		});
	},

	handleSubmit() {
		console.debug("handleSubmit", this.data.keywords);
		this.setData(
			{
				goodsList: [],
				loadMoreStatus: 0,
				keywords: this.data.keywords,
			},
			() => {
				this.init(true);
			},
		);
	},

	onReachBottom() {},

	handleFilterChange(e) {
		const { overall, sorts } = e.detail;
		const { total } = this;
		const _filter = {
			sorts,
			overall,
		};
		this.setData({
			filter: _filter,
			sorts,
			overall,
		});

		this.pageNum = 1;
		this.setData(
			{
				goodsList: [],
				loadMoreStatus: 0,
			},
			() => {
				total && this.init(true);
			},
		);
	},

	showFilterPopup() {
		this.setData({
			show: true,
		});
	},

	showFilterPopupClose() {
		this.setData({
			show: false,
		});
	},

	onMinValAction(e) {
		const { value } = e.detail;
		this.setData({ minVal: value });
	},

	onMaxValAction(e) {
		const { value } = e.detail;
		this.setData({ maxVal: value });
	},

	reset() {
		this.setData({ minVal: "", maxVal: "" });
	},

	confirm() {
		const { minVal, maxVal } = this.data;
		let message = "";
		if (minVal && !maxVal) {
			message = `价格最小是${minVal}`;
		} else if (!minVal && maxVal) {
			message = `价格范围是0-${minVal}`;
		} else if (minVal && maxVal && minVal <= maxVal) {
			message = `价格范围${minVal}-${this.data.maxVal}`;
		} else {
			message = "请输入正确范围";
		}
		if (message) {
			Toast({
				context: this,
				selector: "#t-toast",
				message,
			});
		}
		this.pageNum = 1;
		this.setData(
			{
				show: false,
				minVal: "",
				goodsList: [],
				loadMoreStatus: 0,
				maxVal: "",
			},
			() => {
				this.init();
			},
		);
	},
});
