import {
	getSearchHistory,
	getSearchPopular,
} from "../../services/good/fetchSearchHistory";
import { createStoreBindings } from "mobx-miniprogram-bindings";
import { productStore } from "../../stores/productStore";

Page({
	data: {
		historyWords: [],
		popularWords: [],
		searchValue: "",
		dialog: {
			title: "确认删除当前历史记录",
			showCancelButton: true,
			message: "",
		},
		dialogShow: false,
	},

	deleteType: 0,
	deleteIndex: "",

	onLoad(options) {
		// 将 store 绑定到页面
		this.productStoreBindings = createStoreBindings(this, {
			store: productStore,
			fields: ["shops", "searchHistory"],
			actions: ["loadSearchHistory", "clearSearchHistory"],
		});
	},

	onUnload() {
		this.productStoreBindings.destroyStoreBindings();
	},

	onShow() {
		console.debug("onShow");
		this.loadSearchHistory();

		this.queryHistory();
		this.queryPopular();
	},

	async onPullDownRefresh() {
		// 在这里执行刷新数据的操作
		console.log("下拉刷新触发");
		wx.showLoading();
		this.loadSearchHistory();
		// 模拟异步获取数据
		setTimeout(() => {
			// 数据获取完成后停止刷新动画
			wx.stopPullDownRefresh();
			wx.hideLoading();
		}, 500);
	},

	async queryHistory() {
		try {
			const data = await getSearchHistory();
			const code = "Success";
			if (String(code).toUpperCase() === "SUCCESS") {
				const { historyWords = [] } = data;
				this.setData({
					historyWords,
				});
			}
		} catch (error) {
			console.error(error);
		}
	},

	async queryPopular() {
		try {
			const data = await getSearchPopular();
			const code = "Success";
			if (String(code).toUpperCase() === "SUCCESS") {
				const { popularWords = [] } = data;
				this.setData({
					popularWords,
				});
			}
		} catch (error) {
			console.error(error);
		}
	},

	async confirm() {
		console.debug("confirm delete keywords");
		await this.clearSearchHistory();
		this.setData({
			searchHistory: [],
			dialogShow: false,
		});
		// const { historyWords } = this.data;
		// const { deleteType, deleteIndex } = this;
		// historyWords.splice(deleteIndex, 1);
		// if (deleteType === 0) {
		// 	this.setData({
		// 		historyWords,
		// 		dialogShow: false,
		// 	});
		// } else {
		// 	this.setData({ historyWords: [], dialogShow: false });
		// }
	},

	close() {
		this.setData({ dialogShow: false });
	},

	handleClearHistory() {
		const { dialog } = this.data;
		this.deleteType = 1;
		this.setData({
			dialog: {
				...dialog,
				message: "确认删除所有搜索记录",
			},
			dialogShow: true,
		});
	},

	deleteCurr(e) {
		const { index } = e.currentTarget.dataset;
		const { dialog } = this.data;
		this.deleteIndex = index;
		this.setData({
			dialog: {
				...dialog,
				message: "确认删除当前历史记录",
				deleteType: 0,
			},
			dialogShow: true,
		});
	},

	handleHistoryTap(e) {
		const { historyWords } = this.data;
		const { dataset } = e.currentTarget;
		console.debug("dataset", dataset);
		// const _searchValue = historyWords[dataset.index || 0] || "";
		const _searchValue = dataset.item;
		if (_searchValue) {
			wx.navigateTo({
				url: `/pages/search/result/index?searchValue=${_searchValue}`,
			});
		}
	},

	handleSubmit(e) {
		console.debug("handleSubmit", e);
		const { value } = e.detail;
		console.debug("value", value);
		if (value.length === 0) return;
		wx.navigateTo({
			url: `/pages/search/result/index?searchValue=${value}`,
		});
	},
});
