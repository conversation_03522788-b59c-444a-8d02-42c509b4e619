/* 加入福利群页面样式 */
page {
  background-color: #FFDE59;
}

.join-group {
  padding: 20rpx;
}

.nav-bar {
  display: flex;
  align-items: center;
  /* padding: 20rpx 0; */
  margin-top: 96rpx;
}

.nav-back {
  width: 48rpx;
  height: 48rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  color: #333;
}

.main-title {
  text-align: center;
  font-size: 48rpx;
  font-weight: bold;
  color: #FF4E42;
  margin: 40rpx 0;
}

.group-info {
  background-color: #fff;
  /* border-radius: 16rpx;
  padding: 20rpx; */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  /* margin-bottom: 20rpx; */
}

.group-logo {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
}

.group-details {
  text-align: center;
}

.group-name {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.group-tags {
  display: flex;
  justify-content: center;
}

.tag {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  margin: 0 10rpx;
}

.red-tag {
  background-color: #FF4E42;
  color: #fff;
}

.light-tag {
  background-color: #FFF2F0;
  color: #FF4E42;
}

.group-benefits {
  text-align: center;
  font-size: 28rpx;
  color: #FF4E42;
  margin-top: 20rpx;
}

.qr-code-section {
  text-align: center;
  /* margin: 40rpx 0; */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #FFF;
  padding-bottom: 20rpx;
  margin-bottom: 40rpx;
}

.qr-code {
  width: 400rpx;
  height: 400rpx;
  margin-bottom: 20rpx;
  margin-top: 20rpx;
}

.qr-instruction {
  font-size: 28rpx;
  color: #333;
}

.bottom-benefits {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.benefit-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.benefit-item image {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.benefit-item text {
  font-size: 24rpx;
  color: #333;
} 