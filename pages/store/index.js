import { createStoreBindings } from "mobx-miniprogram-bindings";
import { productStore } from "../../stores/productStore";
import { userStore } from "../../stores/userStore";

// pages/store/index.js
Page({
	/**
	 * 页面的初始数据
	 */
	data: {
		statusBarHeight: 47,
		navBarHeight: 0,
		titleOffset: 0,
		shop: null,
		activeTab: 0,
		tabs: ["商家详情", "探店活动"],
		id: 0,
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		// 将 store 绑定到页面
		this.productStoreBindings = createStoreBindings(this, {
			store: productStore,
			fields: ["shop"], // 需要绑定的字段
			actions: ["shopDetail"], // 需要绑定的 actions
		});
		this.userStoreBindings = createStoreBindings(this, {
			store: userStore,
			fields: ["isShopFavorited"], // 需要绑定的字段
			actions: ["hasFavorited", "toggleFavorite"], // 需要绑定的 actions
		});

		this.initStatusBar();

		console.debug("options:", options);
		// 可以通过options获取店铺ID，然后请求店铺详情
		const { id } = options;
		if (id) {
			this.setData({
				id,
			});
			this.fetchStoreDetail(id);
			this.isFavorited(id);
		}
	},

	initStatusBar() {
		const systemInfo = wx.getWindowInfo();
		const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
		// 状态栏高度
		const statusBarHeight = systemInfo.statusBarHeight;
		// 导航栏高度 = 胶囊按钮高度 + (胶囊按钮顶部距离 - 状态栏高度) * 2
		const navBarHeight =
			menuButtonInfo.height + (menuButtonInfo.top - statusBarHeight) * 2;
		// 标题居中的偏移量 = (胶囊按钮左侧距离 - 屏幕宽度 / 2) + 胶囊按钮宽度 / 2
		const titleOffset =
			menuButtonInfo.left -
			systemInfo.windowWidth / 2 +
			menuButtonInfo.width / 2;
		this.setData({
			statusBarHeight,
			navBarHeight,
			titleOffset,
		});
	},

	/**
	 * 请求店铺详情
	 */
	async fetchStoreDetail(id) {
		// 这里应该调用API获取店铺详情
		// wx.request({
		//   url: `https://api.example.com/stores/${id}`,
		//   success: (res) => {
		//     this.setData({
		//       storeInfo: res.data.storeInfo,
		//       promotions: res.data.promotions
		//     });
		//   }
		// });
		wx.showLoading();
		// const res = await productStore.shopDetail(id)
		// console.debug("shop detail", res);
		// this.setData({
		//   shopInfo: res.data
		// })
		// if(res.data.main_image) {
		//   this.setData({
		//     "shopInfo.logo": res.data.main_image[0]
		//   })
		// }else {
		//   this.setData({
		//     "shopInfo.logo": "https://placehold.co/100x100"
		//   })
		// }
		await this.shopDetail(id);
		wx.hideLoading();
	},

	/**
	 * 导航到地图
	 */
	navigateToMap() {
		wx.openLocation({
			latitude: this.data.shop.latitude, // 这里应该是实际的经纬度
			longitude: this.data.shop.longitude,
			name: this.data.shop.name,
			address: this.data.shop.address,
			scale: 18,
		});
	},

	/**
	 * 拨打商家电话
	 */
	callMerchant() {
		const { phone } = this.data.shop;
		wx.makePhoneCall({
			phoneNumber: phone,
			success: () => {
				console.log("拨打电话成功");
			},
			fail: (err) => {
				console.error("拨打电话失败", err);
			},
		});
	},

	/**
	 * 切换标签页
	 */
	switchTab(e) {
		const { index } = e.currentTarget.dataset;
		this.setData({
			activeTab: index,
		});
	},

	/**
	 * 收藏/取消收藏店铺
	 */
	async toggleFavoriteAction() {
		console.debug("click toggle favorite button");
		const { shop } = this.data;
		console.debug(shop);
		console.debug("isShopFavorited取反后", !this.data.isShopFavorited);
		const result = await this.toggleFavorite({
			shop_id: shop._id,
			favorited: !this.data.isShopFavorited,
		});
		console.debug("toggleFavorite result", result);
		if (!this.data.isShopFavorited) {
			wx.showToast({
				title: "收藏成功",
			});
		} else {
			wx.showToast({
				title: "取消收藏",
			});
		}
		await this.isFavorited(shop._id);
	},

	async isFavorited(id) {
		await this.hasFavorited({
			shop_id: id,
		});
		// const result = await this.hasFavorited({
		// 	shop_id: id,
		// });
		// console.debug("result", result);
		// if (result) {
		// 	this.setData({
		// 		isShopFavorited: true,
		// 	});
		// } else {
		// 	this.setData({
		// 		isShopFavorited: false,
		// 	});
		// }
	},
	/**
	 * 去抢单
	 */
	// grabPromotion(e) {
	//   const { id } = e.currentTarget.dataset;
	//   // 这里应该调用API处理抢单逻辑
	//   wx.showLoading({
	//     title: '处理中...',
	//   });

	//   setTimeout(() => {
	//     wx.hideLoading();
	//     wx.showToast({
	//       title: '抢单成功',
	//       icon: 'success'
	//     });
	//   }, 1000);
	// },
	handleTapProduct(e) {
		console.debug(e);
		const Id = e.currentTarget.dataset.promotionId;
		wx.navigateTo({
			url: `/pages/promotion/index?id=${Id}`,
		});
	},
	/**
	 * 返回上一页
	 */
	goBack() {
		wx.navigateBack();
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {
		this.productStoreBindings.destroyStoreBindings();
		this.userStoreBindings.destroyStoreBindings();
	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {
		this.setData({ enable: true });
		setTimeout(() => {
			this.setData({ enable: false });
		}, 1000);
		this.fetchStoreDetail(this.data.id);
	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {
		let referrer_id = null;
		const userInfo = wx.getStorageSync("dgx-auth");
		if (userInfo) {
			referrer_id = userInfo.user_id;
		}
		const district = this.data.shop?.district;
		const shop_name = this.data.shop?.name;
		return {
			title: `${district} | ${shop_name}`,
			path: `/pages/store/index?id=${this.options.id || ""}&referrer_id=${referrer_id}`,
		};
	},

	handleTapProduct(e) {
		console.debug(e);
		const Id = e.currentTarget.dataset.promotionId;
		wx.navigateTo({
			url: `/pages/promotion/index?id=${Id}`,
			// url: "/pages/goods/details/index",
		});
	},
});
