<wxs module="filter" src="../../utils/util.wxs"></wxs>

<t-pull-down-refresh value="{{enable}}" loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}" usingCustomNavbar
  bind:refresh="onPullDownRefresh" bind:scroll="onScroll">

  <view style="padding-top: {{statusBarHeight}}px;" class="nav-bar">
    <!--标题内容-->
    <!-- <t-sticky offset-top="47"> -->
    <view class="navbar-addr" catchtap="chooseLocation">
      <t-icon prefix="wr" name="location" size="36rpx" style="color:#bbb" />
      {{location.name || location.address || "请选择地址"}}
      <t-icon prefix="" name="chevron-down" size="36rpx" style="color:#bbb" />
    </view>
    <!-- </t-sticky> -->

    <!-- <view class="nav-bar-content">
      <slot>
        <view class="nav-bar-title">优探生活</view>
      </slot>
    </view> -->
  </view>

  <view wx:if="{{isFixed1}}" class="overlay-when-fixed">
  </view>

  <view style="text-align: center; color: #b9b9b9" wx:if="{{pageLoading}}">
    <t-loading theme="circular" size="40rpx" text="加载中..." inherit-color />
  </view>

  <!-- <view class="home-page-header">
    <view class="search" bind:tap="navToSearchPage">
      <t-search t-class-input="t-search__input" t-class-input-container="t-search__input-container" placeholder="曾宴"
        leftIcon="">
        <t-icon slot="left-icon" prefix="wr" name="search" size="40rpx" color="#bbb" />
      </t-search>
    </view>
    <view class="swiper-wrap" wx:if="{{!miniProgram.version}}">
      <t-swiper wx:if="{{imgSrcs.length > 0}}" current="{{current}}" autoplay="{{autoplay}}" duration="{{duration}}"
        interval="{{interval}}" navigation="{{navigation}}" imageProps="{{swiperImageProps}}" list="{{imgSrcs}}"
        bind:click="navToActivityDetail" />
    </view>
  </view> -->

  <view class="home-page-container">
    <!-- <t-sticky offset-top="97" bind:scroll="onSroll"> -->
    <view class="home-page-tabs">
      <t-tabs t-class="t-tabs" t-class-active="tabs-external__active" t-class-item="tabs-external__item"
        defaultValue="{{0}}" space-evenly="{{false}}" bind:change="onTabChange">
        <t-tab-panel wx:for="{{categories}}" wx:for-index="index" wx:key="_id" label="{{item.name}}"
          value="{{item._id}}" />
      </t-tabs>
    </view>
    <!-- </t-sticky> -->

    <!-- <goods-list
    wr-class="goods-list-container"
    goodsList="{{goodsList}}"
    bind:click="goodListClickHandle"
    bind:addcart="goodListAddCartHandle"
    /> -->

    <view class="container">
      <!-- 顶部加载状态 -->
      <!-- 
      <view class="loading-container" wx:if="{{shopsLoadStatus===1}}">
        <t-loading theme="circular" size="48rpx" text="数据加载中..." />
      </view>
      -->

      <view class="store-list">
        <block wx:for="{{shops_ykj}}" wx:key="_id" wx:for-item="shop">
          <!-- 店铺卡片 -->
          <view class="store-card" wx:if="{{shop.promotions.length > 0}}">
            <!-- 店铺信息 - 紧凑版 -->
            <view class="store-info-compact" hover-class="store-hover" bindtap="handleTapStore"
              data-store-id="{{shop._id}}">

              <view class="store-logo">
                <t-image src="{{shop.logo}}" class="logo-image-small" shape="circle" loading="slot">
                  <t-loading slot="loading" theme="spinner" size="30rpx" loading />
                </t-image>
              </view>

              <view class="store-header-row">
                <!-- 商店Logo + 名称 -->
                <!-- <view class="store-left"> -->
                <!-- <t-skeleton class="logo-image-small" rowCol="{{rowColsAvatar}}" loading wx:if="{{!shop.logo}}"></t-skeleton> -->
                <!-- <image class="logo-image-small" src="{{shop.logo}}" mode="aspectFill" width="60" hight="60" wx:if="{{shop.logo}}"/> -->
                <!-- </view> -->
                <view class="store-name-compact">{{shop.name}}</view>
                <view class="store-tag-compact">{{shop.tag}}</view>
              </view>

              <view class="store-right">
                <view class="location-distance">
                  <text class="location-text">{{shop.district}}</text>
                  <text class="distance-text">{{filter.formatDistance(shop.distance)}}</text>
                </view>

                <view class="badge-container">
                  <block>
                    <view class="badge-compact">
                      无需笔记
                      <!-- <t-icon name="swap" size="20rpx" color="#FF5F15" /> -->
                      <!-- <t-icon name="time" size="20rpx" color="#FF5F15" /> 过期退 -->
                    </view>
                  </block>
                </view>
              </view>
              <!-- 第二行：排名 + 标签 -->
              <!-- <view class="store-meta-row">
              <view class="store-meta-left">
                <text class="popularity-text-compact">{{shop.tag}}</text>
              </view>
              <view class="badge-container">
                <block >
                  <view class="badge-compact">
                    <t-icon name="swap" size="20rpx" color="#FF5F15" /> 随时退
                    <t-icon name="time" size="20rpx" color="#FF5F15" /> 过期退
                  </view>
                </block>
              </view>
            </view> -->
            </view>

            <!-- 食品列表 -->
            <view class="food-list">
              <view class="food-card" wx:if="{{shop.promotions.length === 0}}">
                <t-skeleton class="group-avatar" rowCol="{{rowColsImage}}" loading></t-skeleton>
                <t-skeleton class="group-content" rowCol="{{rowColsContent}}" loading></t-skeleton>
              </view>

              <block wx:for="{{shop.promotions}}" wx:key="_id" wx:for-item="promotion">
                <view class="food-card" hover-class="food-card-hover" bindtap="handleTapProduct"
                  data-store-id="{{shop._id}}" data-promotion-id="{{promotion._id}}">
                  <!-- 食品图片 -->
                  <view>
                    <view class="food-image-container">
                      <!-- <image class="food-image" src="{{promotion.product_id.images[0]}}" mode="aspectFill"></image> -->
                      <t-image src="{{promotion.product_id.images[0]}}" mode="aspectFill"
                        id="{{promotion._id+promotion.product_id._id}}" shape="square" loading="slot"
                        class="food-image">
                        <t-loading slot="loading" theme="spinner" size="40rpx" loading />
                      </t-image>
                    </view>
                    <!-- <view class="progress">
                      <t-progress theme="line" size="24" percentage="75" stroke-width="10" label="剩2份"
                        status="active" />
                    </view> -->
                  </view>

                  <view class="food-info">
                    <!-- 第一行：标签和剩余份数放在同一行 -->
                    <view class="food-top-row">
                      <!-- 食品标签 -->
                      <view class=" food-tag-compact" wx:if="{{food.tag}}">
                        <text>{{promotion.tag}}</text>
                      </view>
                    </view>

                    <!-- 第二行：商品名称单独一行 -->
                    <view class="food-title-area">
                      <view class="food-name-compact">{{promotion.name}}</view>
                    </view>

                    <!-- 第三行：价格信息和按钮 -->
                    <view class="food-footer-row">
                      <!-- <view class="price-discount-group"> -->
                      <view class="wave-price-area">
                        <!-- <t-image
                            src="cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-1321286342/app/wave-price-bg.jpg"
                            class="wave-bg" mode="aspectFill" width="260" height="60" /> -->
                        <view class="price-info" catchtap="handleTapProduct" data-store-id="{{shop._id}}"
                          data-promotion-id="{{promotion._id}}">
                          <view class="price-col price1">
                            <view class="price-label">¥{{promotion.original_price}}</view>
                            <view class="price-desc">原价</view>
                          </view>
                          <view class="price-col price2">
                            <view class="price-label">¥{{promotion.sale_price}}</view>
                            <view class="price-desc">团购价</view>
                          </view>
                          <view class="price-col price3 highlight">
                            <view class="price-label">¥<view class="price-num">{{promotion.current_price}}</view>
                            </view>
                            <view class="price-desc">优探价</view>
                          </view>
                        </view>
                      </view>
                    </view>

                    <!-- <view class="flex quantity-info">剩{{promotion.realtime_quantity}}份</view> -->
                  </view>
                </view>
              </block>
            </view>
          </view>
        </block>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{shops_ykj.length === 0 && shopsLoadStatus != 1}}">
        <t-icon name="shop" size="80rpx" color="#cccccc" />
        <text class="empty-text">没有更多了</text>
      </view>

      <load-more list-is-empty="{{!shops_ykj.length}}" status="{{shopsLoadStatus}}" bind:retry="onReTry" />
    </view>

    <t-toast id="t-toast" />

    <t-popup visible="{{false}}" usingCustomNavbar bind:visible-change="onVisibleChange" placement="{{'center'}}">
      <view class="block block--center">在你所在的区域暂未开放</view>
    </t-popup>
  </view>
</t-pull-down-refresh>


<t-back-top visibility-height="300px" style="right:20rpx; bottom: 200px;" theme="{{backTopTheme}}"
  text="{{backTopText}}" scroll-top="{{scrollTop}}" bind:to-top="onToTop"
  wx:if="{{showBackTop && isAndroid}}"></t-back-top>