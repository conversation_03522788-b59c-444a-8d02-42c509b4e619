import { createStoreBindings } from "mobx-miniprogram-bindings";
import { locationStore, productStore } from "../../stores/index";
import { fetchHome, fetchStoresList } from "../../services/home/<USER>";
// import { fetchGoodsList } from "../../services/good/fetchGoods";
import { log } from "../../utils/log";
import Toast from "tdesign-miniprogram/toast/index";
// const chooseLocation = requirePlugin("chooseLocation");

import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
dayjs.extend(isBetween);

Page({
	properties: {
		scrollTop: { type: Number, value: 0 },
	},

	data: {
		imgSrcs: [],
		tabList: [],
		goodsList: [],
		goodsListLoadStatus: 0,
		pageLoading: false,
		current: 1,
		autoplay: true,
		duration: "500",
		interval: 5000,
		navigation: { type: "dots" },
		swiperImageProps: { mode: "scaleToFit" },
		isLoading: false,
		isRefreshing: false,
		statusBarHeight: 0, // 状态栏高度
		navBarHeight: 44, // 导航栏高度
		paddingLeft: 10,
		isFixed1: false,
		location: {},
		miniProgram: {},
		// shopList: [],
		// totalShops: 0,
		selectedCategory: "0",
		categories: [],
		// storesList: [],
		rowColsAvatar: [{ size: "60rpx", type: "circle" }],
		rowColsImage: [{ size: "96rpx", type: "rect" }],
		rowColsContent: [{ width: "50%" }, { width: "100%" }],
		isUpdatingStock: false,
		backRefresh: true,
		backTopTheme: "round",
		backTopText: "顶部",
		showBackTop: false,
		isAndroid: false, // 默认为 false
	},

	checkLocation() {
		console.debug("checkLocation");
		wx.getSetting({
			success(res) {
				const locationAuth = res.authSetting["scope.userLocation"];
				if (locationAuth === undefined) {
					// 还未询问过权限
					console.log("尚未询问定位权限");
				} else if (locationAuth === false) {
					// 用户拒绝了定位权限
					console.log("用户拒绝了定位权限");
					wx.authorize({
						scope: "scope.userLocation",
						success() {
							// 用户已经同意授权
							console.log("定位权限已授权");
						},
						fail() {
							// 用户拒绝了授权
							console.log("定位权限被拒绝");
							Toast({
								context: this,
								selector: "#t-toast",
								message: "请打开定位功能",
							});
							console.debug("请打开定位功能");
							return;
						},
					});
				} else {
					// 用户已授权定位权限
					console.log("用户已授权定位权限");
				}
			},
		});
	},

	async onLoad(options) {
		console.debug("onLoad options", options);

		const { miniProgram } = wx.getAccountInfoSync();
		this.setData({
			miniProgram: miniProgram,
		});

		// 检测系统类型
		const deviceInfo = wx.getDeviceInfo();
		this.setData({
			isAndroid:
				deviceInfo.model.toLowerCase().includes("android") ||
				deviceInfo.system.toLowerCase().includes("android"),
		});

		this.bindMbxStores();

		this.locateMe();
		// this.checkLocation();

		this.initStatusBar();
		this.loadCategories();
		this.loadHomePage();

		// console.debug("this.locate")
		// const locatePromise = await this.locate();
		// console.debug("locatePromise", locatePromise);
	},

	bindMbxStores() {
		// 将 store 绑定到页面
		this.productStoreBindings = createStoreBindings(this, {
			store: productStore,
			fields: [
				"shops",
				"shops_ykj",
				"page_number",
				"page_size",
				"total",
				"shopsLoadStatus",
				"selectedCategory",
				"categories",
			],
			actions: [
				"getAllCategories",
				"getNearestShops",
				"setSelectedCategory",
				"setPageNumber",
				"setTotal",
				"resetShops",
			],
		});

		this.locationStoreBindings = createStoreBindings(this, {
			store: locationStore,
			fields: ["location"],
			actions: ["locate", "loadLocationFromStorage"],
		});
	},

	onShow() {
		this.getTabBar().init();

		// this.locateMe();
		console.debug("init");
		this.init();
	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {
		// 解绑 store
		this.productStoreBindings.destroyStoreBindings();
		this.locationStoreBindings.destroyStoreBindings();
	},

	onPageScroll(e) {
		if (this.data.isAndroid) {
			console.debug("onPageScroll", e);
			// 滚动超过 300px 时显示
			this.setData({ showBackTop: e.scrollTop > 300 });
		}
	},

	init() {
		this.handleIsUpdatingStockButton();
		if (this.data.backRefresh) {
			console.debug("backRefresh");

			this.setPageNumber(1);
			this.setTotal(0);
			this.resetShops();

			setTimeout(() => {
				this.loadNearestShops(this.data.selectedCategory);
			}, 200);
		}
	},

	handleIsUpdatingStockButton() {
		// 定义时间区间
		const startTimeStr = `${dayjs().format("YYYY-MM-DD")} 00:00:00`;
		const endTimeStr = `${dayjs().format("YYYY-MM-DD")} 00:10:00`;
		const startTime = dayjs(startTimeStr, "YYYY-MM-DD HH:mm:ss");
		const endTime = dayjs(endTimeStr, "YYYY-MM-DD HH:mm:ss");

		// 要检查的时间
		const checkTime = dayjs();
		// 判断是否在区间内（包含边界）
		const between = checkTime.isBetween(startTime, endTime, "second", "[]");

		if (between) {
			this.setData({
				isUpdatingStock: true,
			});
		} else {
			this.setData({
				isUpdatingStock: false,
			});
		}
	},

	async onReachBottom() {
		console.debug("onReachBottom");
		// if (this.data.goodsListLoadStatus === 0) {
		// 	// this.loadGoodsList();
		// 	// this.loadStoresList();
		// }
		if (this.data.page_size * (this.data.page_number - 1) >= this.data.total) {
			console.debug("no more pagination");
			this.setData({
				shopsLoadStatus: 2,
			});
		} else {
			console.debug("load more data");
			await this.loadNearestShops(this.data.selectedCategory);
		}
	},

	onPullDownRefresh() {
		console.debug("onPulldownRefresh");
		const ignoreLocalStorage = true;
		this.locate(ignoreLocalStorage);
		this.init();
	},

	onScroll() {
		console.debug("onScroll", this.data.scrollTop);
	},

	async loadCategories() {
		await this.getAllCategories();
	},

	async loadNearestShops(category_id) {
		if (this.data.location?.address_component.city !== "武汉市") {
			Toast({
				context: this,
				selector: "#t-toast",
				message: "不在服务范围内",
				duration: 5000,
			});
			this.setData({
				pageLoading: false,
			});
			this.setPageNumber(1);
			this.setTotal(0);
			this.resetShops();
			return;
		} else {
			this.setData({ goodsListLoadStatus: 1 });
			await this.getNearestShops(category_id, 'ykj');
		}
	},

	initStatusBar() {
		const { statusBarHeight } = wx.getWindowInfo();
		this.setData({
			statusBarHeight: statusBarHeight,
		});
	},

	// onSroll(e) {
	// 	if (e.detail.isFixed) {
	// 		this.setData({
	// 			isFixed1: true,
	// 		});
	// 	}
	// },

	loadHomePage() {
		wx.stopPullDownRefresh();

		this.setData({
			pageLoading: true,
		});
		fetchHome().then(({ swiper, tabList }) => {
			this.setData({
				tabList,
				imgSrcs: swiper,
				pageLoading: false,
			});
		});
	},

	onTabChange(e) {
		// this.privateData.tabIndex = e.detail;
		// this.loadGoodsList(true);
		// this.loadStoresList(true);
		// console.debug(e.detail);
		// console.debug(e.detail.value);
		this.setSelectedCategory(e.detail.value);
		this.setPageNumber(1);
		this.setTotal(0);
		this.resetShops();
		setTimeout(() => {
			this.loadNearestShops(e.detail.value);
		}, 10);
	},

	onReTry() {
		// this.loadGoodsList();
		// this.loadStoresList(true);
	},

	navToSearchPage() {
		wx.navigateTo({ url: "/pages/search/index" });
	},

	navToActivityDetail() {
		console.debug("click swiper");
		wx.switchTab({
			url: "/pages/invite/index",
		});
	},

	chooseLocation() {
		wx.navigateTo({
			url: "/pages/areas/index",
		});
		return;
	},

	async locateMe() {
		const loc = wx.getStorageSync("dgx-location");
		if (loc) {
			console.debug("loadLocationFromStorage in locateMe");
			const loc = await this.loadLocationFromStorage();
			if (loc?.address_component?.city !== "武汉市") {
				Toast({
					context: this,
					selector: "#t-toast",
					message: "不在服务范围内",
					duration: 5000,
				});
				this.setData({
					pageLoading: false,
					shops: [],
				});
				return;
			}
			return;
		} else {
			this.chooseLocation();
			return;
		}

		// if (this.data.location && this.data.location.serviceUnavailable) {
		// 	Toast({
		// 		context: this,
		// 		selector: "#t-toast",
		// 		message: "暂不支持你所在区域",
		// 	});
		// 	console.debug("sorry, service unavailable in your city");
		// 	return;
		// }

		// wx.navigateTo({
		// 	url: "/pages/areas/index",
		// });
		// return;

		// 先获取当前位置，然后设置地图中心点
		// wx.getLocation({
		// 	type: 'gcj02',
		// 	success: (res) => {
		// 		console.debug("wx.getLocation success:", res)
		wx.chooseLocation({
			// latitude: res.latitude,  // 设置地图中心点为当前位置
			// longitude: res.longitude,
			success: (res) => {
				console.debug("wx.chooseLocation success:", res);
				if (res.errMsg === "chooseLocation:ok") {
					const location = {
						name: res.name,
						address: res.address,
						latitude: res.latitude,
						longitude: res.longitude,
					};
					this.setData({
						location: location,
					});
					wx.setStorageSync("dgx-location", location);
					this.setPageNumber(1);
					this.setTotal(0);
					this.resetShops();
					setTimeout(() => {
						this.loadNearestShops(this.data.selectedCategory);
					}, 300);
					// setTimeout(async () => {
					// 	console.debug(this.data.selectedCategory)
					// 	// this.init();
					// 	await this.loadNearestShops(this.data.selectedCategory);
					// }, 1000)
				}
			},
			fail: (err) => {
				console.debug("wx.chooseLocation fail:", err);
			},
		});
	},

	handleTapStore(e) {
		this.setData({
			backRefresh: false,
		});
		const storeId = e.currentTarget.dataset.storeId;
		wx.navigateTo({
			url: `/pages/store/index?id=${storeId}`,
		});
	},

	handleTapProduct(e) {
		this.setData({
			backRefresh: false,
		});
		console.debug(e);
		const Id = e.currentTarget.dataset.promotionId;
		wx.navigateTo({
			url: `/pages/promotion/index?id=${Id}`,
		});
	},

	onShareAppMessage() {
		log.debug("onShareAppMessage");
		let referrer_id = null;
		const userInfo = wx.getStorageSync("dgx-auth");
		if (userInfo) {
			referrer_id = userInfo.user_id;
		}
		return {
			title: "快来优探生活！全城优质活动1折探！", // 分享标题
			path: `/pages/home/<USER>// 分享路径，通常是当前页面路径
			imageUrl:
				"cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-1321286342/app/share.jpg", // 分享图片
		};
	},

	onShareTimeline() {
		log.debug("onShareTimeline");
		let referrer_id = null;
		const userInfo = wx.getStorageSync("dgx-auth");
		if (userInfo) {
			referrer_id = userInfo.user_id;
		}
		return {
			title: "快来优探生活！全城优质活动1折探！", // 分享标题
			path: `/pages/home/<USER>// 分享路径，通常是当前页面路径
			imageUrl:
				"cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-1321286342/app/share.jpg", // 分享图片
			query: "referrer_id=${referrer_id}",
		};
	},

	onToTop(e) {
		this.triggerEvent("to-top", e);
	},
});
