@import "../../style/global.wxss";
page {
	width: 100vw;
}

.goods-detail-page .shop-info-card {
	margin: 20rpx 0;
	padding: 24rpx;
	background: #fff;
	border-radius: 16rpx;
}

.shop-info-center {
	flex: 1;
	min-width: 0;
}

.shop-info-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.shop-name {
	font-size: 32rpx;
	color: #333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	width: 78%;
}

.shop-rating {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 12rpx;
}

.rating-tag {
	font-size: 24rpx;
	color: #fa8c16;
	background: rgba(250, 140, 22, 0.1);
	padding: 4rpx 12rpx;
	border-radius: 8rpx;
}

.rating-score {
	color: #fa8c16;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	margin-left: 4rpx;
	/* font-weight: bold; */
}

.score {
	margin-left: 10rpx;
	display: flex;
	align-items: center;
	font-size: 28rpx;
}

.shop-time {
	display: flex;
	align-items: center;
	gap: 8rpx;
	color: #666;
	font-size: 26rpx;
	margin-top: 8rpx;
}

.shop-address {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 8rpx;
}

.address-info {
	display: flex;
	align-items: center;
	gap: 8rpx;
	color: #666;
	font-size: 26rpx;
	flex: 1;
	min-width: 0;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	/* order: 1; */
}

.address-actions {
	display: flex;
	gap: 16rpx;
	/* margin-left: 16rpx; */
}

.action-btn {
	display: flex;
	align-items: center;
	gap: 4rpx;
}

.action-btn text {
	font-size: 24rpx;
	color: #666;
}

.goods-info {
	margin: 0 auto;
	padding: 26rpx 0 28rpx 30rpx;
	background-color: #fff;
}

.goods-detail-page .swipe-img {
	width: 100%;
	height: 750rpx;
}

.goods-detail-page .goods-info .goods-price {
	display: flex;
	align-items: baseline;
}

.goods-detail-page .goods-info .goods-price-up {
	color: #fa4126;
	font-size: 28rpx;
	position: relative;
	bottom: 4rpx;
	left: 8rpx;
}

.goods-detail-page .goods-info .goods-price .class-goods-price {
	font-size: 64rpx;
	color: #fa4126;
	font-weight: bold;
	font-family: DIN Alternate;
}

.goods-detail-page .goods-info .goods-price .class-goods-symbol {
	font-size: 36rpx;
	color: #fa4126;
}

.goods-detail-page .goods-info .goods-price .class-goods-del {
	position: relative;
	font-weight: normal;
	left: 16rpx;
	bottom: 2rpx;
	color: #999999;
	font-size: 32rpx;
}

.goods-detail-page .goods-info .goods-number {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.goods-detail-page .goods-info .goods-number .sold-num {
	font-size: 24rpx;
	color: #999999;
	display: flex;
	align-items: flex-end;
	margin-right: 32rpx;
}

.goods-detail-page .goods-info .goods-activity {
	display: flex;
	margin-top: 16rpx;
	justify-content: space-between;
}

.goods-detail-page .goods-info .goods-activity .tags-container {
	display: flex;
}

.goods-detail-page
	.goods-info
	.goods-activity
	.tags-container
	.goods-activity-tag {
	background: #ffece9;
	color: #fa4126;
	font-size: 24rpx;
	margin-right: 16rpx;
	padding: 4rpx 8rpx;
	border-radius: 4rpx;
}

.goods-detail-page .goods-info .goods-activity .activity-show {
	display: flex;
	justify-content: center;
	align-items: center;
	color: #fa4126;
	font-size: 24rpx;
	padding-right: 32rpx;
}

.goods-detail-page .goods-info .goods-activity .activity-show-text {
	line-height: 42rpx;
}

.goods-detail-page .goods-info .goods-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 20rpx;
}

.goods-detail-page .goods-info .goods-title .goods-name {
	width: 600rpx;
	font-weight: 500;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	font-size: 32rpx;
	word-break: break-all;
	color: #333333;
}

.goods-detail-page .goods-info .goods-title .goods-tag {
	width: 104rpx;
	margin-left: 26rpx;
}

.goods-detail-page .goods-info .goods-title .goods-tag .shareBtn {
	border-radius: 200rpx 0px 0px 200rpx;
	width: 100rpx;
	height: 96rpx;
	border: none;
	padding-right: 36rpx !important;
}

.goods-detail-page .goods-info .goods-title .goods-tag .shareBtn::after {
	border: none;
}

.goods-detail-page .goods-info .goods-title .goods-tag .btn-icon {
	font-size: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 96rpx;
	color: #999;
}

.goods-detail-page .goods-info .goods-title .goods-tag .btn-icon .share-text {
	padding-top: 8rpx;
	font-size: 20rpx;
	line-height: 24rpx;
}

.goods-detail-page .goods-info .goods-intro {
	font-size: 26rpx;
	color: #888;
	line-height: 36rpx;
	word-break: break-all;
	padding-right: 30rpx;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	white-space: normal;
	overflow: hidden;
}

.spu-select {
	height: 80rpx;
	background-color: #fff;
	margin-top: 20rpx;
	display: flex;
	align-items: center;
	padding: 30rpx;
	font-size: 28rpx;
}

.spu-select .label {
	margin-right: 30rpx;
	text-align: center;
	flex-shrink: 0;
	color: #999999;
	font-weight: normal;
}

.spu-select .content {
	display: flex;
	flex: 1;
	justify-content: space-between;
	align-items: center;
}

.spu-select .content .tintColor {
	color: #aaa;
}

.goods-detail-page .desc-content {
	margin-top: 20rpx;
	background-color: #fff;
	padding-bottom: 120rpx;
}

/* 使用须知弹出层样式 */
.usage-popup-container,
.note-popup-container {
	background-color: #fff;
	border-radius: 24rpx 24rpx 0 0;
	padding: 32rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.usage-popup-header,
.note-popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
}

.usage-popup-title,
.note-popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.usage-popup-close,
.note-popup-close {
	padding: 8rpx;
}

.usage-popup-content,
.note-popup-content {
	color: #666;
	font-size: 28rpx;
}

.usage-item,
.note-item {
	margin-bottom: 24rpx;
}

.usage-title,
.note-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 16rpx;
}

.note-title-tips {
	display: inline;
	color: #fa4126;
}
.note-topic-tips {
	display: inline-flex;
	color: #fa4126;
	min-width: 230rpx;
}
.highlight {
	color: #fa4126;
}

.usage-detail,
.note-detail {
	margin-bottom: 12rpx;
	line-height: 1.6;
}

.goods-detail-page .desc-content__title {
	font-size: 28rpx;
	line-height: 36rpx;
	text-align: center;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 30rpx 20rpx;
}

.goods-detail-page .desc-content__title .img {
	width: 206rpx;
	height: 10rpx;
}

.goods-detail-page .desc-content__title--text {
	font-size: 26rpx;
	margin: 0 32rpx;
	font-weight: 600;
}

.goods-detail-page .desc-content__img {
	width: 100%;
	height: auto;
}

.goods-bottom-operation {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	background-color: #fff;
	padding-bottom: env(safe-area-inset-bottom);
	z-index: 999;
}

.popup-sku-header .popup-sku-header__goods-info .popup-sku__price {
	display: flex;
	align-items: baseline;
	color: #fa4126;
	margin-top: 48rpx;
}

.popup-sku-header
	.popup-sku-header__goods-info
	.popup-sku__price
	.popup-sku__price-num {
	font-size: 64rpx;
	color: #fa4126;
	font-weight: bold;
	font-family: DIN Alternate;
}

.popup-sku-header
	.popup-sku-header__goods-info
	.popup-sku__price
	.popup-sku__price-del {
	position: relative;
	font-weight: normal;
	left: 12rpx;
	bottom: 2rpx;
	color: #999999;
	font-size: 32rpx;
}

.popup-sku-header
	.popup-sku-header__goods-info
	.popup-sku__price
	.popup-sku__price-symbol {
	font-size: 36rpx;
	color: #fa4126;
}

.popup-sku-header
	.popup-sku-header__goods-info
	.popup-sku__price
	.popup-sku__price-max-num {
	font-size: 48rpx;
}

.goods-detail-page .goods-head {
	--td-swiper-radius: 0;
}

.t-toast__content {
	z-index: 12000 !important;
}

.comments-wrap {
	margin-top: 20rpx;
	padding: 32rpx;
	background-color: #fff;
}

.comments-wrap .comments-head {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
}

.comments-wrap .comments-head .comments-title-wrap {
	display: flex;
}

.comments-title-label,
.comments-title-count {
	color: #333333;
	font-size: 32rpx;
	font-weight: 500;
	line-height: 48rpx;
}

.comments-rate-wrap {
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 24rpx;
}

.comments-rate-wrap .comments-good-rate {
	color: #999999;
	font-size: 26rpx;
	font-weight: 400;
	font-style: normal;
	line-height: 36rpx;
}

.comment-item-wrap .comment-item-head {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-top: 32rpx;
}

.comment-item-wrap .comment-item-head .comment-item-avatar {
	width: 64rpx;
	height: 64rpx;
	border-radius: 64rpx;
}

.comment-item-wrap .comment-item-head .comment-head-right {
	margin-left: 24rpx;
}

.comment-head-right .comment-username {
	font-size: 26rpx;
	color: #333333;
	line-height: 36rpx;
	font-weight: 400;
}

.comment-item-wrap .comment-item-content {
	margin-top: 20rpx;
	color: #333333;
	line-height: 40rpx;
	font-size: 28rpx;
	font-weight: 400;
}

.flex {
	display: flex;
}

.items-center {
	align-items: center;
}

.justify-center {
	justify-content: center;
}

.mr32 {
	margin-right: 32rpx;
}

.flex-col {
	flex-direction: column;
}

.sale-price-wrapper {
	align-items: flex-end;
}

.sale-price {
	font-weight: 800;
	font-size: 36rpx;
	color: #0c0b0b;
}

/* 
.t-count-down--default {
	color: #fa4126;
} */

.guide {
	margin-top: 40rpx;
	margin-bottom: 20rpx;
}

.t-steps-item__title--default,
.t-steps-item__title--process {
	color: #fa4126 !important;
}

.t-steps-item__circle--process {
	background-color: #fa4126 !important;
}

/* 更新订单确认弹窗样式 */
.order-popup-container {
	position: relative;
	width: 100%;
	background-color: #fff;
	border-radius: 24rpx 24rpx 0 0;
	padding-bottom: 40rpx;
}

.order-popup-close {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	z-index: 10;
	padding: 12rpx;
}

.order-popup-header {
	padding: 40rpx 30rpx 20rpx;
}

.order-popup-title {
	font-size: 32rpx;
	font-weight: bold;
	text-align: center;
	margin-bottom: 30rpx;
}

.order-warning-text {
	background-color: #fff8e8;
	color: #fa8c16;
	font-size: 26rpx;
	padding: 16rpx 20rpx;
	line-height: 1.5;
	border-radius: 8rpx;
}

.order-popup-content {
	padding: 20rpx 30rpx;
}

.merchant-name {
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 16rpx;
}

.product-info {
	display: flex;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}

.product-image-container {
	width: 160rpx;
	height: 160rpx;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.product-image {
	width: 160rpx;
	height: 160rpx;
	border-radius: 8rpx;
}

.product-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.product-name {
	font-size: 30rpx;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.product-desc {
	font-size: 26rpx;
	color: #888;
	margin-bottom: 12rpx;
	color: #fa8c16;
}

.product-price {
	display: flex;
	align-items: center;
}

.current-price {
	font-size: 32rpx;
	color: #fa4126;
	font-weight: bold;
	margin-right: 16rpx;
}

.activity-info {
	padding: 20rpx 0;
}

.activity-item {
	display: flex;
	justify-content: space-between;
	padding: 16rpx 0;
	font-size: 28rpx;
}

.activity-label {
	color: #666;
	flex-shrink: 0;
}

.activity-value {
	color: #333;
	text-align: right;
}

.order-popup-footer {
	padding: 20rpx 30rpx;
	text-align: center;
}

.order-total {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	background-color: #fa4126;
	color: #fff;
	font-size: 30rpx;
	font-weight: bold;
	border-radius: 40rpx;
}

.consumption-info {
	margin-top: 16rpx;
	padding: 20rpx;
	background-color: #f8f8f8;
	border-radius: 8rpx;
}

.note-text {
	font-size: 24rpx;
	color: #999;
	margin-top: 8rpx;
	text-align: right;
	color: #fa8c16;
}

.disabled {
	background: #b3b3b3;
}

.flex {
	display: flex;
}

.items-center {
	align-items: center;
}

.justify-center {
	justify-content: center;
}

.view-more {
	margin-left: auto;
	margin-right: 10rpx;
}

.experience-rules {
	padding: 22rpx;
	line-height: 48rpx;
	margin-bottom: 60rpx;
}

.rules {
	display: none;
}

.note-detail {
	display: flex;
}

.copy-btn {
	margin-left: auto;
}

.desc-detail {
	display: flex;
	align-items: items-center;
	justify-content: flex-start;
	padding-left: 22rpx;
	padding-right: 22rpx;
	margin-bottom: 48rpx;
	padding-top: 48rpx;
}

.desc-label {
	font-weight: bold;
}

.rules-title {
	font-weight: bold;
}

.desc-box {
	padding-left: 22rpx;
	padding-right: 22rpx;
}

.bold {
	font-weight: bold;
}

.hl {
	color: #fa8c16;
}

.hl-warning {
	color: red;
}

.requirements-text {
	font-size: 24rpx;
	margin-left: 10rpx;
	color: #fa8c16;
}

.warning {
	display: inline-flex;
	color: red;
}

.ml-20 {
	margin-left: 20rpx;
}

.t-toast {
	--td-toast-max-width: 800rpx;
}
