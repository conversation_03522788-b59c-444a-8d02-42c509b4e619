<import src="./templates/1.wxml" />
<import src="./templates/2.wxml" />
<import src="./templates/3.wxml" />
<!-- 
<template is="t1" wx:if="{{true}}" />
<template is="t2" wx:if="{{promotion.promotion_type === 2}}" />
<template is="t3" wx:if="{{promotion.promotion_type === 3}}" /> -->


<wxs module="filter" src="../../utils/util.wxs"></wxs>

<view class="goods-detail-page">
  <t-pull-down-refresh value="{{enable}}" loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}" usingCustomNavbar
    bind:refresh="onPullDownRefresh">
    <view class="goods-head">
      <t-swiper wx:if="{{promotion.product_id.images.length > 0}}" height="750rpx" current="{{current}}"
        autoplay="{{autoplay}}" duration="{{duration}}" interval="{{interval}}" navigation="{{navigation}}"
        list="{{promotion.product_id.images}}"></t-swiper>
      <view class="guide">
        <t-steps current="{{0}}" readonly="true" wx:if="{{!miniProgram.version}}">
          <t-step-item wx:for="{{steps1}}" wx:key="index" title="{{item.title}}" content="{{item.subTitle}}" />
        </t-steps>
      </view>

      <view class="goods-info">
        <view class="goods-number">
          <view class="goods-price">
            <price wr-class="class-goods-price" symbol-class="class-goods-symbol" price="{{promotion.current_price }}"
              type="lighter" />
            <!-- <view class="goods-price-up">起</view> -->
            <price wr-class="class-goods-del" price="{{promotion.original_price}}" type="delthrough" />
          </view>
          <!-- <view class="sold-num">已售{{soldNum}}</view> -->
          <view class="flex mr32 flex-col sale-price-wrapper" style="align-items: flex-end;"
            wx:if="{{!miniProgram.version}}">
            <view class="flex">美团/大众同款在售套餐</view>
            <view class="flex items-center">
              <text>团购价</text><text class="sale-price">¥{{promotion.sale_price}}</text>
            </view>
          </view>
          <!-- <view class="flex items-center mr32">
            <view style="display:flex; color:#fa4126;margin-right:10rpx;">离结束</view>
            <t-count-down time="{{ millisecondsUntilEndOfDay }}" />
          </view> -->
        </view>

        <!-- <view wx:if="{{activityList.length > 0}}" class="goods-activity" bindtap="showPromotionPopup">
          <view class="tags-container">
            <view wx:for="{{activityList}}" data-promotionId="{{item.promotionId}}" wx:key="index" wx:if="{{index<4}}">
              <view class="goods-activity-tag">{{item.tag}}</view>
            </view>
          </view>
          <view class="activity-show">
            <view class="activity-show-text">领劵</view>
            <t-icon name="chevron-right" size="42rpx" />
          </view>
        </view> -->
        <view class="goods-title">
          <view class="goods-name">{{promotion.name}}</view>
          <!-- <view class="goods-tag" wx:if="{{!miniProgram.version}}">
            <t-button open-type="share" t-class="shareBtn" variant="text">
              <view class="btn-icon">
                <t-icon name="share" size="40rpx" color="#000" />
                <view class="share-text">分享</view>
              </view>
            </t-button>
          </view> -->
        </view>
        <view class="goods-intro">{{intro}}</view>
      </view>

      <!-- 店铺信息卡片 -->
      <view class="shop-info-card">
        <view class="shop-info-center">
          <view class="shop-info-header">
            <text class="shop-name">{{promotion.shop_id.name }}</text>

            <view class="favorite-btn" bindtap="toggleFavoriteAction">
              <t-icon name="{{isShopFavorited ? 'heart-filled' : 'heart'}}"
                color="{{isShopFavorited ? '#FF4E42' : '#999'}}" size="40rpx" />
            </view>
            <view catch:tap="navToStore">
              <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
            </view>
          </view>
          <view class="shop-rating">
            <view class="rating-tag">{{promotion.shop_id.tag }}</view>
            <view class="rating-score">
              <t-rate value="{{promotion.shop_id.star_rating}}" size="16" gap="2" color="{{['#ffc51c', '#ddd']}}"
                variant="filled" allow-half readonly disabled />
              <!-- <t-icon name="star-filled" color="#FF4E42" size="28rpx"/> -->
              <view class="score">{{ promotion.shop_id.star_rating / 1.0 }}</view>
            </view>
          </view>
          <view class="shop-time">
            <t-icon name="time" size="28rpx" />
            <view>{{promotion.shop_id.business_hours || ''}} <view class="warning ml-20">下单后请在次日内完成核销</view>
            </view>
          </view>
          <view class="shop-address">
            <view class="address-info" style="flex: 1; margin-right: 20rpx;">
              <t-icon name="location" size="28rpx" />
              <text>距离{{distance}} · {{promotion.shop_id.address || '江汉区青年路江宸天街负一楼'}}</text>
            </view>
            <view class="address-actions" style="display: flex; gap: 20rpx;">
              <view class="action-btn" bindtap="handleNavigation">
                <t-icon name="map" size="28rpx" />
                <text>导航</text>
              </view>
              <view class="action-btn" bindtap="handleCall">
                <t-icon name="call" size="28rpx" />
                <text>电话</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- <view class="spu-select" bindtap="showSkuSelectPopup">
        <view class="label">已选</view>
        <view class="content">
          <view class="{{!selectedAttrStr ? 'tintColor' : ''}}">
            {{selectedAttrStr ? buyNum : ''}}{{selectedAttrStr || '请选择'}}
          </view>
          <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
        </view>
      </view> -->
      <!-- <view wx:if="{{ commentsStatistics.commentCount > 0 }}" class="comments-wrap">
        <view class="comments-head" bindtap="navToCommentsListPage">
          <view class="comments-title-wrap">
            <view class="comments-title-label">商品评价</view>
            <view class="comments-title-count"> ({{ commentsStatistics.commentCount }}) </view>
          </view>
          <view class="comments-rate-wrap">
            <view class="comments-good-rate">{{commentsStatistics.goodRate}}% 好评</view>
            <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
          </view>
        </view>
        <view class="comment-item-wrap" wx:for="{{ commentsList }}" wx:for-item="commentItem" wx:key="goodsSpu">
          <view class="comment-item-head">
            <t-image src="{{commentItem.userHeadUrl}}" t-class="comment-item-avatar" />
            <view class="comment-head-right">
              <view class="comment-username">{{commentItem.userName}}</view>
              <t-rate
                value="{{ commentItem.commentScore }}"
                count="{{5}}"
                size="12"
                gap="2"
                color="{{['#ffc51c', '#ddd']}}"
              />
            </view>
          </view>
          <view class="comment-item-content"> {{commentItem.commentContent}} </view>
        </view>
      </view> -->
    </view>
    <view class="desc-content">
      <view class="desc-content__title" wx:if="{{details.desc.length > 0}}">
        <t-image t-class="img" src="{{recLeftImg}}" />
        <view class="desc-content__title--text">详情介绍</view>
        <t-image t-class="img" src="{{recRightImg}}" />
      </view>
      <!-- <view wx:if="{{details.desc.length > 0}}" wx:for="{{details.desc}}" wx:key="index">
        <t-image t-class="desc-content__img" src="{{item}}" mode="widthFix" />
      </view> -->
      <view class="desc-detail">
        <rich-text nodes="{{promotion.product_id.detail}}"></rich-text>
      </view>

      <view class="desc-box" wx:if="{{!miniProgram.version}}">
        <view class="flex items-center justify-between">
          <view class="desc-label">使用须知：</view>
          <view class="flex">{{promotion.note_text || "免预约︱周一到周日可用"}}</view>
          <view class="view-more" bindtap="handleUsagePopupShow">详情 ></view>
        </view>

        <view class="flex items-center justify-between">
          <view class="desc-label">购买平台：</view>
          <view class="flex">在优探生活购买套餐</view>
        </view>

        <view class="flex items-center justify-between">
          <view class="desc-label">笔记要求：</view>
          <t-image src="https://636c-cloud1-0gpy573m8caa7db3-**********.tcb.qcloud.la/app/dazhongdianping.svg"
            width="16" height="16" mode="aspectFill" style="display: flex;"
            wx:if="{{promotion.platform === 'dianping'}}" />
          <t-image src="https://636c-cloud1-0gpy573m8caa7db3-**********.tcb.qcloud.la/app/xiaohongshu.svg" width="16"
            height="16" mode="aspectFill" style="display: flex;" wx:if="{{promotion.platform === 'xiaohongshu'}}" />
          <text class="requirements-text warning"
            wx:if="{{!miniProgram.version}}">{{filter.formatPlatform(promotion.platform)}}{{filter.formatRequirements(promotion.requires,
            promotion.level, promotion.followers_count)}}</text>
          <!-- <view class="flex">请真实填写{{filter.formatPlatform(promotion.platform)}}笔记</view> -->
          <view class="view-more" bindtap="handleNotePopupShow">详情 ></view>
        </view>
      </view>

      <view class="rules desc-content__title" wx:if="{{!miniProgram.version}}">
        <t-image t-class="img" src="{{recLeftImg}}" />
        <view class="desc-content__title--text">体验说明</view>
        <t-image t-class="img" src="{{recRightImg}}" />
      </view>

      <view class="experience-rules" wx:if="{{!miniProgram.version}}">
        <!-- 参与流程 -->
        <view class="rules-section">
          <view class="rules-title">参与流程</view>
          <view class="process-steps">
            <text>①免费报名</text>
            <text>②到店体验(支付核销)</text>
            <text>③订单检测</text>
            <text>④发布笔记</text>
          </view>
        </view>

        <!-- 报名规则 -->
        <view class="rules-section">
          <view class="rules-title">报名规则</view>
          <view class="rules-content">
            <view class="rule-item">1. 同一账号，最多同时参与3个探店活动，订单完成后可继续报名新活动;</view>
            <view class="rule-item">2. 下单需要消耗1张探店券，<text class="hl">订单取消后不返还;</text></view>
            <view class="rule-item">3. 探店券每天凌晨重新发放。</view>
          </view>
        </view>

        <!-- 体验规则 -->
        <view class="rules-section">
          <view class="rules-title">体验规则</view>
          <view class="rules-content">
            <view class="rule-item">1. 报名成功后需要在<text class="hl">次日24点前完成核销</text>(具体以订单显示核销创计时为准)，<text
                class="hl">逾期将自动取消订单并影响下次报名;</text></view>
            <view class="rule-item">2. 若无法按时到店，请主动取消订单，避免影响后续报名活动;</view>
            <view class="rule-item">3. <text class="hl">到店体验前先联系商家确认是否营业可接待，避免跑空。</text></view>
            <view class="rule-item">4. <text class="hl">禁止多设备探店(一人一部)</text></view>
          </view>
        </view>

        <!-- 笔记规则 -->
        <view class="rules-section">
          <view class="rules-title">笔记规则</view>
          <view class="rules-content">
            <view class="rule-item">1. 笔记上传时间：探店笔记需在<text class="hl">到店体验后72小时内</text>完成发布并上传，否则将影响下次报名活动;</view>
            <view class="rule-item">2. <text
                class="hl">笔记内容：探店笔记仅支持真实到店体验反馈，内容必须根据活动要求满足笔记字数、图片数量、携带话题及定位</text>，若不满足活动要求将无法获得返现金额;</view>
            <view class="rule-item">3. 笔记审核：笔记内容不符合活动要求将被驳回，<text class="hl">单笔订单累计驳回3次将被自动取消;</text></view>
            <view class="rule-item">4. 笔记多次不符合活动要求、恶意宣传商户或平台误导他人，将视具体情况禁用30天起（优探生活）达人身份，无法参与探店;</view>
            <view class="rule-item">5. 参与本平台活动的笔记，可能会被平台引用、展示，如有侵权可联系在线客服进行删除。</view>
          </view>
        </view>

        <!-- 禁止行为规则 -->
        <view class="rules-section">
          <view class="rules-title">禁止行为规则</view>
          <view class="rules-content">
            <view class="rule-item">1. 严禁使用AI工具生成、复制他人内容、帮买帮体验、多平台返现等虚假行为;</view>
            <view class="rule-item">2. 探店笔记审核通过后60天不可修改或撤销;</view>
            <view class="rule-item">3. 严禁通过在（优探生活）就餐后在任何平台给商户打分写评价(特殊店铺活动除外);</view>
            <view class="rule-item">4. 仅支持通过正常发笔记涨粉的账号，互粉、买粉等虚假涨粉行为，一旦检测出将会被取消优探生活达人身份;</view>
            <view class="rule-item">5. 参与本平台活动的笔记，可能会被平台引用、展示，如有侵权可联系在线客服进行删除。</view>
            <view class="rule-item warning"><text class="hl">*以上行为一经发现将关小黑屋处理，无法参与所有活动！</text></view>
          </view>
        </view>
      </view>
    </view>

  </t-pull-down-refresh>

  <view class="goods-bottom-operation">
    <buy-bar isUpdatingStock="{{isUpdatingStock}}" jumpArray="{{jumpArray}}" soldout="{{soldout}}" isStock="{{isStock}}"
      shopCartNum="{{cartNum}}" originalPrice="{{promotion.original_price}}" salePrice="{{promotion.sale_price}}"
      discountPrice="{{promotion.discount_price}}" quantity="{{promotion.realtime_quantity}}"
      buttonType="{{buttonType}}" bind:toAddCart="toAddCart" bind:toNav="toNav" bind:toBuyNow="buyItNow"
      buyBtnDisabled="buyBtnDisabled" class="goods-details-card" shareProfit="{{shareProfit}}" />
  </view>
  <!-- <goods-specs-popup
    id="goodsSpecsPopup"
    show="{{isSpuSelectPopupShow}}"
    title="{{details.title || ''}}"
    src="{{specImg ? specImg : primaryImage}}"
    specList="{{details.specList || []}}"
    skuList="{{skuArray}}"
    limitBuyInfo="{{details.limitInfo[0].text || ''}}"
    bind:closeSpecsPopup="handlePopupHide"
    bind:change="chooseSpecItem"
    bind:changeNum="changeNum"
    bind:addCart="addCart"
    bind:buyNow="gotoBuy"
    bind:specsConfirm="specsConfirm"
    isStock="{{isStock}}"
    outOperateStatus="{{outOperateStatus}}"
  >
    <view slot="goods-price">
      <view class="popup-sku__price">
        <price
          wx:if="{{!isAllSelectedSku || (!promotionSubCode && isAllSelectedSku)}}"
          price="{{selectSkuSellsPrice ? selectSkuSellsPrice : minSalePrice }}"
          wr-class="popup-sku__price-num"
          symbol-class="popup-sku__price-symbol"
        />
        <price
          wx:if="{{selectSkuSellsPrice === 0 && minSalePrice !== maxSalePrice && !isAllSelectedSku}}"
          price="{{maxSalePrice}}"
          wr-class="popup-sku__price-del"
          type="delthrough"
        />
      </view>
    </view>
  </goods-specs-popup>
  <promotion-popup
    list="{{list}}"
    bind:closePromotionPopup="closePromotionPopup"
    show="{{isShowPromotionPop}}"
    bind:promotionChange="promotionChange"
  /> -->

</view>


<t-toast id="t-toast" />

<!-- 使用须知弹窗 -->
<t-popup visible="{{isUsagePopupShow}}" placement="bottom" bind:visible-change="handleUsagePopupHide">
  <view class="usage-popup-container">
    <view class="usage-popup-header">
      <view class="usage-popup-title">使用须知</view>
      <view class="usage-popup-close" bindtap="handleUsagePopupHide">
        <t-icon name="close" size="36rpx" />
      </view>
    </view>
    <view class="usage-popup-content">
      <view class="usage-item">
        <view class="usage-title">禁止事项</view>
        <view class="usage-detail">1. 禁止私下给商家评价，发现拉黑</view>
        <view class="usage-detail">2. 禁止代探店、转让探店码</view>
        <view class="usage-detail">3. 禁止非外带店直接打包</view>
      </view>
      <view class="usage-item">
        <view class="usage-title">使用时间</view>
        <view class="usage-detail">{{promotion.shop_id.business_hours}} <view class="warning ml-20">下单后请在次日内完成核销
          </view>
        </view>
      </view>
      <view class="usage-item">
        <view class="usage-title">预约规则</view>
        <view class="usage-detail">{{ promotion.note_text || "免预约︱周一到周日可用" }}</view>
      </view>
    </view>
  </view>
</t-popup>

<!-- 笔记要求弹窗 -->
<t-popup visible="{{isNotePopupShow}}" placement="bottom" bind:visible-change="handleNotePopupHide">
  <view class="note-popup-container">
    <view class="note-popup-header">
      <view class="note-popup-title">笔记要求</view>
      <view class="note-popup-close" bindtap="handleNotePopupHide">
        <t-icon name="close" size="36rpx" />
      </view>
    </view>
    <view class="note-popup-content">
      <view class="note-item">
        <view class="note-title">温馨提示</view>
        <view class="note-detail warning">图片是重点，可参考门店大众点评或小红书优质笔记</view>
      </view>
      <view class="note-item">
        <view class="note-title">平台要求</view>
        <view class="note-detail">
          {{filter.formatPlatform(promotion.platform)}}，{{filter.formatRequirements(promotion.requires,
          promotion.level,
          promotion.followers_count)}}</view>
      </view>
      <view class="note-item">
        <view class="note-title">图片要求</view>
        <view class="note-detail">1. 图片≥{{promotion.images_count || 9}}张，尺寸需3:4竖图</view>
        <view class="note-detail">2. 封面图高清，带滤镜，其它图片需清晰好看</view>
        <view class="note-detail">3. 建议包含产品整体、产品特写和店铺环境等</view>
        <view class="note-detail highlight">4. 不要不经思考随手一拍，不要菜没齐空桌拍菜品，不要空场拍环境显冷清</view>
      </view>
      <view class="note-item">
        <view class="note-title">文案要求</view>
        <view class="note-detail">1. 字数≥{{promotion.text_count || 100}}字</view>
        <view class="note-detail highlight">2. 分享真实消费体验; 建议从个人感受、店铺环境、餐品口味、服务态度等方面展开</view>
      </view>
      <view class="note-item">
        <view class="note-title">门店定位</view>
        <view class="flex items-center justify-between">
          <view class="note-detail">{{promotion.shop_id.name}}</view>
        </view>
      </view>
      <view class="note-item" wx:if="{{promotion.topics}}">
        <view class="note-title">话题标签 <view class="note-title-tips">(请务必在文案底部加上标签)</view>
        </view>
        <view class="note-detail">{{promotion.topics}}<view class="note-topic-tips">再加2-3个热门标签</view>
        </view>
        <!-- <view class="copy-btn" catchtap="onCopyTopics">复制</view> -->
      </view>
    </view>
  </view>
</t-popup>

<!-- 订单确认弹窗 -->
<t-popup visible="{{isOrderPopupShow}}" placement="bottom" bind:visible-change="handleOrderPopupHide">
  <view class="order-popup-container">
    <view class="order-popup-close" bindtap="handleOrderPopupHide">
      <t-icon name="close" size="36rpx" />
    </view>

    <!-- 顶部标题和警告信息 -->
    <view class="order-popup-header">
      <view class="order-popup-title">确认订单</view>
      <!-- <t-notice-bar visible="{{true}}" theme="warning" content="严禁代探店、转让探店码、非外带店直接打包等消极探店行为，一经发现关小黑屋处理！"></t-notice-bar> -->
      <view class="order-warning-text">
        严禁代探店、转让探店码、非外带店直接打包等消极探店行为，一经发现关小黑屋处理！
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="order-popup-content">
      <view class="merchant-name">{{promotion.shop_id.name}}</view>
      <view class="product-info">
        <view class="product-image-container">
          <image src="{{promotion.product_id.images[0]}}" t-class="product-image" style="width:160rpx;height:160rpx" />
        </view>
        <view class="product-details">
          <view class="product-name">{{promotion.product_id.name}}</view>
          <!-- <view class="product-desc">禁止给商家mt、dp写评价，发现拉黑</view> -->
          <view class="product-price">
            <text class="current-price">¥ {{promotion.sale_price}}</text>
            <text class="original-price">当前探店价</text>
            <view style="margin-left:auto;display:flex;gap:10rpx;color:#FFF">
              <view style="background-color:#FA8C16;padding:0 10rpx">随时退</view>
              <view style="background-color:#FA8C16;padding:0 10rpx">过期退</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 活动信息 -->
      <view class="activity-info">
        <view class="activity-item" wx:if="{{!miniProgram.version}}">
          <view class="activity-label">活动要求</view>
          <view class="activity-value">请真实填写{{filter.formatPlatform(promotion.platform)}}笔记</view>
        </view>
        <view class="activity-item" wx:if="{{!miniProgram.version}}">
          <view class="activity-label">报名要求</view>
          <view class="activity-value" style="color:#FA8C16">{{filter.formatPlatform(promotion.platform)}}
            {{filter.formatRequirements(promotion.requires, promotion.level, promotion.followers_count)}}</view>
        </view>
        <view class="activity-item" wx:if="{{!miniProgram.version}}">
          <view class="activity-label">购买平台</view>
          <view class="activity-value">在本平台购买套餐</view>
        </view>
        <view class="activity-item">
          <view class="activity-label">随时退</view>
          <view class="activity-value" style="color:#FA8C16">超时取消将影响下一次报名</view>
        </view>
        <view class="activity-item">
          <view class="activity-label">活动返利</view>
          <view class="activity-value">先付¥{{promotion.sale_price}}完成后返¥{{promotion.discount_price}}</view>
        </view>
      </view>

      <!-- 需消耗信息 -->
      <view class="consumption-info">
        <view class="activity-item">
          <view class="activity-label">需消耗</view>
          <view class="activity-value">1探店券</view>
        </view>
        <view class="note-text">订单完成后将赠送积分，积分可以用来兑换探店券</view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="order-popup-footer">
      <view class="order-total {{submitting || buyBtnDisabled ? 'disabled' : '' }} " bindtap="submitOrder">
        {{ submitting ? "提交中..." : "提交订单 ¥" + promotion.sale_price }}
      </view>
    </view>
  </view>
</t-popup>

<!-- 绑定账号弹窗 -->
<t-dialog t-class="wrapper" visible="{{showBindDialog}}"
  content="你还未绑定{{filter.formatPlatform(promotion.platform)}}账号，是否前往绑定?"
  confirm-btn="{{ {content: '确定', variant: 'base' } }}" cancel-btn="取消" bind:confirm="goToBind"
  bind:cancel="closeBindDialog" style="color: red" custom-style="color: red">
  <t-image slot="top" t-class="dialog-image"
    src="cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-**********/app/confirm.jpg" width="310"
    height="160" />
</t-dialog>


<t-dialog t-class="wrapper" visible="{{showBindPhoneDialog}}" content="你还未绑定手机号，是否前往绑定?"
  confirm-btn="{{ {content: '确定', variant: 'base' } }}" cancel-btn="取消" bind:confirm="goToBindPhone"
  bind:cancel="closeBindPhoneDialog" style="color: red" custom-style="color: red">
</t-dialog>