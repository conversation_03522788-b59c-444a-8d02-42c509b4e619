<wxs module="filter" src="../../../utils/util.wxs"></wxs>

<template name="t3">
  <view class="desc-box" wx:if="{{!miniProgram.version}}">
    <view class="flex items-center justify-between highlight">
      <view class="desc-label">商家接待时间：</view>
      <view class="flex">{{ filter.formatDate(promotion.verify_start_date) }}至{{ filter.formatDate(promotion.verify_end_date) }}</view>
      <!-- <view class="view-more" bindtap="handleUsagePopupShow">详情 ></view> -->
    </view>

    <view class="flex items-center justify-between">
      <view class="desc-label">使用须知：</view>
      <view class="flex">{{promotion.note_text || "免预约︱周一到周日可用"}}</view>
      <!-- <view class="view-more" bindtap="handleUsagePopupShow">详情 ></view> -->
    </view>

    <view class="flex items-center justify-between">
      <view class="desc-label">购买平台：</view>
      <view class="flex">在优探生活购买套餐</view>
    </view>

    <!-- <view class="flex items-center justify-between">
      <view class="desc-label">笔记要求：</view>
      <t-image src="https://636c-cloud1-0gpy573m8caa7db3-**********.tcb.qcloud.la/app/dazhongdianping.svg" width="16"
        height="16" mode="aspectFill" style="display: flex;" wx:if="{{promotion.platform === 'dianping'}}" />
      <t-image src="https://636c-cloud1-0gpy573m8caa7db3-**********.tcb.qcloud.la/app/xiaohongshu.svg" width="16"
        height="16" mode="aspectFill" style="display: flex;" wx:if="{{promotion.platform === 'xiaohongshu'}}" />
      <text class="requirements-text warning"
        wx:if="{{!miniProgram.version}}">{{filter.formatPlatform(promotion.platform)}}{{filter.formatRequirements(promotion.requires,
        promotion.level, promotion.followers_count)}}</text>
      <view class="view-more" bindtap="handleNotePopupShow">详情 ></view>
    </view> -->
  </view>

  <view class="rules desc-content__title" wx:if="{{!miniProgram.version}}">
    <t-image t-class="img" src="https://636c-cloud1-0gpy573m8caa7db3-**********.tcb.qcloud.la/app/rec-left.png" />
    <view class="desc-content__title--text">购买须知</view>
    <t-image t-class="img" src="https://636c-cloud1-0gpy573m8caa7db3-**********.tcb.qcloud.la/app/rec-right.png" />
  </view>

  <view class="experience-rules" wx:if="{{!miniProgram.version}}">
    <view class="rules-section">
      <view class="rules-title">【有效日期】</view>
      <view class="process-steps">
        <text>下单后30天内有效，超期作废</text>
      </view>
    </view>

    <view class="rules-section">
      <view class="rules-title">【退款规则】</view>
      <view class="rules-content">
        <view class="rule-item hl">本商品因限量售卖，且为低价秒杀，故不支持退款。如不能遵守规则，请将机会名额留给其他用户，谢谢理解，超期作废不支持退款</view>
      </view>
    </view>

    <view class="rules-section">
      <view class="rules-title">【客服】</view>
      <view class="rules-content">
        <view class="rule-item">平台联系在线客服: 点击个人中心-联系客服</view>
        <view class="rule-item">工作时间: 工作日10:00-18:00</view>
      </view>
    </view>

    <view class="rules-section">
      <view class="rules-title">【其他说明】</view>
      <view class="rules-content">
        <view class="rule-item">
          <text class="hl1">1、每桌仅限使用1张，限堂食，不可外带，不与店内其他优惠活动同享;</text>
        </view>
        <view class="rule-item">
          <text class="hl1">2、严禁通过优探生活就餐后于第三方平台撰写差评，如有问题先和客服沟通；一经发现恶意写差评，账号封禁处理;</text>
        </view>
        <view class="rule-item">
          <text class="hl1">3、出于食材管理和用餐安全考虑，谢绝自带食材;</text>
        </view>
        <view class="rule-item">
          <text class="hl1">4、实物请以商家提供为准，图片仅供参考;</text>
        </view>
        <view class="rule-item">
          <text class="hl1">5、请在点单前出示优探生活订单，否则商家有权要求以正常价格买单;</text>
        </view>
        <view class="rule-item">
          <text class="hl1">6、如果店里实际就餐内容与页面展示内容严重不符，请拍照取证，联系官方客服协商处理;
          </text>
        </view>
        <view class="rule-item">
          <text class="hl1">7、有效期内订单如遇商家倒闭、包场、解约、用户到店商家不予接待等其他特殊情况，我司将全额退款;</text>
        </view>
      </view>
    </view>

  </view>
</template>