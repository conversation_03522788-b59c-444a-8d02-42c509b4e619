<view class="flex soldout flex-center wr-sold-out" wx:if="{{soldout ||  !isStock}}">
	{{soldout ? '商品已下架' : '商品已售馨'}}
</view>
<view class="footer-cont flex flex-between wr-class">
	<view class="flex flex-between bottom-operate-left" wx:if="{{jumpArray.length > 0}}">
		<view wx:for="{{jumpArray}}" wx:key="index" class="icon-warp operate-wrap" bindtap="toNav"
			data-ele="foot_navigation" data-index="{{index}}" data-url="{{item.url}}">
			<view>
				<text wx:if="{{shopCartNum > 0 && item.showCartNum}}" class="tag-cart-num">
					{{shopCartNum > 99 ? '99+' : shopCartNum}}
				</text>
				<t-icon prefix="wr" name="{{item.iconName}}" size="40rpx" />
				<view class="operate-text">{{item.title}}</view>
			</view>
		</view>
	</view>

	<block wx:if="{{isUpdatingStock}}">
		<view class="flex buy-buttons">
			<view class="flex flex-col items-center justify-center bar-buy disabled" bindtap="toBuyNow">
				<view class="flex">00:10:00后可抢</view>
			</view>
		</view>
	</block>

	<block wx:if="{{!isUpdatingStock}}">
		<view class="flex buy-buttons">
			<block wx:if="{{promotion.promotion_type === 'ykj'}}">
				<block wx:if="{{miniProgram.envVersion === 'release'}}">
					<view wx:if="{{quantity < 1}}"
						class="flex flex-col items-center justify-center bar-buy disabled {{soldout || !isStock ? 'bar-buyNow-disabled' : '' }}"
						bindtap="toBuyNow">
						<view class="flex">¥ {{salePrice}}</view>
						<view class="flex">今日已抢完 | 付{{salePrice}}返{{discountPrice}}</view>
					</view>

					<view wx:else
						class="flex flex-col items-center justify-center bar-buy {{soldout || !isStock ? 'bar-buyNow-disabled' : '' }}"
						bindtap="toBuyNow">
						<view class="flex">¥ {{salePrice}}</view>
						<view class="flex">剩余{{quantity}}份 | 付{{salePrice}}返{{discountPrice}}</view>
					</view>

					<view class="shareBtn">
						<button open-type="share">
							<view class="flex flex-col items-center justify-center">
								<view class="flex">分享</view>
								<view class="flex" style="font-size: 20rpx;">预计赚{{shareProfit}}+</view>
							</view>
						</button>
					</view>
				</block>

				<block wx:if="{{miniProgram.envVersion !== 'release'}}">
					<view wx:if="{{quantity < 1}}"
						class="flex flex-col items-center justify-center bar-buy disabled {{soldout || !isStock ? 'bar-buyNow-disabled' : '' }}"
						bindtap="toBuyNow">
						<view class="flex">¥ {{salePrice}}</view>
						<view class="flex">已售罄</view>
					</view>

					<view wx:else
						class="flex flex-col items-center justify-center bar-buy {{soldout || !isStock ? 'bar-buyNow-disabled' : '' }} bar-buy-full"
						bindtap="toBuyNow3">
						<view class="flex">立即下单 ¥ {{salePrice}}</view>
					</view>

					<view class="shareBtn" wx:if="{{!miniProgram.version}}">
						<button open-type="share">
							<view class="flex flex-col items-center justify-center">
								<view class="flex">分享</view>
								<view class="flex" style="font-size: 20rpx;">预计赚{{shareProfit}}+</view>
							</view>
						</button>
					</view>
				</block>
			</block>
			<block wx:else>
				<block wx:if="{{miniProgram.envVersion === 'release'}}">
					<view wx:if="{{quantity < 1}}"
						class="flex flex-col items-center justify-center bar-buy disabled {{soldout || !isStock ? 'bar-buyNow-disabled' : '' }}"
						bindtap="toBuyNow">
						<view class="flex">¥ {{salePrice}}</view>
						<view class="flex">今日已抢完 | 付{{salePrice}}返{{discountPrice}}</view>
					</view>

					<view wx:else
						class="flex flex-col items-center justify-center bar-buy {{soldout || !isStock ? 'bar-buyNow-disabled' : '' }}"
						bindtap="toBuyNow">
						<view class="flex">¥ {{salePrice}}</view>
						<view class="flex">剩余{{quantity}}份 | 付{{salePrice}}返{{discountPrice}}</view>
					</view>

					<view class="shareBtn">
						<button open-type="share">
							<view class="flex flex-col items-center justify-center">
								<view class="flex">分享</view>
								<view class="flex" style="font-size: 20rpx;">预计赚{{shareProfit}}+</view>
							</view>
						</button>
					</view>
				</block>

				<block wx:if="{{miniProgram.envVersion !== 'release'}}">
					<view wx:if="{{quantity < 1}}"
						class="flex flex-col items-center justify-center bar-buy disabled {{soldout || !isStock ? 'bar-buyNow-disabled' : '' }}"
						bindtap="toBuyNow">
						<view class="flex">¥ {{salePrice}}</view>
						<view class="flex">今日已抢完 | 付{{salePrice}}返{{discountPrice}}</view>
					</view>

					<view wx:else
						class="flex flex-col items-center justify-center bar-buy {{soldout || !isStock ? 'bar-buyNow-disabled' : '' }} bar-buy-full"
						bindtap="toBuyNow">
						<view class="flex">¥ {{salePrice}}</view>
						<view class="flex">剩余{{quantity}}份 | 付{{salePrice}}返{{discountPrice}}</view>
					</view>

					<view class="shareBtn" wx:if="{{!miniProgram.version}}">
						<button open-type="share">
							<view class="flex flex-col items-center justify-center">
								<view class="flex">分享</view>
								<view class="flex" style="font-size: 20rpx;">预计赚{{shareProfit}}+</view>
							</view>
						</button>
					</view>
				</block>
			</block>
		</view>
	</block>

	<block wx:if="{{isSlotButton}}">
		<slot name="buyButton" />
	</block>
</view>