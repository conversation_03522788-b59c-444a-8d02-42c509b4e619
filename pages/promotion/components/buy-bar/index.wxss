.footer-cont {
	background-color: #fff;
	padding: 16rpx;
}

.icon-warp {
	width: 110rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	text-align: center;
}

.operate-wrap {
	position: relative;
}

.bottom-operate-left {
	width: 100%;
}

.bottom-operate-left .icon-warp {
	width: 50%;
}

.tag-cart-num {
	display: inline-block;
	position: absolute;
	left: 50rpx;
	right: auto;
	top: 6rpx;
	color: #fff;
	line-height: 24rpx;
	text-align: center;
	z-index: 99;
	white-space: nowrap;
	min-width: 28rpx;
	border-radius: 14rpx;
	background-color: #fa550f !important;
	font-size: 20rpx;
	font-weight: 400;
	padding: 2rpx 6rpx;
}

.operate-text {
	color: #666;
	font-size: 20rpx;
}

.soldout {
	height: 80rpx;
	background: rgba(170, 170, 170, 1);
	width: 100%;
	color: #fff;
}

.addCart-disabled,
.bar-addCart-disabled {
	background: rgba(221, 221, 221, 1) !important;
	color: #fff !important;
	font-size: 28rpx;
}

.buyNow-disabled,
.bar-buyNow-disabled {
	background: rgba(198, 198, 198, 1) !important;
	color: #fff !important;
	font-size: 28rpx;
}

.bar-separately,
.bar-buy {
	/* width: 508rpx; */
	min-width: 380rpx;
	/* height: 90rpx; */
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
}

.bar-buy-full {
	width: 508rpx;
	border-radius: 40rpx 40rpx 40rpx 40rpx !important;
}

.bar-separately {
	background: #ffece9;
	color: #fa4126;
	border-radius: 40rpx 0 0 40rpx;
}

.bar-buy {
	background-color: #fa4126;
	border-radius: 40rpx 0 0 40rpx;
}

.flex {
	display: flex;
	display: -webkit-flex;
}

.flex-center {
	justify-content: center;
	-webkit-justify-content: center;
	align-items: center;
	-webkit-align-items: center;
}

.flex-between {
	justify-content: space-between;
	-webkit-justify-content: space-between;
}

.flex {
	display: flex;
}

.items-center {
	align-items: center;
}

.justify-center {
	justify-content: center;
}

.flex-col {
	flex-direction: column;
}

.disabled {
	background-color: #bbb6b6;
}

.share-btn {
	width: 160rpx;
	color: #fff;
	background-color: #fa4126;
	border-radius: 0 40rpx 40rpx 0;
}

.shareBtn {
	color: #fff;
	min-width: 200rpx;
	border-left: 1px solid #f7cfcf;
	border-radius: 0 40rpx 40rpx 0 !important;
	background-color: #fa4126;
}

.shareBtn .t-button {
	width: 160rpx;
	border-left: 1px solid #f7cfcf;
	border-radius: 0 40rpx 40rpx 0 !important;
	font-size: 26rpx;
	font-weight: 600;
}
.t-button--text {
	color: #fff;
	--td-button-default-color: #fff;
}

.t-button--default {
	/* background-color: #fa4126; */
}
