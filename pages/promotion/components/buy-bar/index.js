// const { getPromotion } = require("@/model/promotion");
Component({
  externalClasses: ['wr-sold-out', 'wr-class'],

  options: { multipleSlots: true },

  properties: {
    promotion: {
      type: Object,
      value: {}
    },
    isUpdatingStock: {
      type: Boolean,
      value: false,
    },
    soldout: {
      type: Boolean,
      value: false,
    },
    buyBtnDisabled: {
      type: Boolean,
      value: false,
    },
    jumpArray: {
      type: Array,
      value: [],
    },
    isStock: {
      type: Boolean,
      value: true,
    }, // 是否有库存
    isSlotButton: {
      type: Boolean,
      value: false,
    }, // 是否开启按钮插槽
    shopCartNum: {
      type: Number, // 购物车气泡数量
    },
    buttonType: {
      type: Number,
      value: 0,
    },
    minDiscountPrice: {
      type: String,
      value: '',
    },
    minSalePrice: {
      type: String,
      value: '',
    },
    originalPrice: {
      type: Number,
      value: 0,
    },
    salePrice: {
      type: Number,
      value: 0,
    },
    discountPrice: {
      type: Number,
      value: 0,
    },
    quantity: {
      type: Number,
      value: 0
    },
    shareProfit: {
      type: Number,
      value: 0
    },
    miniProgram: {
      type: Object,
      value: {}
    }
  },
  lifetimes: {
    ready() {
      const { miniProgram } = wx.getAccountInfoSync();
      this.setData({
        miniProgram
      })
    },
    attached() {
    },
    detached() {
    },
  },
  data: {
    fillPrice: false,
  },

  methods: {
    toAddCart() {
      const { isStock } = this.properties;
      if (!isStock) return;
      this.triggerEvent('toAddCart');
    },

    toBuyNow(e) {
      if (this.data.isUpdatingStock) {
        wx.showToast({
          title: '更新库存中，稍后再试',
          icon: 'none',
          duration: 2000
        })
        return
      }

      if (this.data.quantity < 1) {
        wx.showToast({
          title: '今日已抢完，请明日再来',
          icon: 'none',
          duration: 2000
        })
        return
      }

      const { isStock } = this.properties;
      if (!isStock) return;
      this.triggerEvent('toBuyNow', e);
    },

    toBuyNow3(e) {
      console.debug("toBuyNow3");
      if (this.data.isUpdatingStock) {
        wx.showToast({
          title: '更新库存中，稍后再试',
          icon: 'none',
          duration: 2000
        })
        return
      }

      // if (this.data.quantity < 1) {
      //   wx.showToast({
      //     title: '今日已抢完，请明日再来',
      //     icon: 'none',
      //     duration: 2000
      //   })
      //   return
      // }

      // const { isStock } = this.properties;
      // if (!isStock) return;
      console.debug("triggerEvent toBuyNow");
      this.triggerEvent('toBuyNow', e);
    },

    toNav(e) {
      const { url } = e.currentTarget.dataset;
      return this.triggerEvent('toNav', {
        e,
        url,
      });
    },
  },
});
