import { createStoreBindings } from "mobx-miniprogram-bindings";
import {
	productStore,
	cardStore,
	authStore,
	orderStore,
	userStore,
} from "../../stores/index";

import {
	estimateProfit,
	formatPlatform,
	formatRequirements,
} from "../../utils/util";
import { log } from "../../utils/log";

import Toast from "tdesign-miniprogram/toast/index";

import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
dayjs.extend(isBetween);

import { validateOrder } from './validation'

import { cdnBase } from "../../config/index";
const imgPrefix = `${cdnBase}/`;
const recLeftImg = `${imgPrefix}app/rec-left.png`;
const recRightImg = `${imgPrefix}app/rec-right.png`;

Page({
	data: {
		isUsagePopupShow: false,
		isNotePopupShow: false,
		isShowPromotionPop: false,
		isOrderPopupShow: false,
		jumpArray: [
			{
				title: "首页",
				url: "/pages/home/<USER>",
				iconName: "home",
			},
			{
				title: "客服",
				url: "/pages/customer-service/index",
				iconName: "service",
			},
		],
		recLeftImg,
		recRightImg,
		buttonType: 1,
		buyNum: 1,
		selectedAttrStr: "",
		skuArray: [],
		primaryImage: "",
		specImg: "",
		isSpuSelectPopupShow: false,
		isAllSelectedSku: false,
		buyType: 0,
		outOperateStatus: false, // 是否外层加入购物车
		operateType: 0,
		selectSkuSellsPrice: 0,
		maxLinePrice: 0,
		minSalePrice: 0,
		maxSalePrice: 0,
		list: [],
		spuId: "",
		navigation: { type: "fraction" },
		current: 0,
		autoplay: true,
		duration: 500,
		interval: 5000,
		soldNum: 0, // 已售数量
		details: {},
		productInfo: {},
		millisecondsUntilEndOfDay: 0,
		// 1. 抢名额(下单并支付) >> 2. 到店体验(核销验券) >> 3. 发布笔记 >> 4. 返利(审核后)
		steps1: [
			{
				title: "抢名额",
				subTitle: "下单并支付",
			},
			{
				title: "到店体验",
				subTitle: "核销验券",
			},
			{
				title: "发布笔记",
				subTitle: "注意不是评价",
			},
			{
				title: "返现",
				subTitle: "审核后",
			},
		],
		miniProgram: null,
		showBindPhoneDialog: false,
		backRefresh: false,
		shareProfit: 0,
		isUpdatingStock: false,
		isStock: true,
		soldout: false,
		buyBtnDisabled: false,
		submitting: false,
		buyedInHalfMonth: false,
	},

	handleIsUpdatingStockButton() {
		// 定义时间区间
		const startTimeStr = `${dayjs().format("YYYY-MM-DD")} 00:00:00`;
		const endTimeStr = `${dayjs().format("YYYY-MM-DD")} 00:10:00`;
		const startTime = dayjs(startTimeStr, "YYYY-MM-DD HH:mm:ss");
		const endTime = dayjs(endTimeStr, "YYYY-MM-DD HH:mm:ss");

		// 要检查的时间
		const checkTime = dayjs();
		// 判断是否在区间内（包含边界）
		const between = checkTime.isBetween(startTime, endTime, "second", "[]");

		if (between) {
			this.setData({
				isUpdatingStock: true,
			});
		} else {
			this.setData({
				isUpdatingStock: false,
			});
		}
	},

	handlePopupHide() {
		this.setData({
			isSpuSelectPopupShow: false,
		});
	},

	handleUsagePopupShow() {
		this.setData({
			isUsagePopupShow: true,
		});
	},

	handleUsagePopupHide() {
		this.setData({
			isUsagePopupShow: false,
		});
	},

	handleNotePopupShow() {
		this.setData({
			isNotePopupShow: true,
		});
	},

	handleNotePopupHide() {
		this.setData({
			isNotePopupShow: false,
		});
	},

	// 处理验证结果的辅助函数
	handleValidationResult(result) {
		console.debug("验证结果:", result);
		switch (result.action) {
			case 'showToast':
				wx.showToast({
					title: result.message,
					icon: "error",
					duration: 3000,
				});
				break;
			case 'showBindPhoneDialog':
				this.setData({
					showBindPhoneDialog: true,
				});
				break;
			case 'showBindCardDialog':
				this.setData({
					showBindCardDialog: true,
				});
				break;
			default:
				console.warn('未知的验证动作:', result.action);
		}
	},

	// 探店的下单按钮
	async buyItNow() {
		// await	this.refreshUserInfo();
		const { miniProgram } = wx.getAccountInfoSync();
		console.debug(this.data.userInfo)
		// if (miniProgram.version) {
			// 使用验证管道进行统一验证
			const validationResult = validateOrder(
				this.data.userInfo,
				this.data.promotion,
				this.data.cards
			);

			console.debug("validationResult:", validationResult);

			if (!validationResult.isValid) {
				this.handleValidationResult(validationResult);
				return;
			}

			// 所有验证都通过，显示订单弹窗
		// }

		this.setData({
			isOrderPopupShow: true,
		});
	},

	// 特价团的立即下单按钮
	async buyItNow3() {
		// 要电话
		// 不需要名片
		// 跳转到下单确认页面

		wx.navigateTo({
			url: `/pages/promotion/order-confirm/index?id=${this.data.promotion._id}`
		})

		// const { miniProgram } = wx.getAccountInfoSync();
		// if (miniProgram.version) {
		// 	if (this.data.userInfo.remain_coupons < 1) {
		// 		wx.showToast({
		// 			title: "探店券已用完",
		// 			icon: "error",
		// 			duration: 3000,
		// 		});
		// 		return;
		// 	}

		// 	if (this.data.userInfo.banned) {
		// 		wx.showToast({
		// 			title: "账号已被封禁",
		// 			icon: "error",
		// 			duration: 3000,
		// 		});
		// 		return;
		// 	}

		// 	// console.debug("this.data.userInfo.phone", this.data.userInfo)
		// 	if (!this.data.userInfo.phone) {
		// 		// wx.showToast({
		// 		//   title: "请先绑定手机号",
		// 		//   icon: 'error',
		// 		//   duration: 3000
		// 		// })
		// 		this.setData({
		// 			showBindPhoneDialog: true,
		// 		});
		// 		return;
		// 	}

		// 	// 检测是否有名片、粉丝数、等级是否符合要求
		// 	// 检测实时库存
		// 	const promotion = this.data.promotion;
		// 	if (promotion.realtime_quantity < 1) {
		// 		wx.showToast({
		// 			title: "今日份额已售完",
		// 			icon: "error",
		// 			duration: 3000,
		// 		});
		// 		return;
		// 	}

		// 	console.debug("cards", this.data.cards);
		// 	console.debug("promotion", promotion);
		// 	console.debug(
		// 		"活动要求: ",
		// 		formatPlatform(promotion.platform),
		// 		formatRequirements(
		// 			promotion.requires,
		// 			promotion.level,
		// 			promotion.followers_count,
		// 		),
		// 	);
		// 	const verifiedPlatformCards = this.data.cards
		// 		.filter((x) => x.verified === 1)
		// 		.filter((x) => x.platform === promotion.platform)
		// 		.sort((a, b) => b.level - a.level)
		// 		.sort((a, b) => b.followers_count - a.followers_count);
		// 	console.debug("verifiedPlatformCards:", verifiedPlatformCards);

		// 	if (verifiedPlatformCards.length < 1) {
		// 		// wx.showToast({
		// 		//   title: `请先认证名片`,
		// 		//   icon: 'error',
		// 		//   duration: 3000
		// 		// })
		// 		this.setData({
		// 			showBindCardDialog: true,
		// 		});
		// 		return;
		// 	}

		// 	if (promotion.requires === "1") {
		// 		console.debug("要求等级");
		// 		const qualifiedPlatformCards = verifiedPlatformCards
		// 			.sort((a, b) => b.level - a.level)
		// 			.filter((x) => x.level >= promotion.level);
		// 		if (qualifiedPlatformCards.length < 1) {
		// 			wx.showToast({
		// 				title: `等级不达标`,
		// 				icon: "error",
		// 				duration: 3000,
		// 			});
		// 			return;
		// 		}
		// 	}

		// 	if (promotion.requires === "2") {
		// 		console.debug("要求粉丝数");
		// 		const qualifiedPlatformCards = verifiedPlatformCards
		// 			.sort((a, b) => b.followers_count - a.followers_count)
		// 			.filter((x) => x.followers_count >= promotion.followers_count);
		// 		if (qualifiedPlatformCards.length < 1) {
		// 			wx.showToast({
		// 				title: `粉丝数不达标`,
		// 				icon: "error",
		// 				duration: 3000,
		// 			});
		// 			return;
		// 		}
		// 	}

		// 	if (promotion.requires === "3") {
		// 		console.debug("要求等级粉丝数");
		// 		const qualifiedPlatformCards = verifiedPlatformCards
		// 			.filter((x) => x.followers_count >= promotion.followers_count)
		// 			.filter((x) => x.level >= promotion.level);

		// 		if (qualifiedPlatformCards.length < 1) {
		// 			wx.showToast({
		// 				title: `要求不达标`,
		// 				icon: "error",
		// 				duration: 3000,
		// 			});
		// 			return;
		// 		}
		// 	}

		// 	// 检测是否15天内有下过单
		// 	// const hasPlacedOrderInHalfMonth = await this.checkIfBuyedRecently(this.data.promotion.shop_id._id)
		// 	// if (hasPlacedOrderInHalfMonth) {
		// 	//   this.setData({
		// 	//     submitting: false
		// 	//   })
		// 	//   Toast({
		// 	//     context: this,
		// 	//     selector: '#t-toast',
		// 	//     message: '15天内同一个商家只能下一单',
		// 	//     duration: 3000
		// 	//   });
		// 	//   return
		// 	// }
		// 	// await this.checkIfBuyed(this.data.promotion.shop_id._id);
		// }

		// this.setData({
		// 	isOrderPopupShow: true,
		// });
	},

	toNav(e) {
		const { url } = e.detail;
		console.log(url);
		wx.switchTab({
			url: url,
			fail() {
				wx.navigateTo({
					url: url,
				});
			},
		});
	},

	closePromotionPopup() {
		this.setData({
			isShowPromotionPop: false,
		});
	},

	promotionChange(e) {
		const { index } = e.detail;
		wx.navigateTo({
			url: `/pages/promotion-detail/index?promotion_id=${index}`,
		});
	},

	showPromotionPopup() {
		this.setData({
			isShowPromotionPop: true,
		});
	},

	onShareAppMessage() {
		// 自定义的返回信息
		// const { selectedAttrStr } = this.data;
		// let shareSubTitle = '';
		// if (selectedAttrStr.indexOf('件') > -1) {
		//   const count = selectedAttrStr.indexOf('件');
		//   shareSubTitle = selectedAttrStr.slice(count + 1, selectedAttrStr.length);
		// }
		// const images = this.data.promotion.product_id.images
		// const shareSubTitle = `折后价${this.data.promotion.current_price}元`
		let referrer_id = null;
		const userInfo = wx.getStorageSync("dgx-auth");
		if (userInfo) {
			referrer_id = userInfo.user_id;
		}

		const district = this.data.promotion.shop_id?.district;
		const shareSubTitle = `${district} | 原价${this.data.promotion.original_price}元，最低${this.data.promotion.current_price}元抢`;

		const customInfo = {
			// imageUrl: images[0],
			title: shareSubTitle + this.data.promotion.name,
			path: `/pages/promotion/index?id=${this.data.id}&referrer_id=${referrer_id}`,
		};
		return customInfo;
	},

	async onLoad(options) {
		console.debug("options: ", options);

		const { miniProgram } = wx.getAccountInfoSync();
		this.setData({
			miniProgram: miniProgram,
		});

		this.handleIsUpdatingStockButton();

		this.bindMbxStores();

		// 可以通过options获取ID，然后请求详情
		if (options.id) {
			this.setData({
				id: options.id,
			});
		}

		if (options.backRefresh) {
			this.setData({
				backRefresh: true,
			});
		}
	},

	bindMbxStores() {
		this.promotionStoreBindings = createStoreBindings(this, {
			store: productStore,
			fields: ["promotion"], // 需要绑定的字段
			actions: ["promotionDetail"], // 需要绑定的 actions
		});

		this.cardStoreBindings = createStoreBindings(this, {
			store: cardStore,
			fields: ["cards"], // 需要绑定的字段
			actions: ["fetchCards"], // 需要绑定的 actions
		});

		this.userStoreBindings = createStoreBindings(this, {
			store: userStore,
			fields: ["isShopFavorited"], // 需要绑定的字段
			actions: ["hasFavorited", "toggleFavorite"], // 需要绑定的 actions
		});

		this.orderStoreBindings = createStoreBindings(this, {
			store: orderStore,
			fields: ["order"],
			actions: [
				"placeOrder",
				"checkIfBuyedRecently",
				"cancelPayment",
				"reduceStock",
				"addStock",
			],
		});

		this.authStoreBindings = createStoreBindings(this, {
			store: authStore,
			fields: ["userInfo"],
			actions: ["refreshUserInfo", "getReferrerId", "setReferrerId"],
		});
	},

	onUnload() {
		this.promotionStoreBindings.destroyStoreBindings();
		this.cardStoreBindings.destroyStoreBindings();
		this.userStoreBindings.destroyStoreBindings();
		this.orderStoreBindings.destroyStoreBindings();
		this.authStoreBindings.destroyStoreBindings();
	},

	async onShow() {
		this.init();
	},

	async init() {
		this.handleIsUpdatingStockButton();
		await this.fetchDetail(this.data.id);
		await this.refreshUserInfo();
		await this.fetchCards();
		// this.initCountdown();
		this.calcProfit();

		const usageText = await this.formatUsageText(this.data.promotion);
		this.setData({
			usageText: usageText,
		});
	},

	async fetchDetail(id) {
		wx.showLoading();
		await this.promotionDetail(id);
		setTimeout(async () => {
			const promotion = this.data.promotion;
			if (promotion && promotion.available === false) {
				console.debug("活动已结束");
				this.setData({
					soldout: true,
					isStock: false,
				});
			}
			if (
				promotion &&
				promotion.shop_id &&
				promotion.shop_id.available === false
			) {
				console.debug("活动已结束");
				this.setData({
					soldout: true,
					isStock: false,
				});
			}

			if (promotion && promotion.promotion_type === 'ykj') {
				wx.setNavigationBarTitle({
					title: '特价团活动详情'
				})
			}else {
				wx.setNavigationBarTitle({
					title: '探店活动详情'
				})
			}
	
			// 获取店铺是否收藏
			this.isFavorited(this.data.promotion.shop_id._id);
			await this.checkIfBuyed(this.data.promotion.shop_id._id);

			const location = wx.getStorageSync("dgx-location");
			const distance = this.haversineDistance(
				location.latitude,
				location.longitude,
				this.data.promotion.shop_id.latitude,
				this.data.promotion.shop_id.longitude,
			);
			console.debug(distance);
			if (distance > 1) {
				this.setData({
					distance: `${distance.toFixed(2)}km`,
				});
			} else {
				this.setData({
					distance: `${(distance * 100).toFixed(0)}m`,
				});
			}
		}, 500);
		// const res = await productStore.productDetail(id)
		// console.debug("product detail", res);
		// this.setData({
		//   productInfo: res.data
		// })
		// if(res.data.images) {
		//   this.setData({
		//     "productInfo.logo": res.data.images[0]
		//   })
		// }else {
		//   this.setData({
		//     "productInfo.logo": "https://placehold.co/100x100"
		//   })
		// }
		wx.hideLoading();
	},

	async onReady() {
	},

	initCountdown() {
		// 获取当前时间
		const now = dayjs();
		// 获取今日晚上 23:59:59 的时间
		const endOfDay = dayjs().endOf("day");
		// 计算两者之间的毫秒数
		const millisecondsUntilEndOfDay = endOfDay.diff(now);
		// return millisecondsUntilEndOfDay;
		this.setData({
			millisecondsUntilEndOfDay: millisecondsUntilEndOfDay,
		});
	},

	handleOrderPopupHide() {
		this.setData({
			isOrderPopupShow: false,
		});
	},

	async checkIfBuyed(shop_id) {
		console.debug("checkIfBuyed", shop_id);
		// 探店订单需要检测是否15天内有下过单
		if(this.data.promotion.promotion_type !== "ykj") {
			const hasPlacedOrderInHalfMonth = await this.checkIfBuyedRecently(
				shop_id || this.data.promotion.shop_id._id,
			);
			if (hasPlacedOrderInHalfMonth) {
				this.setData({
					submitting: false,
					buyBtnDisabled: true,
					buyedInHalfMonth: true,
				});
				Toast({
					context: this,
					selector: "#t-toast",
					message: "15天内同一个商家只能下一单",
					duration: 3000,
				});
				return;
			}
		}
	},

	async calcProfit() {
		// console.debug("calcProfit", this.data.promotion.commission, this.data.userInfo.user_level);
		const profit = await estimateProfit(
			this.data.promotion.commission,
			this.data.userInfo.user_level,
		);
		// console.debug("profit", profit);
		this.setData({
			shareProfit: profit,
		});
	},

	closeBindCardDialog() {
		this.setData({
			showBindCardDialog: false,
		});
	},

	closeBindPhoneDialog() {
		this.setData({
			showBindPhoneDialog: false,
		});
	},

	goToBindCard() {
		this.setData({
			showBindCardDialog: false,
		});
		wx.navigateTo({
			url: "/pages/cards/index",
		});
	},

	goToBindPhone() {
		this.setData({
			showBindPhoneDialog: false,
		});
		wx.navigateTo({
			url: "/pages/usercenter/bind-phone/index",
		});
	},

	async onPullDownRefresh() {
		this.setData({ enable: true });
		// await this.fetchDetail(this.data.id);
		await this.init();
		setTimeout(() => {
			this.setData({ enable: false });
		}, 1000);
	},

	async submitOrder() {
		// await this.checkIfBuyed(this.data.promotion.shop_id._id);
		if (this.data.buyedInHalfMonth) {
			Toast({
				context: this,
				selector: "#t-toast",
				message: "15天内同一个商家只能下一单",
				duration: 3000,
			});
			return;
		}

		// 定义时间区间
		const startTimeStr = `${dayjs().format("YYYY-MM-DD")} 00:00:00`;
		const endTimeStr = `${dayjs().format("YYYY-MM-DD")} 00:10:00`;
		const startTime = dayjs(startTimeStr, "YYYY-MM-DD HH:mm:ss");
		const endTime = dayjs(endTimeStr, "YYYY-MM-DD HH:mm:ss");

		// 要检查的时间
		const checkTime = dayjs();
		// 判断是否在区间内（包含边界）
		const isBetween = checkTime.isBetween(startTime, endTime, "second", "[]");
		console.log(isBetween); // true
		if (isBetween) {
			return;
		}

		if (this.data.submitting) {
			return;
		}

		this.setData({
			submitting: true,
		});

		await this.promotionDetail(this.data.id);
		if (this.data.promotion && this.data.promotion.available === false) {
			console.debug("活动已结束");
			this.setData({
				soldout: true,
				isStock: false,
			});
		}

		const promotion = this.data.promotion;
		if (promotion.realtime_quantity < 1) {
			wx.showToast({
				title: "今日份额已售完",
				icon: "error",
				duration: 3000,
			});
			return;
		}

		console.debug("cards", this.data.cards);
		console.debug("promotion", promotion);
		console.debug(
			"活动要求: ",
			formatPlatform(promotion.platform),
			formatRequirements(
				promotion.requires,
				promotion.level,
				promotion.followers_count,
			),
		);
		const verifiedPlatformCards = this.data.cards
			.filter((x) => x.verified === 1)
			.filter((x) => x.platform === promotion.platform)
			.sort((a, b) => b.level - a.level)
			.sort((a, b) => b.followers_count - a.followers_count);
		console.debug("verifiedPlatformCards:", verifiedPlatformCards);

		let qualifiedPlatformCardForOrder = null;
		if (promotion.requires === "1") {
			console.debug("要求等级");
			const qualifiedPlatformCards = verifiedPlatformCards.filter(
				(x) => x.level >= promotion.level,
			);
			qualifiedPlatformCardForOrder = qualifiedPlatformCards[0];
		}

		if (promotion.requires === "2") {
			console.debug("要求粉丝数");
			const qualifiedPlatformCards = verifiedPlatformCards.filter(
				(x) => x.followers_count >= promotion.followers_count,
			);
			qualifiedPlatformCardForOrder = qualifiedPlatformCards[0];
		}

		if (promotion.requires === "3") {
			console.debug("要求等级粉丝数");
			const qualifiedPlatformCards = verifiedPlatformCards
				.filter((x) => x.followers_count >= promotion.followers_count)
				.filter((x) => x.level >= promotion.level);
			qualifiedPlatformCardForOrder = qualifiedPlatformCards[0];
		}
		console.debug("符合要求的名片: ", qualifiedPlatformCardForOrder);

		wx.showLoading({
			title: "正在生成订单...",
			mask: true,
		});

		const referrer_id = this.getReferrerId() || "";
		console.debug("订单邀请人:", referrer_id);

		const auth = wx.getStorageSync("dgx-auth");
		const data = {
			referrer_id: referrer_id,
			user_id: {
				_id: auth.user_id,
			},
			promotion_id: [
				{
					_id: this.data.promotion._id,
				},
			],
			shop_id: {
				_id: this.data.promotion.shop_id._id,
			},
			card_id: {
				_id: qualifiedPlatformCardForOrder?._id,
			},
			p_id: this.data.promotion._id,
			commission: this.data.promotion.commission,
			total_amount: this.data.promotion.sale_price,
			paid_amount: this.data.promotion.sale_price,
			refund_amount: this.data.promotion.discount_price,
			order_type: this.data.promotion.promotion_type,
		};
		log.debug("data to place order", data);

		// wx.hideLoading();
		// return

		const res = await this.placeOrder(data);
		log.debug("placeOrder", res);

		const orderId = res.id;
		const orderNo = res.orderNo;
		const that = this;
		// 减库存
		if (that.data.promotion.realtime_quantity > 0) {
			// await this.reduceStock(this.data.promotion._id, this.data.promotion.realtime_quantity)
			await wx.cloud.callFunction({
				name: "reduce_stock",
				data: {
					promotion_id: that.data.promotion._id,
					// "realtime_quantity": this.data.promotion.realtime_quantity
				},
				success: (res) => {
					log.debug(
						"wx.cloud.callFunction reduce_stock for",
						that.data.promotion._id,
						"response:",
						res,
					);
				},
			});
		}
		// this.setData({
		//   submitting: false
		// })
		// return

		log.debug("拉起支付");
		// 小程序代码
		wx.cloud.callFunction({
			name: "place_order",
			data: {
				body: that.data.promotion.name,
				outTradeNo: orderNo,
				totalFee: Math.round(data.paid_amount * 100), // 默认单位是分
			},
			success: (res) => {
				wx.hideLoading();
				Toast({
					context: that,
					selector: "#t-toast",
					message: "订单提交成功",
					icon: "check-circle",
					duration: 1000,
				});
				that.handleOrderPopupHide();

				console.debug("wx.cloud.callFunction unified_order:", res);
				const payment = res.result.payment;
				try {
					wx.requestPayment({
						...payment,
						success(res) {
							log.debug("wx.requestPayment success", res);
							if (res.errMsg === "requestPayment:ok") {
								if (that.data.promotion.promotion_type === "tandian1") {
									wx.navigateTo({
										url: `/pages/order/detail/index?id=${orderId}`,
									});
									return;
								}
								if (that.data.promotion.promotion_type === "tandian2") {
									wx.navigateTo({
										url: `/pages/order/reserve/index?id=${orderId}`,
									});
									return;
								}
							}
						},
						async fail(err) {
							log.debug("wx.requestPayment fail", res);
							wx.hideLoading();
							// console.debug(
							// 	"取消支付，库存+1",
							// 	that.data.promotion._id,
							// 	that.data.promotion.realtime_quantity,
							// );
							// // await orderStore.addStock(this.data.promotion._id, this.data.promotion.realtime_quantity);
							// await wx.cloud.callFunction({
							// 	name: "add_stock",
							// 	data: {
							// 		promotion_id: that.data.promotion._id,
							// 		// "realtime_quantity": this.data.promotion.realtime_quantity
							// 	},
							// 	success: (res) => {
							// 		console.debug(
							// 			"wx.cloud.callFunction add_stock for",
							// 			that.data.promotion._id,
							// 		);
							// 	},
							// });
							if (err.errMsg === "requestPayment:fail cancel") {
								wx.showToast({
									title: "用户取消支付",
									icon: "error",
									duration: 2000,
								});
								// log.debug("orderStore.cancelPayment: ", err);
								// await that.cancelPayment(orderNo);
								// return;
							}
							await wx.cloud.callFunction({
								name: "query_order",
								data: {
									order_no: orderNo,
								},
								success: (res) => {
									console.debug(
										"wx.cloud.callFunction query_order for",
										orderNo,
										"when cancel payment, response:",
										res,
									);
								},
							});
							wx.redirectTo({
								url: "/pages/order/list/index"
							});
							return
						},
					});
				} catch (error) {
					log.error(error);
				}
			},
			fail: console.error,
		});

		this.setData({
			submitting: false,
		});
		return;
	},

	navToStore() {
		const shopId = this.data.promotion.shop_id._id;
		console.debug("shopId", shopId);
		wx.navigateTo({
			url: `/pages/store/index?id=${shopId}`,
		});
	},

	handleNavigation() {
		const { address, longitude, latitude, name } = this.data.promotion.shop_id;
		wx.openLocation({
			latitude: latitude,
			longitude: longitude,
			name: name,
			address: address,
			scale: 18,
		});
	},

	handleCall() {
		const shopPhone = this.data.promotion.shop_id.phone;
		if (shopPhone) {
			wx.makePhoneCall({
				phoneNumber: shopPhone,
			});
		} else {
			wx.showToast({
				title: "暂无商家电话",
				icon: "none",
			});
		}
	},

	haversineDistance: function (lat1, lon1, lat2, lon2) {
		// 将经纬度从度数转换为弧度
		const toRadians = (degrees) => degrees * (Math.PI / 180);

		const R = 6371; // 地球半径，单位：公里
		const dLat = toRadians(lat2 - lat1);
		const dLon = toRadians(lon2 - lon1);

		const a =
			Math.sin(dLat / 2) * Math.sin(dLat / 2) +
			Math.cos(toRadians(lat1)) *
				Math.cos(toRadians(lat2)) *
				Math.sin(dLon / 2) *
				Math.sin(dLon / 2);

		const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

		const distance = R * c; // 最终距离，单位：公里
		return distance;
	},

	/**
	 * 收藏/取消收藏店铺
	 */
	async toggleFavoriteAction() {
		console.debug("click toggle favorite button");
		const shop = this.data.shop || this.data.promotion.shop_id;
		// console.debug(shop);
		console.debug("isShopFavorited取反后", !this.data.isShopFavorited);
		// shop.isFavorite = !shop.isFavorite;
		// this.setData({ shop });

		try {
			const result = await this.toggleFavorite({
				shop_id: shop._id,
				favorited: !this.data.isShopFavorited,
			});
			console.debug("toggleFavorite result", result);
			if (!this.data.isShopFavorited) {
				wx.showToast({
					title: "收藏成功",
				});
			} else {
				wx.showToast({
					title: "取消收藏",
				});
			}
			// if (result && result.id) {
			// }
			// if (result && result.count === 1) {
			// }
		} catch (err) {
			console.error(err);
		} finally {
			await this.isFavorited(shop._id);
		}
	},

	// 判断店铺是否收藏
	async isFavorited(id) {
		await this.hasFavorited({
			shop_id: id,
		});
	},

	// 格式化使用须知
	async formatUsageText(promotion) {
		const promotion_type = promotion.promotion_type;
		console.debug("xxxxx", promotion)
		switch (promotion_type) {
			case "tandian1":
				return "下单后请在次日内完成核销";
			case "tandian2":
				return "下单后预约探店时间，只要不下线就不过期";
			case "ykj":
				return "";
				// return "下单后30天有效，过期作废";
				// return "接待时间: " + dayjs(promotion.verify_start_date, "YYYY-MM-DD HH:mm:ss").format("YYYY.MM.DD") + "至" + dayjs(promotion.verify_end_date, "YYYY-MM-DD HH:mm:ss").format("YYYY.MM.DD");
			default:
				return "未知";
		}
	}
});
