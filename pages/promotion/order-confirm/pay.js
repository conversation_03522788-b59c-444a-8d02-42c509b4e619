import Dialog from "tdesign-miniprogram/dialog/index";
import Toast from "tdesign-miniprogram/toast/index";

// 模拟提交支付API
const mockCommitPay = (_params) => {
	return new Promise((resolve, _reject) => {
		setTimeout(() => {
			// 模拟成功响应
			resolve({
				code: "Success",
				data: {
					channel: "wechat",
					payInfo: {
						timeStamp: Date.now().toString(),
						nonceStr: Math.random().toString(36).substr(2, 15),
						package: "prepay_id=mock_prepay_id",
						signType: "RSA",
						paySign: "mock_pay_sign",
					},
					tradeNo: `ORDER_${Date.now()}`,
					interactId: `INTERACT_${Date.now()}`,
					transactionId: `TRANS_${Date.now()}`,
				},
			});
		}, 1000);
	});
};

// 提交支付
export const commitPay = (params) => {
	console.log("提交支付参数:", params);
	return mockCommitPay(params);
};

// 微信支付
export const wechatPayOrder = (payOrderInfo) => {
	console.log("微信支付信息:", payOrderInfo);

	// 模拟微信支付调用
	wx.requestPayment({
		timeStamp: payOrderInfo.payInfo.timeStamp,
		nonceStr: payOrderInfo.payInfo.nonceStr,
		package: payOrderInfo.payInfo.package,
		signType: payOrderInfo.payInfo.signType,
		paySign: payOrderInfo.payInfo.paySign,
		success: (res) => {
			console.log("支付成功:", res);
			Toast({
				context: this,
				selector: "#t-toast",
				message: "支付成功",
				duration: 2000,
				icon: "check-circle",
			});

			setTimeout(() => {
				wx.redirectTo({ url: "/pages/order/list/index" });
			}, 2000);
		},
		fail: (err) => {
			console.log("支付失败:", err);
			payFail(payOrderInfo, err.errMsg);
		},
	});
};

// 支付失败处理
export const payFail = (_payOrderInfo, resultMsg) => {
	if (resultMsg === "requestPayment:fail cancel") {
		Dialog.confirm({
			title: "是否放弃付款",
			content: "商品可能很快就会被抢空哦，是否放弃付款？",
			confirmBtn: "放弃",
			cancelBtn: "继续付款",
		})
			.then(() => {
				wx.redirectTo({ url: "/pages/order/list/index" });
			})
			.catch(() => {
				// 用户选择继续付款，可以重新调用支付
				console.log("用户选择继续付款");
			});
	} else {
		Toast({
			context: this,
			selector: "#t-toast",
			message: `支付失败：${resultMsg}`,
			duration: 2000,
			icon: "close-circle",
		});
		setTimeout(() => {
			wx.redirectTo({ url: "/pages/order/list/index" });
		}, 2000);
	}
};
