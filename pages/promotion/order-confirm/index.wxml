<wxs module="filter" src="../../../utils/util.wxs"></wxs>
<wxs module="order" src="./order.wxs" />
<wxs module="handleInvoice" src="./handleInvoice.wxs" />

<block wx:if="{{!loading}}">
  <view class="order-confirm">
    <!-- 商品列表 -->
    <view class="order-wrapper">
      <view class="store-wrapper">
        <t-icon prefix="wr" size="40rpx" color="#333333" name="store" class="store-logo" />
        {{promotion.shop_id.name}}
      </view>
      <view class="goods-wrapper">
        <t-image src="{{promotion.product_id.images[0]}}" t-class="goods-image" mode="aspectFill" />
        <view class="goods-content">
          <view class="goods-title">{{promotion.name}}</view>
          <view class="goods-specs">{{goods.specs}}</view>
          <view class="goods-tags">
            <t-tag size="small" variant="outline" theme="default">
              无需笔记
            </t-tag>
            <t-tag size="small" variant="outline" theme="default">
              特价活动不支持退款
            </t-tag>
          </view>
        </view>
        <view class="goods-right">
          <price wr-class="goods-price" price="{{promotion.current_price}}" fill="{{true}}" decimalSmaller />
          <view class="goods-num">x1</view>
        </view>
      </view>
    </view>

    <!-- 支付详情 -->
    <view class="pay-detail">
      <view class="pay-item">
        <text>原价</text>
        <price fill decimalSmaller type="del" wr-class="pay-item__right font-bold" price="{{promotion.original_price || '0'}}" />
      </view>
      <view class="pay-item">
        <text>团购价</text>
        <price fill decimalSmaller type="del" wr-class="pay-item__right font-bold" price="{{promotion.sale_price || '0'}}" />
      </view>
      <view class="pay-item">
        <text>优探价</text>
        <price fill decimalSmaller wr-class="pay-item__right font-bold" price="{{promotion.current_price || '0'}}" />
      </view>
      <!--
      <view class="pay-item">
        <text>有效期</text>
        <view class="pay-item__right font-bold primary">
          <block wx:if="{{settleDetailData.totalDeliveryFee && settleDetailData.totalDeliveryFee != 0}}">
            +
            <price fill decimalSmaller price="{{settleDetailData.totalDeliveryFee}}" />
          </block>
          <text wx:else>有效期30天，过期未核销订单作废</text>
        </view>
      </view>
      -->
      <view class="pay-item">
        <text>商家接待时间</text>
        <view class="pay-item__right">
          <text>{{ filter.formatDate(promotion.verify_start_date) }}至{{ filter.formatDate(promotion.verify_end_date) }}</text>
        </view>
      </view>
      <view class="pay-item">
        <text>优惠券</text>
        <view class="pay-item__right">
          <text>暂无</text>
        </view>
      </view>
      <!--
      <view class="pay-item">
        <text>其他要求</text>
        <view class="pay-item__right">
          <text>无需笔记，未过期随时可退</text>
        </view>
      </view>
      <view class="pay-item">
        <text>活动返利</text>
        <view class="pay-item__right">
          <text>不返利</text>
        </view>
      </view>
      -->
      <!-- <view class="pay-item">
        <text>优惠券</text>
        <view class="pay-item__right" data-storeid="{{settleDetailData.storeGoodsList[0].storeId}}"
          catchtap="onOpenCoupons">
          <block wx:if="{{submitCouponList.length}}">
            <block wx:if="{{settleDetailData.totalCouponAmount && settleDetailData.totalCouponAmount !== '0'}}">
              -
              <price fill decimalSmaller price="{{settleDetailData.totalCouponAmount}}" />
            </block>
            <block wx:else>选择优惠券</block>
          </block>
          <text wx:else>无可用</text>
          <t-icon name="chevron-right" size="32rpx" color="#BBBBBB" />
        </view>
      </view> -->
    </view>

    <!-- 金额汇总 -->
    <view class="amount-wrapper">
      <view class="pay-amount">
        <text class="order-num">共1件</text>
        <text>小计</text>
        <price class="total-price" price="{{promotion.current_price}}" fill="{{false}}" decimalSmaller />
      </view>
    </view>

    <!-- 底部支付栏 -->
    <view class="wx-pay-cover">
      <view class="wx-pay">
        <price decimalSmaller fill class="price" price="{{promotion.current_price || '0'}}" />
        <view class="submit-btn" bindtap="submitOrder">
          {{ payLock ? '正在提交' : '提交订单'}}
        </view>
      </view>
    </view>

    <!-- 优惠券选择 -->
    <select-coupons bind:sure="onCoupons" storeId="{{currentStoreId}}" orderSureCouponList="{{couponList}}"
      promotionGoodsList="{{promotionGoodsList}}" couponsShow="{{couponsShow}}" />
  </view>
</block>

<!-- 加载状态 -->
<view wx:else class="loading-container">
  <t-loading size="40rpx" text="加载中..." />
</view>

<!-- 组件 -->
<t-toast id="t-toast" />
<t-dialog id="t-dialog" />