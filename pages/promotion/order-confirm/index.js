// pages/promotion/order-confirm/index.js
import Toast from "tdesign-miniprogram/toast/index";
import { commitPay, wechatPayOrder } from "./pay";
import { createStoreBindings } from "mobx-miniprogram-bindings";
import {
	productStore,
	cardStore,
	authStore,
	orderStore,
	userStore,
} from "../../../stores/index";
import { log } from "../../../utils/log";

import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
dayjs.extend(isBetween);

Page({
	/**
	 * 页面的初始数据
	 */
	data: {
		placeholder: "备注信息",
		loading: false,
		couponList: [],
		notesPosition: "center",
	},
	payLock: false,
	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		console.debug("推广活动订单确认页面参数:", options);
		this.setData({ loading: true });
		this.handleOptionsParams(options);

		console.debug("options: ", options);

		const { miniProgram } = wx.getAccountInfoSync();
		this.setData({
			miniProgram: miniProgram,
		});

		this.handleIsUpdatingStockButton();

		this.promotionStoreBindings = createStoreBindings(this, {
			store: productStore,
			fields: ["promotion"], // 需要绑定的字段
			actions: ["promotionDetail"], // 需要绑定的 actions
		});

		this.userStoreBindings = createStoreBindings(this, {
			store: userStore,
			fields: ["isShopFavorited"], // 需要绑定的字段
			actions: ["hasFavorited", "toggleFavorite"], // 需要绑定的 actions
		});

		this.orderStoreBindings = createStoreBindings(this, {
			store: orderStore,
			fields: ["order"],
			actions: [
				"placeOrder",
				"checkIfBuyedRecently",
				"cancelPayment",
				"reduceStock",
				"addStock",
			],
		});

		this.authStoreBindings = createStoreBindings(this, {
			store: authStore,
			fields: ["userInfo"],
			actions: ["refreshUserInfo", "getReferrerId", "setReferrerId"],
		});

		// 可以通过options获取ID，然后请求详情
		if (options.id) {
			this.setData({
				id: options.id,
			});

			this.loadData(options.id)
		}
	},

	async loadData(id) {
		await this.promotionDetail(id);
		this.setData({
			loading: false
		})
		await this.refreshUserInfo();

		const usageText = await this.formatUsageText(this.data.promotion);
		this.setData({
			usageText: usageText,
		});
		console.debug("usageText", usageText);
	},

	// 格式化使用须知
	async formatUsageText(promotion) {
		const promotion_type = promotion.promotion_type;
		console.debug("xxxxx", promotion)
		switch (promotion_type) {
			case "tandian1":
				return "下单后请在次日内完成核销";
			case "tandian2":
				return "下单后预约探店时间，只要不下线就不过期";
			case "ykj":
				return "";
				// return "下单后30天有效，过期作废";
				// return "接待时间: " + dayjs(promotion.verify_start_date, "YYYY-MM-DD HH:mm:ss").format("YYYY.MM.DD") + "至" + dayjs(promotion.verify_end_date, "YYYY-MM-DD HH:mm:ss").format("YYYY.MM.DD");
			default:
				return "未知";
		}
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {
		// 页面初次渲染完成
	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {
		// 页面显示
	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {
		// 页面隐藏
	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {
		// 页面卸载
	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {
		// 下拉刷新
		this.init();
	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {
		// 上拉触底
	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {
		// 分享
		return {
			title: "推广活动订单确认",
			path: "/pages/promotion/order-confirm/index",
		};
	},

	handleIsUpdatingStockButton() {
		// 定义时间区间
		const startTimeStr = `${dayjs().format("YYYY-MM-DD")} 00:00:00`;
		const endTimeStr = `${dayjs().format("YYYY-MM-DD")} 00:10:00`;
		const startTime = dayjs(startTimeStr, "YYYY-MM-DD HH:mm:ss");
		const endTime = dayjs(endTimeStr, "YYYY-MM-DD HH:mm:ss");

		// 要检查的时间
		const checkTime = dayjs();
		// 判断是否在区间内（包含边界）
		const between = checkTime.isBetween(startTime, endTime, "second", "[]");

		if (between) {
			this.setData({
				isUpdatingStock: true,
			});
		} else {
			this.setData({
				isUpdatingStock: false,
			});
		}
	},

	// 处理页面参数
	handleOptionsParams(options) {
		const { goodsRequestList, promotionId, activityId } = options;

		if (goodsRequestList) {
			this.goodsRequestList = JSON.parse(goodsRequestList);
		} else if (promotionId && activityId) {
			// 从推广活动页面进入，需要根据活动ID获取商品信息
			this.getPromotionGoods(promotionId, activityId);
			return;
		}

		this.init();
	},

	// 获取推广活动商品信息
	getPromotionGoods(promotionId, activityId) {
		// TODO: 调用推广活动相关的API获取商品信息
		// 这里需要根据实际的API接口进行调整
		console.log("获取推广活动商品信息:", { promotionId, activityId });

		// 模拟数据，实际应该调用API
		const mockGoodsRequestList = [
			{
				quantity: 1,
				storeId: "store001",
				uid: "user001",
				saasId: "saas001",
				spuId: "spu001",
				goodsName: "推广活动商品",
				skuId: "sku001",
				storeName: "推广店铺",
				roomId: "room001",
			},
		];

		this.goodsRequestList = mockGoodsRequestList;
		this.init();
	},

	// 初始化数据
	init() {
		this.setData({ loading: true });
		// this.loadMockData();
	},

	// 加载模拟数据
	loadMockData() {
		// 模拟结算详情数据
		const mockSettleDetailData = {
			storeGoodsList: [
				{
					storeId: "store001",
					storeName: "推广店铺",
					storeTotalPayAmount: "99.00",
					skuDetailVos: [
						{
							image: "https://example.com/image.jpg",
							goodsName: "推广活动商品",
							skuSpecLst: [{ specValue: "规格1" }],
							tagPrice: "99.00",
							settlePrice: "99.00",
							tagText: "限时特价",
							quantity: 1,
							skuId: "sku001",
							spuId: "spu001",
							storeId: "store001",
						},
					],
					couponList: [],
				},
			],
			totalSalePrice: "99.00",
			totalDeliveryFee: "0",
			totalPromotionAmount: "10.00",
			totalCouponAmount: "0",
			totalPayAmount: "89.00",
			totalGoodsCount: 1,
			settleType: 1,
			invoiceSupport: false,
		};

		this.initData(mockSettleDetailData);
	},

	// 初始化数据
	initData(resData) {
		const data = this.handleResToGoodsCard(resData);
		this.setData({
			settleDetailData: data,
			loading: false,
		});
	},

	// 转换响应数据为商品卡片展示格式
	handleResToGoodsCard(data) {
		const orderCardList = [];
		const storeInfoList = [];
		const submitCouponList = [];

		data.storeGoodsList.forEach((ele) => {
			const orderCard = {
				id: ele.storeId,
				storeName: ele.storeName,
				status: 0,
				statusDesc: "",
				amount: ele.storeTotalPayAmount,
				goodsList: [],
			};

			ele.skuDetailVos.forEach((item, index) => {
				orderCard.goodsList.push({
					id: index,
					thumb: item.image,
					title: item.goodsName,
					specs: item.skuSpecLst.map((s) => s.specValue),
					price: item.tagPrice || item.settlePrice || "0",
					settlePrice: item.settlePrice,
					titlePrefixTags: item.tagText ? [{ text: item.tagText }] : [],
					num: item.quantity,
					skuId: item.skuId,
					spuId: item.spuId,
					storeId: item.storeId,
				});
			});

			storeInfoList.push({
				storeId: ele.storeId,
				storeName: ele.storeName,
				remark: "",
			});

			submitCouponList.push({
				storeId: ele.storeId,
				couponList: ele.couponList || [],
			});


			orderCardList.push(orderCard);
		});

		this.setData({ orderCardList, storeInfoList, submitCouponList });
		return data;
	},

	// 跳转到地址页面
	onGotoAddress() {
		wx.navigateTo({
			url: "/pages/usercenter/address/list/index?isOrderSure=true",
		});
	},

	// 打开优惠券选择
	onOpenCoupons(e) {
		const { storeid } = e.currentTarget.dataset;
		this.setData({
			currentStoreId: storeid,
			couponsShow: true,
		});
	},

	// 提交订单
	async submitOrder() {
		if (this.payLock) {
			return;
		}
		this.payLock = true;

		// 
		wx.showLoading({
			title: "正在生成订单...",
			mask: true,
		});

		const referrer_id = this.getReferrerId() || "";
		console.debug("订单邀请人:", referrer_id);

		console.debug(this.data.promotion)


		const auth = wx.getStorageSync("dgx-auth");
		const data = {
			referrer_id: referrer_id,
			user_id: {
				_id: auth.user_id,
			},
			promotion_id: [
				{
					_id: this.data.promotion._id,
				},
			],
			shop_id: {
				_id: this.data.promotion.shop_id._id,
			},
			// card_id: {
			// 	_id: qualifiedPlatformCardForOrder?._id,
			// },
			p_id: this.data.promotion._id,
			order_type: this.data.promotion.promotion_type,
			total_amount: this.data.promotion.sale_price,
			paid_amount: this.data.promotion.sale_price,
			refund_amount: this.data.promotion.discount_price,
			commission: this.data.promotion.commission,
		};
		console.debug("data to place order", data);

		Toast({
			context: this,
			selector: "#t-toast",
			message: "订单提交成功，正在拉起支付",
			duration: 3000,
			// icon: "check-circle",
		});
		wx.hideLoading();
		this.payLock = false;

		const res = await this.placeOrder(data);
		log.debug("placeOrder", res);

		log.debug("拉起支付");
		const that = this;
		const orderId = res.id;
		const orderNo = res.orderNo;
		wx.cloud.callFunction({
			name: "place_order",
			data: {
				body: that.data.promotion.name,
				outTradeNo: orderNo,
				totalFee: Math.round(data.paid_amount * 100), // 默认单位是分
			},
			success: (res) => {
				wx.hideLoading();
				Toast({
					context: that,
					selector: "#t-toast",
					message: "订单提交成功",
					icon: "check-circle",
					duration: 1000,
				});

				console.debug("wx.cloud.callFunction unified_order:", res);
				const payment = res.result.payment;
				try {
					wx.requestPayment({
						...payment,
						success(res) {
							log.debug("wx.requestPayment success", res);
							if (res.errMsg === "requestPayment:ok") {
								if (that.data.promotion.promotion_type === "tandian1") {
									wx.redirectTo({
										url: `/pages/order/list/index?id=${orderId}`,
									});
									return;
								}
								if (that.data.promotion.promotion_type === "tandian2") {
									wx.redirectTo({
										url: `/pages/order/reserve/index?id=${orderId}`,
									});
									return;
								}
								if (that.data.promotion.promotion_type === "ykj") {
									wx.redirectTo({
										url: `/pages/order/list/index?id=${orderId}`,
									});
									return;
								}
							}
						},
						async fail(err) {
							log.debug("wx.requestPayment fail", res);
							wx.hideLoading();
							if (err.errMsg === "requestPayment:fail cancel") {
								wx.showToast({
									title: "用户取消支付",
									icon: "error",
									duration: 2000,
								});
								// log.debug("orderStore.cancelPayment: ", err);
								// await that.cancelPayment(orderNo);
								// return;
							}
							await wx.cloud.callFunction({
								name: "query_order",
								data: {
									order_no: orderNo,
								},
								success: (res) => {
									console.debug(
										"wx.cloud.callFunction query_order for",
										orderNo,
										"when cancel payment, response:",
										res,
									);
								},
							});
							wx.redirectTo({
								url: `/pages/order/list/index`,
							});
							return
						},
					});
				} catch (error) {
					log.error(error);
				}
			},
			fail: console.error,
		});

		this.setData({
			submitting: false,
		});
		return;

		setTimeout(() => {
			this.payLock = false;
			wx.navigateTo({
				url: "/pages/order/list/index",
			});
		}, 2000);
	},

	// 优惠券选择确认
	onCoupons(_e) {
		this.setData({ couponsShow: false });
		Toast({
			context: this,
			selector: "#t-toast",
			message: "优惠券选择功能开发中",
			duration: 2000,
			icon: "help-circle",
		});
	},
});
