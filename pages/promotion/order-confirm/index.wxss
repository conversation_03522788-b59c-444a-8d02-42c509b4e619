/* pages/promotion/order-confirm/index.wxss */

.order-confirm {
  box-sizing: border-box;
  background: #f6f6f6;
  padding: 24rpx 0 calc(env(safe-area-inset-bottom) + 136rpx);
  min-height: 100vh;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f6f6f6;
}

/* 底部支付栏 */
.order-confirm .wx-pay-cover {
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 10;
  background: #fff;
  height: 112rpx;
  padding-bottom: env(safe-area-inset-bottom);
}

.order-confirm .wx-pay-cover .wx-pay {
  width: 100%;
  height: 100rpx;
  box-sizing: border-box;
  padding: 0rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-confirm .wx-pay-cover .wx-pay .price {
  color: #fa4126;
  font-weight: bold;
  font-size: 63rpx;
  line-height: 88rpx;
}

.order-confirm .wx-pay-cover .wx-pay .submit-btn {
  height: 80rpx;
  width: 240rpx;
  border-radius: 40rpx;
  background-color: #fa4126;
  color: #ffffff;
  line-height: 80rpx;
  font-weight: bold;
  font-size: 28rpx;
  text-align: center;
}

.order-confirm .wx-pay-cover .wx-pay .btn-gray {
  background: #cccccc;
}

/* 商品列表 */
.order-wrapper .store-wrapper {
  width: 100%;
  height: 96rpx;
  box-sizing: border-box;
  padding: 0 32rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  line-height: 40rpx;
  color: #333333;
  background-color: #ffffff;
}

.order-wrapper .store-wrapper .store-logo {
  margin-right: 16rpx;
}

.order-wrapper .goods-wrapper {
  width: 100%;
  box-sizing: border-box;
  padding: 16rpx 32rpx;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  font-size: 24rpx;
  line-height: 32rpx;
  color: #999999;
  background-color: #ffffff;
}

.goods-wrapper .goods-image {
  width: 176rpx;
  height: 176rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 16rpx;
}

.goods-wrapper .goods-content {
  flex: 1;
}

.goods-wrapper .goods-content .goods-title {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 28rpx;
  line-height: 40rpx;
  margin-bottom: 12rpx;
  color: #333333;
  margin-right: 16rpx;
}

.goods-wrapper .goods-content .goods-specs {
  font-size: 24rpx;
  line-height: 32rpx;
  color: #999999;
  margin-bottom: 8rpx;
}

.goods-wrapper .goods-content .goods-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.goods-wrapper .goods-right {
  min-width: 128rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.goods-right .goods-price {
  color: #333333;
  font-size: 32rpx;
  line-height: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.goods-right .goods-num {
  text-align: right;
}

/* 支付详情 */
.order-confirm .pay-detail {
  background-color: #ffffff;
  padding: 16rpx 32rpx;
  width: 100%;
  box-sizing: border-box;
}

.order-confirm .pay-detail .pay-item {
  width: 100%;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 26rpx;
  line-height: 36rpx;
  color: #666666;
}

.order-confirm .pay-detail .pay-item .pay-item__right {
  color: #333333;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  max-width: 400rpx;
}

.order-confirm .pay-detail .pay-item .font-bold {
  font-weight: bold;
}

.order-confirm .pay-detail .pay-item .primary {
  color: #fa4126;
}

/* 金额汇总 */
.order-confirm .amount-wrapper {
  width: 100%;
  box-sizing: border-box;
  background-color: #ffffff;
  padding: 0rpx 32rpx;
  height: 96rpx;
}

.order-confirm .pay-amount {
  width: 100%;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 28rpx;
  color: #333333;
  position: relative;
}

.order-confirm .pay-amount::after {
  position: absolute;
  content: ' ';
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  transform: scale(0.5);
  transform-origin: 0 0;
  border-top: 2rpx solid #f5f5f5;
}

.order-confirm .pay-amount .order-num {
  color: #999999;
  padding-right: 8rpx;
}

.order-confirm .pay-amount .total-price {
  font-size: 36rpx;
  color: #fa4126;
  font-weight: bold;
  padding-left: 8rpx;
}

.t-toast {
	--td-toast-max-width: 800rpx;
}

.t-tag--outline.t-tag--default {
  --td-tag-default-color: #ff5f15;
  --td-tag-default-font-color: #ff5f15;
}