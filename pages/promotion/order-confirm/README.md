# 推广活动订单确认页面

## 功能描述

这是一个专门为推广活动设计的订单确认页面，用户可以在该页面确认商品信息、收货地址、优惠券等信息，并完成下单支付。

## 页面结构

### 主要组件
- **地址卡片**: 显示或添加收货地址
- **商品列表**: 展示待购买的商品信息
- **支付详情**: 显示商品总额、运费、优惠等信息
- **底部支付栏**: 显示总价和提交订单按钮

### 功能特性
- ✅ 收货地址管理
- ✅ 商品信息展示
- ✅ 优惠券选择
- ✅ 订单备注
- ✅ 发票信息（预留）
- ✅ 支付功能
- ✅ 异常商品处理

## 文件结构

```
pages/promotion/order-confirm/
├── index.wxml          # 页面结构
├── index.wxss          # 页面样式
├── index.js            # 页面逻辑
├── index.json          # 页面配置
├── order.wxs           # 订单相关工具函数
├── handleInvoice.wxs   # 发票处理工具函数
├── getNotes.wxs        # 备注处理工具函数
├── pay.js              # 支付相关功能
└── README.md           # 说明文档
```

## 使用方式

### 页面跳转
```javascript
// 从推广活动页面跳转
wx.navigateTo({
  url: '/pages/promotion/order-confirm/index?promotionId=123&activityId=456'
});

// 从购物车跳转
wx.navigateTo({
  url: '/pages/promotion/order-confirm/index?goodsRequestList=' + JSON.stringify(goodsList)
});
```

### 参数说明
- `promotionId`: 推广活动ID
- `activityId`: 活动ID
- `goodsRequestList`: 商品请求列表（JSON字符串）

## 数据格式

### 商品数据结构
```javascript
{
  quantity: 1,
  storeId: 'store001',
  uid: 'user001',
  saasId: 'saas001',
  spuId: 'spu001',
  goodsName: '商品名称',
  skuId: 'sku001',
  storeName: '店铺名称',
  roomId: 'room001'
}
```

### 结算详情数据结构
```javascript
{
  storeGoodsList: [
    {
      storeId: 'store001',
      storeName: '店铺名称',
      storeTotalPayAmount: '99.00',
      skuDetailVos: [
        {
          image: '商品图片',
          goodsName: '商品名称',
          skuSpecLst: [{ specValue: '规格' }],
          tagPrice: '99.00',
          settlePrice: '99.00',
          tagText: '活动标签',
          quantity: 1,
          skuId: 'sku001',
          spuId: 'spu001',
          storeId: 'store001'
        }
      ],
      couponList: []
    }
  ],
  totalSalePrice: '99.00',
  totalDeliveryFee: '0',
  totalPromotionAmount: '10.00',
  totalCouponAmount: '0',
  totalPayAmount: '89.00',
  totalGoodsCount: 1,
  settleType: 1,
  invoiceSupport: false
}
```

## 注意事项

1. 页面依赖现有的订单确认组件，确保相关组件路径正确
2. 支付功能目前使用模拟数据，实际使用时需要对接真实的支付API
3. 推广活动商品获取功能需要根据实际业务需求实现
4. 页面样式基于TDesign组件库，保持与项目整体风格一致

## 开发计划

- [ ] 对接真实的推广活动API
- [ ] 完善支付功能
- [ ] 添加发票功能
- [ ] 优化用户体验
- [ ] 添加单元测试 