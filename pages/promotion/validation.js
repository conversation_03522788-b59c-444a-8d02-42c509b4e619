// 探店订单需要检查时候有手机号和名片符合要求、15天内是否购买过、今日库存、是否有探店券
// 特价团仅需检查有手机号和不超总库存

// 检查用户是否被封禁
export const checkIfUserBanned = (userInfo) => {
  console.debug("checkIfUserBanned", userInfo.banned);
  if (userInfo.banned) {
    // wx.showToast({
    //   title: "账号已被封禁",
    //   icon: "error",
    //   duration: 3000,
    // });
    return {
      isValid: false,
      action: "showToast",
      message: "账号已被封禁"
    }; // 返回 false 表示用户被封禁，应该停止执行
  }
  return {
    isValid: true,
    message: "账号正常，未被封禁"
  };  // 返回 true 表示用户正常，可以继续执行
}

// 检查是否有手机号
export const checkPhone = (userInfo) => {
  if (!userInfo.phone) {
    return {
      isValid: false,
      action: 'showBindPhoneDialog', // 指示需要显示绑定手机号对话框
      message: "请先绑定手机号"
    };
  }
  return { isValid: true };
}

// 检查探店券余额
export const checkCoupons = (userInfo) => {
  if (userInfo.remain_coupons < 1) {
    return {
      isValid: false,
      action: 'showToast',
      message: "探店券已用完"
    };
  }
  return { isValid: true };
}

// 检查库存
export const checkStock = (promotion) => {
  if (promotion.realtime_quantity < 1) {
    return {
      isValid: false,
      action: 'showToast',
      message: "今日份额已售完"
    };
  }
  return { isValid: true };
}

// 检查名片验证
export const checkCards = (cards, promotion) => {
  const verifiedPlatformCards = cards
    .filter((x) => x.verified === 1)
    .filter((x) => x.platform === promotion.platform)
    .sort((a, b) => b.level - a.level)
    .sort((a, b) => b.followers_count - a.followers_count);

  if (verifiedPlatformCards.length < 1) {
    return {
      isValid: false,
      action: 'showBindCardDialog',
      message: "请先认证名片"
    };
  }
  return { isValid: true, data: verifiedPlatformCards };
}

// 检查等级要求
export const checkLevel = (verifiedCards, promotion) => {
  if (promotion.requires === "1") {
    const qualifiedCards = verifiedCards
      .sort((a, b) => b.level - a.level)
      .filter((x) => x.level >= promotion.level);

    if (qualifiedCards.length < 1) {
      return {
        isValid: false,
        action: 'showToast',
        message: "等级不达标"
      };
    }
  }
  return { isValid: true };
}

// 检查粉丝数要求
export const checkFollowers = (verifiedCards, promotion) => {
  if (promotion.requires === "2") {
    const qualifiedCards = verifiedCards
      .sort((a, b) => b.followers_count - a.followers_count)
      .filter((x) => x.followers_count >= promotion.followers_count);

    if (qualifiedCards.length < 1) {
      return {
        isValid: false,
        action: 'showToast',
        message: "粉丝数不达标"
      };
    }
  }
  return { isValid: true };
}

// 检查等级和粉丝数要求
export const checkLevelAndFollowers = (verifiedCards, promotion) => {
  if (promotion.requires === "3") {
    const qualifiedCards = verifiedCards
      .filter((x) => x.followers_count >= promotion.followers_count)
      .filter((x) => x.level >= promotion.level);

    if (qualifiedCards.length < 1) {
      return {
        isValid: false,
        action: 'showToast',
        message: "要求不达标"
      };
    }
  }
  return { isValid: true };
}

// 验证管道 - 统一的验证流程
export const validateOrder = (userInfo, promotion, cards) => {
  const validations = [
    () => checkIfUserBanned(userInfo),
    () => checkPhone(userInfo),
    () => checkCoupons(userInfo),
    () => checkStock(promotion),
    () => checkCards(cards, promotion),
  ];

  // 执行基础验证
  for (const validate of validations) {
    const result = validate();
    if (!result.isValid) {
      return result;
    }
    // 如果是 checkCards，保存验证过的名片数据
    if (result.data) {
      var verifiedCards = result.data;
    }
  }

  // 执行名片相关验证
  if (verifiedCards) {
    const cardValidations = [
      () => checkLevel(verifiedCards, promotion),
      () => checkFollowers(verifiedCards, promotion),
      () => checkLevelAndFollowers(verifiedCards, promotion),
    ];

    for (const validate of cardValidations) {
      const result = validate();
      if (!result.isValid) {
        return result;
      }
    }
  }

  return { isValid: true };
}