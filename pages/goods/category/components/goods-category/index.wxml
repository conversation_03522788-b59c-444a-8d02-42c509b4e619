<view class="goods-category custom-class">
	<c-sidebar custom-class="custom-sidebar" bindchange="onParentChange" activeKey="{{activeKey}}">
		<c-sidebar-item
		  wx:for="{{ category }}"
		  wx:key="index"
		  title="{{ item.name }}"
		  disabled="{{ item.disabled }}"
		/>
	</c-sidebar>
	<view class="goods-category__right">
		<c-tabbar
		  wx:if="{{isSlotRight}}"
		  activeKey="{{subActiveKey}}"
		  bindchange="onChildChange"
		  showMore
		>
			<slot/>
		</c-tabbar>
		<view wx:if="{{!isSlotRight}}" class="goods-category-normal">
			<view class="goods-category-normal-item" wx:if="{{category[activeKey].children && category[activeKey].children.length > 0}}">
				<block wx:for="{{category[activeKey].children}}" wx:key="index" wx:if="{{level === 3 && item.children && item.children.length > 0}}">
					<view class="flex goods-category-normal-item-title">
						{{item.name}}
					</view>
					<view class="goods-category-normal-item-container">
						<view
						  class="goods-category-normal-item-container-item"
						  wx:for="{{item.children}}"
						  wx:for-index="subIndex"
						  wx:key="subIndex"
						  wx:for-item="subItem"
						  bindtap="changCategory"
						  data-item="{{subItem}}"
						>
							<t-image src="{{subItem.thumbnail}}" t-class="image" />
							<view class="flex goods-category-normal-item-container-item-title">
								{{subItem.name}}
							</view>
						</view>
					</view>
				</block>
				<view class="goods-category-normal-item-second-container" wx:if="{{level === 2}}">
					<block wx:for="{{category[activeKey].children}}" wx:key="index">
						<view
						  class="goods-category-normal-item-second-container-item"
						  wx:for-key="index"
						  bindtap="changCategory"
						  data-item="{{item}}"
						>
							<t-image src="{{item.thumbnail}}" t-class="image" />
							<view class="flex goods-category-normal-item-container-item-title">
								{{item.name}}
							</view>
						</view>
					</block>
				</view>
			</view>
		</view>
	</view>
</view>

