.c-tabbar-more {
  width: 100%;
  height: calc(100% - var(--tabbar-height, 100rpx));
  position: absolute;
  top: var(--tabbar-height, 100rpx);
}
.c-tabbar-more__btn {
  position: absolute;
  top: calc(0rpx - var(--tabbar-height, 100rpx));
  right: 0;
  width: 80rpx;
  height: var(--tabbar-height, 100rpx);
  line-height: var(--tabbar-height, 100rpx);
  background-color: var(--tabbar-background-color, white);
  box-shadow: -20rpx 0 20rpx -10rpx var(--tabbar-background-color, white);
  text-align: center;
}
.c-tabbar-more__btn .market {
  font-size: 20rpx;
}
.t-tabbar-more__boardwrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.t-tabbar-more__mask {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}
.c-tabbar-more__board {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  max-height: 100%;
}
.c-tabbar-more__boardinner {
  padding: 20rpx 0 20rpx 20rpx;
  background-color: var(--tabbar-background-color, white);
  display: flex;
  flex-flow: row wrap;
}
.c-tabbar-more__item {
  margin: 0 20rpx 20rpx 0;
  flex: 0 0 calc((100% - 60rpx) / 3);
  box-sizing: border-box;
  padding: 0 10rpx;
  border-radius: 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 22rpx;
  color: #5d5d5d;
  background-color: #eee;
}
.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
