.popup-container {
  background-color: #ffffff;
  position: relative;
  z-index: 100;
  border-radius: 16rpx 16rpx 0 0;
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
}

.popup-container .popup-close {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  z-index: 9;
  color: #999999;
}

.popup-sku-header {
  display: flex;
  padding: 30rpx 28rpx 0 30rpx;
}

.popup-sku-header .popup-sku-header__img {
  width: 176rpx;
  height: 176rpx;
  border-radius: 8rpx;
  background: #d8d8d8;
  margin-right: 24rpx;
}

.popup-sku-header .popup-sku-header__goods-info {
  position: relative;
  width: 500rpx;
}

.popup-sku-header .popup-sku-header__goods-info .popup-sku__goods-name {
  font-size: 28rpx;
  line-height: 40rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
  overflow: hidden;
  width: 430rpx;
  text-overflow: ellipsis;
}

.popup-sku-header .popup-sku-header__goods-info .popup-sku__selected-spec {
  display: flex;
  color: #333333;
  font-size: 26rpx;
  line-height: 36rpx;
}

.popup-sku-header
  .popup-sku-header__goods-info
  .popup-sku__selected-spec
  .popup-sku__selected-item {
  margin-right: 10rpx;
}

.popup-sku-body {
  margin: 0 30rpx 40rpx;
  max-height: 600rpx;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}

.popup-sku-body .popup-sku-group-container .popup-sku-row {
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.popup-sku-body
  .popup-sku-group-container
  .popup-sku-row
  .popup-sku-row__title {
  font-size: 26rpx;
  color: #333;
}

.popup-sku-body .popup-sku-group-container .popup-sku-row .popup-sku-row__item {
  font-size: 24rpx;
  color: #333;
  min-width: 128rpx;
  height: 56rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  border: 2rpx solid #f5f5f5;
  margin: 19rpx 26rpx 0 0;
  padding: 0 16rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.popup-sku-body
  .popup-sku-group-container
  .popup-sku-row
  .popup-sku-row__item.popup-sku-row__item--active {
  border: 2rpx solid #fa4126;
  color: #fa4126;
  background: rgba(255, 95, 21, 0.04);
}

.popup-sku-body
  .popup-sku-group-container
  .popup-sku-row
  .disabled-sku-selected {
  background: #f5f5f5 !important;
  color: #cccccc;
}

.popup-sku-body .popup-sku-stepper-stock .popup-sku-stepper-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 40rpx 0;
}

.popup-sku-body
  .popup-sku-stepper-stock
  .popup-sku-stepper-container
  .popup-sku__stepper-title {
  display: flex;
  font-size: 26rpx;
  color: #333;
}

.popup-sku-body
  .popup-sku-stepper-stock
  .popup-sku-stepper-container
  .popup-sku__stepper-title
  .limit-text {
  margin-left: 10rpx;
  color: #999999;
}

.popup-sku-body
  .popup-sku-stepper-stock
  .popup-sku-stepper-container
  .popup-stepper {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  font-size: 28px;
  height: 48rpx;
  line-height: 62rpx;
}

.popup-sku-body
  .popup-sku-stepper-stock
  .popup-sku-stepper-container
  .popup-stepper
  .input-btn,
.popup-sku-body
  .popup-sku-stepper-stock
  .popup-sku-stepper-container
  .popup-stepper
  .input-num-wrap {
  position: relative;
  height: 100%;
  text-align: center;
  background-color: #f5f5f5;
  border-radius: 4rpx;
}

.popup-sku-body
  .popup-sku-stepper-stock
  .popup-sku-stepper-container
  .popup-stepper
  .input-num-wrap {
  color: #282828;
  display: flex;
  max-width: 76rpx;
  align-items: center;
  justify-content: space-between;
}

.popup-sku-body
  .popup-sku-stepper-stock
  .popup-sku-stepper-container
  .popup-stepper
  .input-num-wrap
  .input-num {
  height: 100%;
  width: auto;
  font-weight: 600;
  font-size: 30rpx;
}

.popup-sku-body
  .popup-sku-stepper-stock
  .popup-sku-stepper-container
  .popup-stepper
  .input-btn {
  width: 48rpx;
}

.popup-sku-body
  .popup-sku-stepper-stock
  .popup-sku-stepper-container
  .popup-stepper
  .popup-stepper__minus {
  margin-right: 4rpx;
  border-radius: 4rpx;
  color: #9a979b;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-sku-body
  .popup-sku-stepper-stock
  .popup-sku-stepper-container
  .popup-stepper
  .popup-stepper__plus {
  margin-left: 4rpx;
  border-radius: 4rpx;
  color: #9a979b;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-sku-body
  .popup-sku-stepper-stock
  .popup-sku-stepper-container
  .popup-stepper
  .popup-stepper__plus::after {
  width: 24rpx;
  height: 3rpx;
  background-color: #999999;
}

.popup-sku-body
  .popup-sku-stepper-stock
  .popup-sku-stepper-container
  .popup-stepper
  .popup-stepper__plus::before {
  width: 3rpx;
  height: 24rpx;
  background-color: #999999;
}

.popup-sku-actions {
  font-size: 32rpx;
  height: 80rpx;
  text-align: center;
  line-height: 80rpx;
  padding: 0 20rpx;
}

.popup-sku-actions .sku-operate {
  height: 80rpx;
  width: 50%;
  color: #fff;
  border-radius: 48rpx;
}

.popup-sku-actions .sku-operate .sku-operate-addCart {
  background-color: #ffece9;
  color: #fa4126;
  border-radius: 48rpx 0 0 48rpx;
}

.popup-sku-actions .sku-operate .sku-operate-addCart.disabled {
  background: rgb(221, 221, 221);
  color: #fff;
}

.popup-sku-actions .sku-operate .sku-operate-buyNow {
  background-color: #fa4126;
  border-radius: 0 48rpx 48rpx 0;
}

.popup-sku-actions .sku-operate .sku-operate-buyNow.disabled {
  color: #fff;
  background: rgb(198, 198, 198);
}

.popup-sku-actions .sku-operate .selected-sku-btn {
  width: 100%;
}

.popup-container .single-confirm-btn {
  border-radius: 48rpx;
  color: #ffffff;
  margin: 0 32rpx;
  font-size: 32rpx;
  height: 80rpx;
  text-align: center;
  line-height: 88rpx;
  background-color: #fa4126;
}

.popup-container .single-confirm-btn.disabled {
  font-size: 32rpx;
  color: #fff;
  background-color: #dddddd;
}
