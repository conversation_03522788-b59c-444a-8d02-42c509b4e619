<t-popup visible="{{show}}" placement="bottom" bind:visible-change="handlePopupHide">
  <view class="popup-container">
    <view class="popup-close" bindtap="handlePopupHide">
      <t-icon name="close" size="36rpx" />
    </view>
    <view class="popup-sku-header">
      <t-image t-class="popup-sku-header__img" src="{{src}}" />
      <view class="popup-sku-header__goods-info">
        <view class="popup-sku__goods-name">{{title}}</view>
        <view class="goods-price-container">
          <slot name="goods-price" />
        </view>
        <!-- 已选规格 -->
        <view class="popup-sku__selected-spec">
          <view>选择：</view>
          <view wx:for="{{specList}}" wx:key="specId">
            <view
              class="popup-sku__selected-item"
              wx:for="{{item.specValueList}}"
              wx:for-item="selectedItem"
              wx:if="{{selectedItem.isSelected}}"
              wx:key="specValueId"
            >
              {{selectedItem.specValue}}
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="popup-sku-body">
      <view class="popup-sku-group-container">
        <view class="popup-sku-row" wx:for="{{specList}}" wx:key="specId">
          <view class="popup-sku-row__title">{{item.title}}</view>
          <block
            wx:for="{{item.specValueList}}"
            wx:for-item="valuesItem"
            wx:for-index="valuesIndex"
            wx:key="specValueId"
          >
            <view
              class="popup-sku-row__item {{valuesItem.isSelected ? 'popup-sku-row__item--active' : ''}} {{!valuesItem.hasStockObj.hasStock || !isStock ? 'disabled-sku-selected' : ''}}"
              data-specid="{{item.specId}}"
              data-id="{{valuesItem.specValueId}}"
              data-val="{{valuesItem.specValue}}"
              data-hasStock="{{valuesItem.hasStockObj.hasStock}}"
              bindtap="toChooseItem"
            >
              {{valuesItem.specValue}}
            </view>
          </block>
        </view>
      </view>
      <view class="popup-sku-stepper-stock">
        <view class="popup-sku-stepper-container">
          <view class="popup-sku__stepper-title">
            购买数量
            <view class="limit-text" wx:if="{{limitBuyInfo}}"> ({{limitBuyInfo}}) </view>
          </view>
          <t-stepper value="{{buyNum}}" min="{{1}}" max="{{2}}" theme="filled" bind:change="handleBuyNumChange" />
        </view>
      </view>
    </view>
    <view wx:if="{{outOperateStatus}}" class="single-confirm-btn {{!isStock ? 'disabled' : ''}}" bindtap="specsConfirm">
      确定
    </view>
    <view
      class="popup-sku-actions flex flex-between {{!isStock ? 'popup-sku-disabled' : ''}}"
      wx:if="{{!outOperateStatus}}"
    >
      <view class="sku-operate">
        <view class="selected-sku-btn sku-operate-addCart {{!isStock ? 'disabled' : ''}}" bindtap="addCart">
          加入购物车
        </view>
      </view>
      <view class="sku-operate">
        <view class="selected-sku-btn sku-operate-buyNow  {{!isStock ? 'disabled' : ''}}" bindtap="buyNow">
          立即购买
        </view>
      </view>
    </view>
    <slot name="bottomSlot" />
  </view>
</t-popup>
<t-toast id="t-toast" />
