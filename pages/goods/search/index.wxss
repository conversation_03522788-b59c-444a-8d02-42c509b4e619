.search-page {
  box-sizing: border-box;
  width: 100vw;
  height: 100vh;
  padding: 0 30rpx;
}

.search-page .t-class__input-container {
  height: 64rpx !important;
  border-radius: 32rpx !important;
}

.search-page .t-search__input {
  font-size: 28rpx !important;
  color: #333 !important;
}

.search-page .search-wrap {
  margin-top: 44rpx;
}

.search-page .history-wrap {
  margin-bottom: 20px;
}

.search-page .search-header {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
}

.search-page .search-title {
  font-size: 30rpx;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: rgba(51, 51, 51, 1);
  line-height: 42rpx;
}

.search-page .search-clear {
  font-size: 24rpx;
  font-family: PingFang SC;
  line-height: 32rpx;
  color: #999999;
  font-weight: normal;
}

.search-page .search-content {
  overflow: hidden;
  display: flex;
  flex-flow: row wrap;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 24rpx;
}

.search-page .search-item {
  color: #333333;
  font-size: 24rpx;
  line-height: 32rpx;
  font-weight: normal;
  margin-right: 24rpx;
  margin-bottom: 24rpx;
  background: #f5f5f5;
  border-radius: 38rpx;
  padding: 12rpx 24rpx;
}

.search-page .hover-history-item {
  position: relative;
  top: 3rpx;
  left: 3rpx;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1) inset;
}

.add-notes__confirm {
  color: #fa4126 !important;
}
