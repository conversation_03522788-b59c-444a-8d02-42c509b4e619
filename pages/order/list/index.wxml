<wxs module="filter" src="../../../utils/util.wxs"></wxs>

<view class="page-container">
  <view class="tab-bar">
    <view class="tab-bar__placeholder" />
    <t-tabs
      t-class="tab-bar__inner"
      t-class-active="tab-bar__active"
      t-class-track="t-tabs-track"
      bind:change="onTabChange"
      value="{{status}}"
      style="position: fixed; top: 0; left: 0; z-index: 100"
    >
      <t-tab-panel
        wx:for="{{tabs}}"
        wx:for-index="index"
        wx:for-item="item"
        wx:key="index"
        label="{{item.text}}"
        value="{{item.key}}"
      />
    </t-tabs>
  </view>
  
<t-pull-down-refresh
  value="{{enable}}"
  loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}"
  usingCustomNavbar
  bind:refresh="onPullDownRefresh"
  bind:scrolltolower="onReachBottom"
>
    <order-card
      wx:if="{{orders.length > 0}}"
      wx:for="{{orders}}"
      wx:for-item="order"
      wx:key="_id"
      order="{{order}}"
      defaultShowNum="{{3}}"
      data-order="{{order}}"
      bindcardtap="onOrderCardTap"
      useLogoSlot="{{true}}"
      useTopRightSlot="{{false}}"
    >
      <view slot="top-left" class="order-number" catchtap="onCopy" data-order-no="{{order.order_no}}">
        <view class="flex items-center">
          <text decode style="display:inline-flex;">订单号&nbsp;</text>
          <text style="display:inline-flex;">{{order.order_no}}</text>
          <t-icon name="copy" size="28rpx" color="#FF9D00" custom-style="display:inline-flex;margin-left:14rpx" />
        </view>
      </view>
      <promotion-card order="{{order}}" promotion="{{order.promotion}}" wx:if="{{order && order.promotion}}"/>
      <view slot="more" class="card-bottom">
        <view class="cancel-order-btn" catchtap="onRefundOrder" data-order-no="{{order.order_no}}" wx:if="{{order.status == '10' || order.status == '11'}}">申请退款</view>
        <!-- <view class="cancel-order-btn" catchtap="onPayOrder" data-order="{{order}}" wx:if="{{order.status == '0'}}">立即支付</view> -->
        <view class="price-total">
          <view class="" wx:if="{{order && order.promotion.promotion_type != 'ykj'}}">
            <text>总价</text>
            <price fill price="{{order.total_amount + ''}}" />
            <text>，完成后返</text>
            <price fill price="{{order.refund_amount + ''}}" />
            <text decode>&nbsp;</text>
          </view>
          <text class="bold-price" decode="{{true}}">实付&nbsp;</text>
          <price fill class="real-pay" price="{{order.paid_amount + ''}}" decimalSmaller />
        </view>
        <!-- 订单按钮栏 -->
        <!-- <order-button-bar order="{{order}}" bindrefresh="onRefresh" data-order="{{order}}" /> -->
      </view>
    </order-card>
    
    <!-- 列表加载中/已全部加载 -->
    <load-more
      wx:if="{{true}}"
      list-is-empty="{{!orders.length}}"
      status="{{listLoading}}"
      bindretry="onReTryLoad"
    >
      <!-- 空态 -->
      <view slot="empty" class="empty-wrapper">
        <t-empty t-class="t-empty-text" src="{{emptyImg}}">暂无相关订单</t-empty>
      </view>
    </load-more>
  </t-pull-down-refresh>
</view>


<t-toast id="t-toast" />

<t-dialog id="t-dialog" />

<t-dialog
  visible="{{showWarnConfirm}}"
  content="退款成功后，资金将原路返回支付账户，请查收"
  confirm-btn="{{ { content: '确定', variant: 'base', theme: 'danger' } }}"
  cancel-btn="取消"
  bind:confirm="hanleRefundOrder"
  bind:cancel="closeDialog"
/>

<t-fab icon="help" bind:click="handleClickFab"
  y-bounds="{{[0, 132]}}"
  aria-label="help" text=""></t-fab>
