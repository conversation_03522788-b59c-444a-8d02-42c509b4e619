import { createStoreBindings } from "mobx-miniprogram-bindings";
import { orderStore } from "../../../stores/orderStore";

import { OrderStatus } from "../config";
import { OrderEnum } from "../config";

// import {
//   fetchOrders,
//   fetchOrdersCount,
// } from '../../../services/order/orderList';
// import { cosThumb } from '../../../utils/util';

Page({
	page: {
		size: 5,
		num: 1,
	},

	data: {
		tabs1: [
			{ key: "-1", text: "全部" },
			{ key: OrderStatus.PENDING_PAYMENT, text: "待核销", info: "" },
			{ key: OrderStatus.PENDING_DELIVERY, text: "待上传", info: "" },
			{ key: OrderStatus.PENDING_RECEIPT, text: "审核中", info: "" },
			{ key: OrderStatus.PENDING_RECEIPT, text: "已完成", info: "" },
			{ key: OrderStatus.PENDING_RECEIPT, text: "已驳回", info: "" },
			{ key: OrderStatus.PENDING_RECEIPT, text: "已取消", info: "" },
			{ key: OrderStatus.PENDING_RECEIPT, text: "已过期", info: "" },
			{ key: OrderStatus.COMPLETE, text: "已完成", info: "" },
		],
		tabs: [
			{ key: "-1", text: "全部" },
			{ key: OrderEnum.PENDING_PAYMENT, text: "待支付", info: "" },
			{ key: OrderEnum.PAID, text: "待核销", info: "" },
			{ key: OrderEnum.PENDING_UPLOAD, text: "已核销/待上传", info: "" },
			{ key: OrderEnum.UNDER_REVIEW, text: "审核中", info: "" },
			{ key: OrderEnum.AUDIT_PASS, text: "已完成", info: "" },
			{ key: OrderEnum.AUDIT_FAIL, text: "已驳回", info: "" },
			{ key: OrderEnum.REFUNDED, text: "已退款", info: "" },
			{ key: OrderEnum.EXPIRED, text: "已过期", info: "" },
		],
		curTab: -1,
		status: -1,
		listLoading: 0,
		pullDownRefreshing: false,
		emptyImg:
			"https://cdn-we-retail.ym.tencent.com/miniapp/order/empty-order-list.png",
		backRefresh: false,
		dropdownMenuOptions: [
			{
				value: "new",
				label: "退款",
			},
		],
	},

	async restoreStatus(options) {
		if (options && options.status) {
			const status = options.status;
			console.debug("options.status", status);
			await this.setStatus(options.status);
		} else {
			console.debug("no status params");
			await this.setStatus("-1");
		}
	},

	async onLoad(options) {
		// 将 store 绑定到页面
		this.orderStoreBindings = createStoreBindings(this, {
			store: orderStore,
			fields: ["orders", "total", "pageSize", "pageNumber", "status"],
			actions: ["setPageNumber", "setStatus", "fetchOrders", "cancelOrder"],
		});

		await this.restoreStatus(options);
		// this.setData({
		//   status: options.status
		// })
		// let status = parseInt(options.status);
		// status = this.data.tabs.map((t) => t.key).includes(status) ? status : -1;
		// this.init(status);
		// this.pullDownRefresh = this.selectComponent('#wr-pull-down-refresh');

		// console.debug("this.data.status", this.data.status)

		// // 等待状态更新完成后再获取最新状态
		// setTimeout(() => {
		//   console.debug("this.data.status", this.data.status);
		//   console.debug("this.status", this.status);
		// }, 0);

		// setTimeout(() => {
		//   console.debug("setTimeout this.data.status", this.data.status)
		// }, 2000)

		// this.fetchOrders(this.data.status);
		// this.init();
	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {
		this.orderStoreBindings.destroyStoreBindings();
	},

	onShow() {
		// if (!this.data.backRefresh) return;
		this.onRefresh();
		// this.setData({ backRefresh: false });
	},

	onReachBottom() {
		console.debug("onReachBottom");
		// if (this.data.listLoading === 0) {
		// this.getOrderList(this.data.curTab);
		// }
		if (this.data.pageNumber * this.data.pageSize < this.data.total) {
			this.setData({
				listLoading: 1,
			});
			this.setPageNumber(this.data.pageNumber + 1);
			this.fetchOrders(this.data.status);
		} else {
			console.debug("no more pagination");
			this.setData({
				listLoading: 2,
			});
		}
	},

	// onPageScroll(e) {
	//   this.pullDownRefresh && this.pullDownRefresh.onPageScroll(e);
	// },

	onPullDownRefresh(e) {
		console.debug("onPullDownRefresh");
		// const { callback } = e.detail;
		// this.setData({ pullDownRefreshing: true });
		// this.refreshList(this.data.curTab)
		//   .then(() => {
		//     this.setData({ pullDownRefreshing: false });
		//     callback && callback();
		//   })
		//   .catch((err) => {
		//     this.setData({ pullDownRefreshing: false });
		//     Promise.reject(err);
		//   });
		this.setData({ enable: true });
		setTimeout(() => {
			this.setData({ enable: false });
		}, 1000);

		this.init(this.data.status);
		// this.fetchOrders(this.data.status);
	},

	init(status) {
		console.debug("init", this.data.status);
		status = status !== undefined ? status : this.data.curTab;
		this.setData({
			status,
		});
		// this.refreshList(status);
		this.fetchOrders(this.data.status, 1);
	},

	// getOrderList(statusCode = -1, reset = false) {
	//   const params = {
	//     parameter: {
	//       pageSize: this.page.size,
	//       pageNum: this.page.num,
	//     },
	//   };
	//   if (statusCode !== -1) params.parameter.orderStatus = statusCode;
	//   this.setData({ listLoading: 1 });
	//   return fetchOrders(params)
	//     .then((res) => {
	//       this.page.num++;
	//       let orderList = [];
	//       if (res && res.data && res.data.orders) {
	//         orderList = (res.data.orders || []).map((order) => {
	//           return {
	//             id: order.orderId,
	//             orderNo: order.orderNo,
	//             parentOrderNo: order.parentOrderNo,
	//             storeId: order.storeId,
	//             storeName: order.storeName,
	//             status: order.orderStatus,
	//             statusDesc: order.orderStatusName,
	//             amount: order.paymentAmount,
	//             totalAmount: order.totalAmount,
	//             logisticsNo: order.logisticsVO.logisticsNo,
	//             createTime: order.createTime,
	//             goodsList: (order.orderItemVOs || []).map((goods) => ({
	//               id: goods.id,
	//               thumb: cosThumb(goods.goodsPictureUrl, 70),
	//               title: goods.goodsName,
	//               skuId: goods.skuId,
	//               spuId: goods.spuId,
	//               specs: (goods.specifications || []).map(
	//                 (spec) => spec.specValue,
	//               ),
	//               price: goods.tagPrice ? goods.tagPrice : goods.actualPrice,
	//               num: goods.buyQuantity,
	//               titlePrefixTags: goods.tagText ? [{ text: goods.tagText }] : [],
	//             })),
	//             buttons: order.buttonVOs || [],
	//             groupInfoVo: order.groupInfoVo,
	//             freightFee: order.freightFee,
	//           };
	//         });
	//       }
	//       return new Promise((resolve) => {
	//         if (reset) {
	//           this.setData({ orderList: [] }, () => resolve());
	//         } else resolve();
	//       }).then(() => {
	//         this.setData({
	//           orderList: this.data.orderList.concat(orderList),
	//           listLoading: orderList.length > 0 ? 0 : 2,
	//         });
	//       });
	//     })
	//     .catch((err) => {
	//       this.setData({ listLoading: 3 });
	//       return Promise.reject(err);
	//     });
	// },

	onReTryLoad() {
		// this.getOrderList(this.data.curTab);
	},

	onTabChange(e) {
		console.debug("onTabChange", e);
		const { value } = e.detail;
		// this.setData({
		//   status: value,
		//   pageNumber: 1
		// });
		this.setStatus(value);
		this.setPageNumber(1);

		// console.debug(this.data.status)
		// console.debug(orderStore.status)

		// console.debug(this.data.pageNumber)
		// console.debug(orderStore.pageNumber)

		this.init(value);
	},

	// getOrdersCount() {
	//   return fetchOrdersCount().then((res) => {
	//     const tabsCount = res.data || [];
	//     const { tabs } = this.data;
	//     tabs.forEach((tab) => {
	//       const tabCount = tabsCount.find((c) => c.tabType === tab.key);
	//       if (tabCount) {
	//         tab.info = tabCount.orderNum;
	//       }
	//     });
	//     this.setData({ tabs });
	//   });
	// },

	// refreshList(status = -1) {
	//   this.page = {
	//     size: this.page.size,
	//     num: 1,
	//   };
	//   this.setData({ curTab: status, orderList: [] });

	//   return Promise.all([
	//     // this.getOrderList(status, true),
	//     // this.getOrdersCount(),
	//   ]);
	// },

	onRefresh() {
		setTimeout(() => {
			console.debug("setTimeout this.data.status", this.data.status);
			this.init(this.data.status);
		}, 0);
	},

	onOrderCardTap(e) {
		const { order } = e.currentTarget.dataset;
		wx.navigateTo({
			url: `/pages/order/detail/index?id=${order._id}`,
		});
	},
	handleClickFab(e) {
		console.debug(e);
		wx.navigateTo({
			url: "/pages/faq/index",
		});
	},
	onRefundOrder(e) {
		console.debug(e);
		this.setData({
			showWarnConfirm: true,
			orderToRefund: e.currentTarget.dataset.orderNo,
		});
	},
	onPayOrder(e) {
		const order = e.currentTarget.dataset.order;
		console.debug("order:", order);
		// return
		wx.cloud.callFunction({
			name: "place_order",
			data: {
				body: order.promotion.name,
				outTradeNo: order.order_no,
				// totalFee: order.paid_amount,
			},
			success: (res) => {
				console.debug("wx.cloud.callFunction unified_order:", res);
				const payment = res.result.payment;
				wx.requestPayment({
					...payment,
					success(res) {
						console.log("pay success", res);
						if (res.errMsg === "requestPayment:ok") {
							wx.navigateTo({
								url: "/pages/order/detail/index?id=" + orderId,
							});
						}
					},
					fail(err) {
						console.error("pay fail", err);
						if (err.errMsg === "requestPayment:fail cancel") {
							wx.showToast({
								title: "用户取消支付",
								icon: "error",
								duration: 2000,
							});
						}
						return;
					},
				});
			},
			fail: console.error,
		});
	},
	closeDialog() {
		this.setData({
			showWarnConfirm: false,
		});
	},
	async hanleRefundOrder(e) {
		this.closeDialog();
		wx.showToast({
			title: "退款中",
			mask: true,
			icon: "loading",
			duration: 2000,
		});
		const res = await this.cancelOrder(this.data.orderToRefund);
		console.debug("hanleRefundOrder res:", res);
		if (res.errMsg === "cloud.callFunction:ok") {
			await this.fetchOrders(this.data.status);
			wx.showToast({
				title: "退款成功",
				icon: "success",
				duration: 2000,
			});
		}
	},

	onCopy(e) {
		console.debug(e);
		wx.setClipboardData({
			data: e.currentTarget.dataset.orderNo,
			success(res) {
				wx.getClipboardData({
					success(res) {
						console.log(res.data); // data
					},
				});
				wx.showToast({
					title: "订单号已复制",
					icon: "success",
					duration: 2000,
				});
			},
		});
	},
});
