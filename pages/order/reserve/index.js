// pages/order/reserve/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    totalSeconds: 45, // 总倒计时秒数
    remainingSeconds: 45, // 剩余秒数
    isDisabled: true, // 按钮禁用状态
    timer: null, // 定时器
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.startCountdown();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    this.clearTimer();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 开始倒计时
  startCountdown() {
    this.setData({
      isDisabled: true,
      remainingSeconds: this.data.totalSeconds
    });

    // 设置定时器（每秒更新一次）
    const timer = setInterval(() => {
      const seconds = this.data.remainingSeconds - 1;
      
      if (seconds <= 0) {
        this.clearTimer();
        this.setData({
          isDisabled: false,
          remainingSeconds: 0
        });
        return;
      }

      this.setData({ remainingSeconds: seconds });
    }, 1000);

    this.setData({ timer });
  },

  // 清除定时器
  clearTimer() {
    if (this.data.timer) {
      clearInterval(this.data.timer);
      this.setData({ timer: null });
    }
  },

  goToOrders() {
    if(this.data.isDisabled) {
      wx.showToast({
        title: "请读一下先",
        duration: 1000,
      })
      return;
    }
    wx.redirectTo({
      url: '/pages/order/list/index'
    })
  }
})