.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.mt40 {
  margin-top: 80rpx;
}

.mb40 {
  margin-bottom: 40rpx;
}

.title {
  color: red;
  font-size: 40rpx;
  margin-top: 280rpx;
}


.rule-item {
  font-size: 22rpx;
  line-height: 40rpx;
  margin-top: 10rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
}

.btn-view-orders {
  margin-top: 60rpx;
  color: #fa4126;
  background: transparent;
  border: 1rpx solid #fa4126;
  padding: 10rpx 36rpx;
  border-radius: 16rpx;
}

.disabled {
  background-color: #c4c3c3;
  color: #fff;
  border: none;
}