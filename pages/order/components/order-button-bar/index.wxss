:host {
  width: 100%;
}
.btn-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 1;
}
.btn-bar .order-btn {
  line-height: 1;
  /* border-radius: unset; */
  /* min-width: 160rpx; */
}

.btn-bar .right {
  display: flex;
  align-items: center;
}
.btn-bar .t-button {
  width: 160rpx;
  font-weight: 400;
  margin-left: 24rpx;
}
.btn-bar .t-button--max {
  width: 176rpx;
  margin-left: 24rpx;

  --td-button-extra-small-height: 72rpx;
}

.btn-bar .left .delete-btn {
  font-size: 22rpx;
}
.btn-bar .left .delete-btn::after {
  display: none;
}

.btn-bar .right .normal {
  --td-button-default-color: #333333;
  --td-button-default-border-color: #dddddd;
}

.btn-bar .right .primary {
  --td-button-default-color: #fff;
  --td-button-default-bg-color: #fa4126;
  --td-button-default-border-color: #fa4126;
  --td-button-default-active-bg-color: #fa42269c;
}

.t-button {
  --td-button-default-color: #000;
  --td-button-primary-text-color: #fa4126;
}
