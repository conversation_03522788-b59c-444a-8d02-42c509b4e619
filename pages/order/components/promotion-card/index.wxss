.promotion-card {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding-left: 0;
  padding-bottom: 0;
}

.goods-thumb {
  width: 160rpx;
  height: 160rpx;
  flex-shrink: 0;
}

.thumb-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
}

.goods-info {
  flex: 1;
  min-width: 0;
}

.goods-title {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.store-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.promotion-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.promotion-tag {
  background-color: #FA4126;
  color: #fff;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.promotion-desc {
  color: #FA4126;
  font-size: 24rpx;
}

.order-info {
  margin-top: 12rpx;
  font-size: 24rpx;
  color: #666;
}

.order-requirements,
.participate-time {
  margin-bottom: 4rpx;
}

.hl {
  color: #FA4126;
}