// const { create } = require("../../fill-tracking-no/api");
import { formatPlatform, formatDateTime, formatOrderType } from "../../../../utils/util";
import dayjs from 'dayjs';

Component({
	properties: {
		order: {
			type: Object,
			observer(order) {
				// if (!order?.goodsList) return;
				// const goodsCount = order.goodsList.length;
				// this.setData({
				// 	goodsCount,
				// });
				if (!order) return;
				const storeName = order.promotion?.shop_id?.name || "";
				const productName = order.promotion?.product_id?.name || "";
				const productImage = order.promotion?.product_id?.images[0] || "";
				const promotionDesc = `付${order.paid_amount}返${order.refund_amount}` || "";
				const platform = formatPlatform(order.promotion?.platform);
				const createdAt = formatDateTime(order.createdAt);
				const paidAt = formatDateTime(order.paidAt);
				const verifiedAt = formatDateTime(order.verifiedAt);
				const refundedAt = formatDateTime(order.refundedAt);
				const auditedAt = formatDateTime(order.auditedAt);
        const orderType = formatOrderType(order.order_type);
        let validUntil = "";
        if(order.order_type === "tandian1") {
          validUntil = dayjs(order.createdAt)
                              .add(1, 'day')            // Move to next day
                              .endOf('day')
                              .format("YYYY-MM-DD HH:mm:ss") + "前";   
        }else if (order.order_type === "tandian2") {
          validUntil =  "活动下架前"
        } else if (order.order_type === "ykj") {
					validUntil =  dayjs(order.createdAt)
						.add(29, 'day')            // Move to next day
						.endOf('day')
						.format("YYYY-MM-DD HH:mm:ss") + "前"; 
				} else {
          validUntil = "未知"
        }
				let promotion = order.promotion;
        
				this.setData({
					storeName,
					productName,
					productImage,
					promotionDesc,
					platform,
					createdAt,
					paidAt,
					verifiedAt,
					refundedAt,
					auditedAt,
          orderType,
          validUntil,
					promotion
				});
			},
		},
		// promotion: {
    //   type: Object,  // 指定为对象类型
		// 	value: {}      // 提供默认空对象
		// },
		productImage: {
			type: String,
			value: "https://plachold.co/100x100",
		},
		promotionId: {
			type: String,
			value: "",
		},
		promotionType: {
			type: String,
			value: "",
		},
		promotionDesc: {
			type: String,
			value: "付0返0",
		},
		thumb: {
			type: String,
			value: "",
		},
		productName: {
			type: String,
			value: "",
		},
		storeName: {
			type: String,
			value: "",
		},
		platform: {
			type: String,
			value: "",
		},
		participateTime: {
			type: String,
			value: "",
		},
		createdAt: {
			type: String,
			value: "",
		},
		paidAt: {
			type: String,
			value: "",
		},
		verifiedAt: {
			type: String,
			value: "",
		},
		refundedAt: {
			type: String,
			value: "",
		},
		auditedAt: {
			type: String,
			value: "",
		},
	},
});
