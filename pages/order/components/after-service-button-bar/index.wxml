<view class="btn-bar">
  <view class="left">
    <t-button
      wx:for="{{buttons.left}}"
      wx:key="type"
      wx:for-item="leftBtn"
      size="extra-small"
      shape="round"
      t-class="order-btn delete-btn"
      catchtap="onServiceBtnTap"
      data-type="{{leftBtn.type}}"
    >
      {{leftBtn.name}}
    </t-button>
  </view>
  <view class="right">
    <t-button
      wx:for="{{buttons.right}}"
      wx:key="type"
      wx:for-item="rightBtn"
      size="extra-small"
      variant="{{ rightBtn.primary ? 'base' : 'outline'}}"
      shape="round"
      t-class="order-btn {{rightBtn.primary ? 'primary' : 'normal'}}"
      catchtap="onServiceBtnTap"
      data-type="{{rightBtn.type}}"
      open-type="{{ rightBtn.openType }}"
      data-share="{{ rightBtn.dataShare }}"
    >
      {{rightBtn.name}}
    </t-button>
  </view>
</view>
