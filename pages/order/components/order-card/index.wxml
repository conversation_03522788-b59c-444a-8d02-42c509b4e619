<wxs module="filter" src="../../../../utils/util.wxs"></wxs>

<view class="order-card wr-class" bind:tap="onOrderCardTap">
	<view class="header header-class">
		<view class="store-name title-class">
			<block wx:if="{{!useLogoSlot}}">
				<t-image wx:if="{{order.promotion_id}}" t-class="store-name__logo" src="{{order.promotion_id}}" />
				<t-icon wx:else prefix="wr" class="store-name__logo" name="store" size="inherit" color="inherit" />
				<view class="store-name__label">{{order.storeName}}</view>
			</block>
			<slot wx:else name="top-left" />
		</view>
		<view wx:if="{{!useTopRightSlot}}" class="order-status">{{filter.formatOrderStatus(order.status)}}</view>
		<slot wx:else name="top-right" />
		<!-- {{filter.formatOrderStatus(order.status)}} -->
		<!-- <view class="payment-countdown" wx:if="{{order.status === '0'}}">
			<t-count-down time="{{ 5 * 60 * 1000 }}" />
		</view> -->
	</view>
	<view class="slot-wrapper">
		<slot />
	</view>
	<!-- <view wx:if="{{goodsCount > defaultShowNum && !showAll}}" class="more-mask" catchtap="onShowMoreTap">
		展开商品信息（共 {{goodsCount}} 个）
		<t-icon name="chevron-down" size="32rpx" />
	</view> -->
	<slot name="more" />
</view>