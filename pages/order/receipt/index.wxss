@import '../../../style/theme.wxss';

.receipt {
  height: 100vh;
  background: #f5f5f5;
  position: relative;
  padding-top: 20rpx;

  --td-input-vertical-padding: 0;
}

.receipt-cell .t-cell__title {
  width: 144rpx;
  padding-right: 32rpx;
  flex: none !important;
}

.receipt .t-input__wrapper {
  margin: 0 !important;
}
.srcoll-view-wrap {
  margin-top: 20rpx;
}
.receipt .flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.receipt .head-title {
  color: #333;
  font-size: 30rpx;
  font-weight: bold;
}
.receipt .btn-wrap {
  display: flex;
}
.receipt .btn-wrap .btn {
  width: 128rpx;
  background: #f5f5f5;
  font-size: 24rpx;
  color: #333;
  margin-right: 22rpx;
  text-align: center;
  border-radius: 8rpx;
  position: relative;
  border: 2rpx solid #f5f5f5;
}
.receipt .btn-wrap .active-btn {
  background-color: transparent;
  border-color: #fa4126;
  color: #fa4126;
}
.receipt .title {
  width: 100%;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.receipt .receipt-label {
  display: flex;
}
.receipt .receipt-label .btn {
  width: 128rpx;
  background: #f5f5f5;
  font-size: 24rpx;
  color: #333;
  margin-left: 22rpx;
  text-align: center;
  border-radius: 8rpx;
  border: 2rpx solid #f5f5f5;
}
.receipt .receipt-label .active-btn {
  background-color: transparent;
  border-color: #fa4126;
  color: #fa4126;
}
.receipt .receipt-label .wr-cell__title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}
.receipt .receipt-content {
  background: #fff;
  margin-top: 20rpx;
}
.receipt .receipt-content .addressTags {
  padding: 0 30rpx;
  height: 100rpx;
}
.receipt .receipt-content .addressTags .btn-wrap {
  display: flex;
}
.receipt .receipt-content .line {
  width: 720rpx;
  margin-left: 30rpx;
  background-color: #e6e6e6;
  height: 1rpx;
}
.receipt .receipt-content .receipt-input {
  display: flex;
  padding: 0 30rpx;
  align-items: center;
  height: 100rpx;
  color: #666;
}
.receipt .receipt-content .receipt-input .title {
  color: #333;
  display: inline-block;
  width: 140rpx;
  margin-right: 30rpx;
  font-size: 30rpx;
  font-weight: bold;
}
.input-com {
  display: inline-block;
  flex: 1;
  font-size: 30rpx;
  font-weight: 400;
  line-height: 30rpx;
  padding: 0 !important;
  color: #666;
}
.input-com::after {
  border: none !important;
}

.receipt .receipt-content .receipt-input .wr-icon {
  font-size: 28rpx !important;
  margin-left: 20rpx;
}
.receipt .receipt-info {
  background: #fff;
  margin-top: 20rpx;
}
.receipt .receipt-info .info-con {
  padding: 0 30rpx;
  height: 100rpx;
}
.receipt .receipt-info .title {
  font-size: 24rpx;
  color: #999999;
  line-height: 36rpx;
  padding: 0 30rpx 20rpx;
  box-sizing: border-box;
}
.receipt .receipt-know {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  font-weight: 400;
  color: #999999;
  padding: 20rpx 30rpx;
  line-height: 26rpx;
}
.receipt .receipt-know .icon {
  margin-left: 16rpx;
  font-size: 26rpx;
}
.receipt .dialog-receipt .dialog__message {
  padding: 0;
}
.receipt .dialog-receipt .dialog-info {
  max-height: 622rpx;
}
.receipt .dialog-receipt .info-wrap {
  padding: 0 18rpx;
}
.receipt .dialog-receipt .info .title {
  display: inline-block;
  font-size: 28rpx;
  font-weight: 400;
  color: #999;
  line-height: 40rpx;
  margin-bottom: 40rpx;
  text-align: left;
}
.receipt .receipt-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  width: 100%;
  padding: 0 20rpx;
  box-sizing: border-box;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}
.receipt .receipt-btn .receipt-btn-con {
  margin-top: 20rpx;
  display: inline-block;
  width: 100%;
  line-height: 80rpx;
  background: #fa4126;
  text-align: center;
  color: #fff;
  border-radius: 48rpx;
}

.cell-left {
  margin-right: 0 !important;
}

.cell-right {
  display: flex;
  justify-content: flex-start;
  width: 480rpx;
}

.addressTagsIndex-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.t-button {
  --td-button-default-color: #000;
  --td-button-primary-text-color: #fa4126;
}
