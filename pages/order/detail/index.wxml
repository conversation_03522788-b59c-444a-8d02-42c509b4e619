<!-- <import src="./templates/1.wxml" />
<import src="./templates/2.wxml" />
<import src="./templates/3.wxml" /> 
-->

<wxs module="filter" src="../../../utils/util.wxs"></wxs>

<view class="block">
  <t-navbar t-class-placeholder="t-navbar-placeholder" t-class-content="t-navbar-content" class="block" title="订单详情"
    left-arrow bind:go-back="handleBack" />
</view>

<t-pull-down-refresh value="{{enable}}" loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}" usingCustomNavbar
  bind:refresh="onPullDownRefresh">

  <view class="order-detail" wx:if="{{!miniProgram.version}}">
    <t-notice-bar visible="{{true}}" prefixIcon="{{false}}" content="禁止给商家美团大众点评写评价，发现拉黑，有问题请先联系客服，我们响应很快的"
      theme="error" marquee>
      <t-icon slot="prefix-icon" name="error-circle-filled"></t-icon>
      <!-- <t-icon slot="suffix-icon" name="close" size="44rpx"></t-icon> -->
    </t-notice-bar>


    <!-- <view class="current-step">
    <text>订单详情</text>
  </view> -->

    <!-- 订单信息卡片 -->
    <view class="card" style="position: relative;">
      <!-- <view class="order-no">订单号： {{order.order_no}}</view> -->
      <view class="order-status">{{filter.formatOrderStatus(order.status)}}</view>
      <!-- 商品图片和店铺信息 -->
      <view class="order-header">
        <view class="product-image">
          <t-image class="food-image" src="{{product.images[0]}}" mode="aspectFill" width="120rpx" height="120rpx" />
        </view>
        <view class="store-info-main">
          <view class="store-icon-name">
            <!-- <t-icon name="shop" size="36rpx" class="store-icon" prefix="wr" /> -->
            <text class="store-name-text">{{shop.name }}</text>
          </view>
          <view class="store-title">{{order.promotion_id[0].name}}</view>
        </view>
      </view>

      <!-- 活动标签 -->
      <view class="tags-container" wx:if="{{order.order_type != 'ykj'}}">
        <text class="tag-highlight">{{storeInfo.payType || '先付后返'}}</text>
        <text class="tag-activity">付{{order.total_amount}}返{{order.refund_amount}}</text>
        <text class="tag-highlight"
          wx:if="{{!miniProgram.version}}">{{filter.formatPlatform(order.promotion_id[0].platform)}}笔记</text>
      </view>

      <!-- 地址和电话 -->
      <view class="address-phone-container">
        <view class="store-address">
          <t-icon name="location" size="32rpx" class="address-icon" />
          <text class="address-text">{{shop.address }}</text>
        </view>
        <view class="navigate-btn" bindtap="onNavigateTap">
          <t-icon prefix="wr" name="navigation" size="32rpx" class="address-icon" />
          <text>导航</text>
        </view>
        <view class="telephone-btn" bindtap="onPhoneTap">
          <t-icon prefix="wr" name="telephone" size="32rpx" class="phone-icon" />
          <text>电话</text>
        </view>
      </view>

      <view class="section-title">订单信息</view>
      <view style="font-size: 26rpx;">
        <view>订单号：{{order.order_no}}</view>
        <view class="participate-time hl">订单类型：{{orderType}}</view>
        <view wx:if="{{order.createdAt}}">下单时间：{{createdAt}}</view>
        <view class="participate-time">订单有效期至：{{validUntil}}</view>
        <view wx:if="{{order.paidAt}}">支付时间：{{paidAt}}</view>
        <view wx:if="{{order.refundedAt}}">取消时间：{{refundedAt}}</view>
        <view wx:if="{{order.verifiedAt}}">核销时间：{{order.verifiedAt}}</view>
      </view>

      <!-- 套餐详情 -->
      <view class="section-title">商品详情</view>
      <view class="product-detail {{isShowMore ? 'expanded' : ''}}" style="font-size: 26rpx;">
        <rich-text nodes="{{product.detail}}"></rich-text>
      </view>

      <view class="show-more" bindtap="toggleShowMore" wx:if="{{showMoreBtn}}">
        {{isShowMore ? '收起' : '展开全部'}}
        <t-icon name="{{isShowMore ? 'chevron-up' : 'chevron-down'}}" size="32rpx" />
      </view>
    </view>

    <tandian-order-detail order="{{order}}" promotion="{{promotion}}" wx:if="{{ promotion.promotion_type != 'ykj'}}"
      bind:showVerifyDialog="showVerifyDialog" />
    <ykj-order-detail order="{{order}}" promotion="{{promotion}}" wx:if="{{ promotion.promotion_type === 'ykj'}}"
      bind:showVerifyDialog="showVerifyDialog" />

    <!-- 底部流程说明 -->
    <!-- <view class="order-flow">
    <view class="flow-step">
      <view class="flow-number">1</view>
      <text>到店体验，锁定名额</text>
      <t-icon name="chevron-right" size="32rpx" />
    </view>
    <view class="flow-step">
      <view class="flow-number">2</view>
      <text>上传笔记，微信到账</text>
    </view>
  </view> -->

    <!-- <template is="t1" wx:if="{{promotion.promotion_type === 'tandian1'}}"
      data="{{miniProgram: miniProgram, order: order, promotion: promotion, note: note}}" /> -->
    <!-- <template is="t2" wx:if="{{promotion.promotion_type === 'tandian2'}}"
      data="{{miniProgram: miniProgram, order: order, promotion: promotion, note: note}}" /> -->
    <!-- <template is="t3" wx:if="{{promotion.promotion_type === 'ykj'}}"
      data="{{miniProgram: miniProgram, order: order, promotion: promotion, note: note}}" /> -->
  </view>

  <t-toast id="t-toast" />
  <t-dialog id="t-dialog" />
  <t-message id="t-message" />

  <!-- 笔记要求弹窗 -->
  <t-popup visible="{{isNotePopupShow}}" placement="bottom" bind:visible-change="handleNotePopupHide">
    <view class="note-popup-container">
      <view class="note-popup-header">
        <view class="note-popup-title">笔记要求</view>
        <view class="note-popup-close" bindtap="handleNotePopupHide">
          <t-icon name="close" size="36rpx" />
        </view>
      </view>
      <view class="note-popup-content">
        <view class="note-item">
          <view class="note-title">温馨提示</view>
          <view class="note-detail highlight">图片是重点，可参考门店大众点评或小红书优质笔记</view>
        </view>
        <view class="note-item">
          <view class="note-title">平台要求</view>
          <view class="note-detail">
            {{filter.formatPlatform(promotion.platform)}}，{{filter.formatRequirements(promotion.requires,
            promotion.level, promotion.followers_count)}}</view>
        </view>
        <view class="note-item">
          <view class="note-title">图片要求</view>
          <view class="note-detail">1. 图片≥{{promotion.images_count || 9}}张，尺寸需3:4竖图</view>
          <view class="note-detail">2. 封面图高清，带滤镜，其它图片需清晰好看</view>
          <view class="note-detail">3. 建议包含产品整体、产品特写和店铺环境等</view>
          <view class="note-detail highlight">4. 不要不经思考随手一拍，不要菜没齐空桌拍菜品，不要空场拍环境显冷清</view>
        </view>
        <view class="note-item">
          <view class="note-title">文案要求</view>
          <view class="note-detail">1. 字数≥{{promotion.text_count || 100}}字</view>
          <view class="note-detail">2. 分享真实消费体验; 建议从个人感受、店铺环境、餐品口味、服务态度等方面展开</view>
        </view>
        <view class="note-item">
          <view class="note-title">门店定位</view>
          <view class="flex items-center justify-between">
            <view class="note-detail">{{shop.name}}</view>
            <view class="copy-btn" data-copy-content="{{shop.name}}" catchtap="onCopyShopName">复制店名</view>
          </view>
        </view>
        <view class="note-item" wx:if="{{promotion.topics}}">
          <view class="note-title">话题标签
            <view class="note-title-tips">(请务必在文案底部加上标签)</view>
            <view class="copy-btn" data-copy-content="{{promotion.topics}}" catchtap="onCopyTopics">复制话题</view>
          </view>
          <view class="flex items-center justify-between">
            <view class="note-detail">{{promotion.topics}} <view class="note-topic-tips">再加2-3个热门标签</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </t-popup>

  <!-- 核销确认弹窗 -->
  <t-dialog t-class="wrapper" visible="{{verifyDialogVisible}}" title="你到店了吗？" content="请让店员点击或者当着店员的面点击哈，点确定会核销订单的哦"
    confirm-btn="{{ {content: '确定', variant: 'base' } }}" cancel-btn="取消" bind:confirm="onVerifyOrder"
    bind:cancel="closeVerifyDialog">
  </t-dialog>
</t-pull-down-refresh>