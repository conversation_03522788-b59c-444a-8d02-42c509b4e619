page {
	background-color: #f5f5f5;
}

.order-detail {
	padding-bottom: 120rpx;
}

/* 头部样式 */
.header {
	background-color: #ffeeb2;
	padding: 20rpx 30rpx;
	margin-bottom: 20rpx;
	position: relative;
	height: 160rpx;
}

.order-detail__header {
	padding: 30rpx 0;
}

.order-detail__header .title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

/* 卡片通用样式 */
.card {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin: 24rpx 24rpx 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

/* 订单头部 */
.order-header {
	display: flex;
	margin-bottom: 16rpx;
}
.product-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 8rpx;
	margin-right: 16rpx;
}

.food-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 8rpx;
	margin-right: 16rpx;
}

.store-info-main {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.store-icon-name {
	display: flex;
	align-items: center;
	margin-bottom: 4rpx;
}

.store-icon {
	margin-right: 8rpx;
	color: #333;
}

.store-name-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	width: 80%;
}

.store-title {
	font-size: 28rpx;
	/* color: #666; */
}

/* 标签容器 */
.tags-container {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 16rpx;
}

.tag-highlight,
.tag-activity {
	font-size: 24rpx;
	color: #ff9d00;
	background-color: #fff7e6;
	padding: 6rpx 20rpx;
	border-radius: 30rpx;
	margin-right: 16rpx;
	margin-bottom: 12rpx;
}

.tag-comment {
	font-size: 24rpx;
	color: #333;
	padding: 6rpx 20rpx;
	border-radius: 30rpx;
	border: 1px solid #eee;
	margin-right: 16rpx;
}

/* 店铺提醒 */
.store-notice {
	display: flex;
	align-items: flex-start;
	font-size: 26rpx;
	color: #666;
	margin-bottom: 24rpx;
	background-color: #f8f8f8;
	padding: 16rpx;
	border-radius: 8rpx;
}

.notice-icon {
	margin-right: 12rpx;
	flex-shrink: 0;
	margin-top: 4rpx;
}

.notice-text {
	flex: 1;
	line-height: 1.4;
}

/* 地址电话容器 */
.address-phone-container {
	margin-top: 24rpx;
	display: flex;
	justify-content: space-between;
}

.store-address {
	display: flex;
	align-items: center;
	font-size: 26rpx;
	color: #666;
	margin-bottom: 20rpx;
	max-width: 500rpx;
}

.address-icon {
	margin-right: 12rpx;
	flex-shrink: 0;
	color: #666;
}

.address-text {
	margin-right: 16rpx;
	width: 400rpx;
}

.navigate-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	font-size: 26rpx;
	margin-left: auto;
	margin-right: 30rpx;
}

.telephone-btn {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	font-size: 26rpx;
	margin-left: auto;
	margin-right: 20rpx;
}

.nav-icon,
.phone-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 8rpx;
}

.divider {
	height: 1px;
	background-color: #eee;
	margin: 24rpx 0;
}

.store-phone {
	display: flex;
	justify-content: flex-end;
}

.phone-btn {
	display: flex;
	align-items: center;
	color: #0052d9;
	font-size: 26rpx;
}

/* 套餐详情卡片 */
.meal-card {
	margin-top: 24rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-top: 24rpx;
	margin-bottom: 24rpx;
	/* color: #666; */
}

.product-detail {
	max-height: 200rpx;
	overflow: hidden;
	transition: max-height 0.3s ease-in-out;
	/* color: #666; */
}

.product-detail.expanded {
	max-height: none;
}

.meal-section {
	margin-bottom: 20rpx;
	overflow: hidden;
	transition: all 0.3s ease-in-out;
	transform-origin: top;
	opacity: 1;
	max-height: 200rpx;
}

.meal-section.expanded {
	max-height: 800rpx;
}

/* .step-detail {
	overflow: hidden;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	max-height: 0;
	opacity: 0;
	transform: translateY(-10rpx);
} */

.step-detail.expanded {
	max-height: 1500rpx;
	opacity: 1;
	transform: translateY(0);
}

.order-status {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	background-color: #ff5f15;
	color: white;
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 8rpx;
}

.show-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx;
	color: #666;
	font-size: 28rpx;
}

.show-more t-icon {
	margin-left: 8rpx;
}

.meal-name {
	font-size: 28rpx;
	font-weight: bold;
	margin-bottom: 16rpx;
	color: #333;
}

.meal-items {
	margin-bottom: 24rpx;
}

.meal-item {
	display: flex;
	font-size: 28rpx;
	margin-bottom: 12rpx;
	align-items: center;
}

.item-name {
	flex: 1;
	color: #666;
}

.item-quantity {
	width: 80rpx;
	text-align: center;
	color: #666;
}

.item-price {
	width: 100rpx;
	text-align: right;
	color: #666;
}

.show-more {
	text-align: center;
	color: #666;
	font-size: 26rpx;
	padding: 12rpx 0;
}

/* 当前步骤标题 */
.current-step {
	font-size: 32rpx;
	font-weight: bold;
	margin: 30rpx 24rpx 16rpx;
	color: #333;
}

/* 步骤卡片 */
.steps-card {
	padding: 0;
	overflow: hidden;
}

.step-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx;
	background-color: #fff;
	position: relative;
}

.step-left {
	display: flex;
	align-items: center;
}

.step-number {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background-color: #ff9d00;
	color: #fff;
	font-size: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
}

.step-desc {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.step-expand {
	color: #999;
}

.step-detail {
	/* padding: 0 24rpx 24rpx; */
	background-color: #f9f9f9;
	overflow: hidden;
	max-height: 0;
	transition: max-height 0.3s ease-out;
}

.step-detail.expanded {
	max-height: fit-content;
}

/* 笔记上传表单样式 */
.note-upload-form {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 24rpx;
}

.platform-header {
	display: flex;
	align-items: center;
	padding-bottom: 20rpx;
	border-bottom: 1px solid #f5f5f5;
	margin-bottom: 20rpx;
}

.platform-logo {
	width: 48rpx;
	height: 48rpx;
	margin-right: 12rpx;
}

.platform-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-left: 20rpx;
}

.user-profile {
	display: flex;
	align-items: center;
	padding: 16rpx 0;
	margin-bottom: 20rpx;
}

.user-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 16rpx;
	border: 1px dashed #ffb800;
}

.user-info {
	display: flex;
	flex-direction: column;
}

.user-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 4rpx;
}

.user-level {
	display: inline-block;
	font-size: 22rpx;
	color: #fff;
	background-color: #ffb800;
	padding: 2rpx 12rpx;
	border-radius: 20rpx;
	width: fit-content;
}

.form-item {
	margin-bottom: 24rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 12rpx;
	font-weight: 500;
}

.form-input-container {
	margin-bottom: 16rpx;
}

.rating-container {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.rating-label {
	font-size: 26rpx;
	color: #666;
	margin-right: 16rpx;
}

.submit-btn {
	margin-top: 32rpx;
	width: 100%;
	background-color: #ffb800;
	color: #fff;
	font-weight: normal;
}

.step-notice {
	background-color: #fdf6ec;
	padding: 16rpx 10rpx;
	border-radius: 8rpx;
	display: flex;
	align-items: flex-start;
}

.step-notice .notice-text {
	flex: 1;
	font-size: 26rpx;
	color: #e6a23c;
	margin-left: 10rpx;
	line-height: 1.5;
}

.step-actions {
	margin-top: 20rpx;
	margin-bottom: 30rpx;
	padding: 0 24rpx;
}

.action-item {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;
}

.action-number {
	font-size: 28rpx;
	font-weight: bold;
	margin-right: 10rpx;
	color: #333;
}

.action-desc {
	font-size: 28rpx;
	color: #333;
}

.action-status {
	font-size: 26rpx;
	color: #19be6b;
	margin-left: 30rpx;
	margin-bottom: 24rpx;
}

.action-hint {
	font-size: 26rpx;
	color: #666;
	margin-left: 30rpx;
	margin-bottom: 24rpx;
}

.action-warning {
	font-size: 26rpx;
	color: #f56c6c;
	line-height: 1.5;
	margin-bottom: 30rpx;
	background-color: #fef0f0;
	padding: 16rpx 10rpx;
	border-radius: 8rpx;
}

.qrcode-container {
	margin-top: 24rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.qrcode {
	width: 400rpx;
	height: 400rpx;
	margin: 0 auto;
	display: block;
	margin-bottom: 24rpx;
}

.verify-btn {
	width: 90% !important;
	margin-top: 16rpx !important;
	color: #fff !important;
	border-radius: 8rpx !important;
}

/* 底部流程 */
.order-flow {
	display: flex;
	justify-content: center;
	margin: 30rpx 24rpx;
	padding: 20rpx;
	background-color: #fff;
	border-radius: 16rpx;
}

.flow-step {
	display: flex;
	align-items: center;
	font-size: 26rpx;
	color: #666;
	margin: 0 16rpx;
}

.flow-number {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background-color: #e53935;
	color: #fff;
	font-size: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 10rpx;
}

.countdown {
	display: flex;
	margin-left: 22rpx;
	align-items: center;
	gap: 10rpx;
}

.countdown .countdown-label {
	font-size: 24rpx;
	color: #ff6000;
}

.t-textarea {
	height: 200rpx;
}

.step-status {
	color: #ff6000;
	margin-left: 80rpx;
	background: red;
	color: #fff;
	padding: 2rpx 20rpx;
	font-size: 24rpx;
	border-radius: 8rpx;
}

.hide {
	display: none;
}

/* 使用须知弹出层样式 */
.usage-popup-container,
.note-popup-container {
	background-color: #fff;
	border-radius: 24rpx 24rpx 0 0;
	padding: 32rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.usage-popup-header,
.note-popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
}

.usage-popup-title,
.note-popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.usage-popup-close,
.note-popup-close {
	padding: 8rpx;
}

.usage-popup-content,
.note-popup-content {
	color: #666;
	font-size: 28rpx;
}

.usage-item,
.note-item {
	margin-bottom: 24rpx;
}

.usage-title,
.note-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 16rpx;
}

.usage-detail,
.note-detail {
	margin-bottom: 12rpx;
	line-height: 1.6;
	display: flex;
	width: 90%;
}

.copy-btn {
	display: flex;
	margin-left: auto;
	list-style: 1.6;
	margin-bottom: 12rpx;
	color: #ff5f15;
	min-width: 100rpx;
	float: right;
	font-weight: 400;
	font-size: 24rpx;
}

.note-requirements {
	display: flex;
	margin-left: auto;
	color: #ff5f15;
}

.flex {
	display: flex;
}
.items-center {
	align-items: center;
}
.justify-between {
	justify-content: space-between;
}

.verify-status-container {
	margin-top: 60rpx;
}

.verify-status-title {
	font-size: 32rpx;
}

.verify-status-desc {
	margin-top: 20rpx;
	font-size: 28rpx;
	/* color: #FF6000; */
	color: red;
}

.verify-status-reason {
	margin-top: 20rpx;
	font-size: 28rpx;
}

.verify-status-tips {
	margin-top: 20rpx;
	font-size: 28rpx;
	color: red;
}

.status-text {
	margin-left: 10rpx;
	margin-bottom: 40rpx;
	font-size: 28rpx;
	color: #ff6000;
	padding-left: 30rpx;
	display: flex;
}

.order-no {
	color: #ff5f15;
	margin-bottom: 16rpx;
}

.note-title-tips {
	display: inline;
	color: #fa4126;
}
.note-topic-tips {
	display: inline-flex;
	color: #fa4126;
	min-width: 230rpx;
}
.highlight {
	color: #fa4126;
}
