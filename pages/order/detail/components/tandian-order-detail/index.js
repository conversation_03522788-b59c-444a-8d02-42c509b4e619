// pages/order/detail/components/tandian-order-detail/index.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    order: {
      type: Object,
      value: {}
    },
    promotion: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isStepExpanded: false, // 控制第一步展开/收起
		isSecondStepExpanded: false, // 控制第二步展开/收起
  },

  lifetimes: {
    // 生命周期函数，可以为函数，或一个在methods段中定义的方法名
    attached: function () { 
      console.error("tandian order detail component attached");
    },
    moved: function () { },
    detached: function () { },
  },

  // 生命周期函数，可以为函数，或一个在methods段中定义的方法名
  attached: function () { }, // 此处attached的声明会被lifetimes字段中的声明覆盖
  ready: function() { },

  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: function () { },
    hide: function () { },
    resize: function () { },
  },

    /**
   * 组件的方法列表
   */
    methods: {
      expandStep() {
        // 切换展开/收起状态
        this.setData({
          isStepExpanded: !this.data.isStepExpanded,
        });
    
        // 添加轻微震动反馈
        wx.vibrateShort({
          type: "light",
        });
      },
  
      expandSecondStep() {
        // 切换第二步展开/收起状态
        this.setData({
          isSecondStepExpanded: !this.data.isSecondStepExpanded,
        });
  
        // 添加轻微震动反馈
        wx.vibrateShort({
          type: "light",
        });
      },

      showVerifyDialog() {
        this.triggerEvent('showVerifyDialog')
      }
  
    },
})