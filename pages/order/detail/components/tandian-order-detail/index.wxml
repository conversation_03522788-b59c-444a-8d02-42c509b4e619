<wxs module="filter" src="../../../../../utils/util.wxs"></wxs>

<block>
  <!-- 当前步骤区域 -->
  <view class="current-step" wx:if="{{ !miniProgram.version && order.status != '40' }}">
    <text>步骤说明</text>
  </view>

  <!-- 第一步： 到店核销 -->
  <view class="card steps-card" wx:if="{{ !miniProgram.version && order.status != '40' }}">
    <block wx:if="{{order.status === '10'}}">
      <view class="step-item active" bindtap="expandStep">
        <view class="step-left">
          <view class="step-number">1</view>
          <view class="step-desc">到店体验，锁定名额</view>
          <!-- <view wx:if="{{order.status === '0'}}" class="countdown">
          <view class="countdown-label">离结束</view>
          <t-count-down time="{{ 5 * 60 * 1000 }}" />
        </view> -->
          <view wx:if="{{status1}}" class="step-status">
            {{status1}}
          </view>
        </view>
        <t-icon name="{{isStepExpanded ? 'chevron-up' : 'chevron-down'}}" size="32rpx" class="step-expand" />
      </view>

      <view class="step-detail {{isStepExpanded ? 'expanded' : ''}}"
        style="{{!isStepExpanded ? 'display: none;' : ''}}">
        <view class="step-notice">
          <t-icon name="info-circle" size="36rpx" color="#E6A23C" />
          <text class="notice-text">请在{{expiryDate}}前到店核销，超时将自动退款，并将影响下一次活动报名</text>
        </view>
        <view class="step-notice">
          <t-icon name="info-circle" size="36rpx" color="#E6A23C" />
          <text class="notice-text">平台禁代探店、转让探店码、非外带店直接打包等消极探店行为，一经发现关小黑屋处理！</text>
        </view>
        <view class="step-actions">
          <view class="action-item">
            <text class="action-number">1.</text>
            <text class="action-desc">购买套餐，获得核销码</text>
          </view>
          <view class="action-status">活动套餐已购买，请到店体验</view>

          <view class="action-item">
            <text class="action-number">2.</text>
            <text class="action-desc">到店体验，完成核销</text>
          </view>
          <view class="action-hint">请先联系商家确认营业再前往核销</view>
          <view class="action-hint">到店报暗号：“优探生活”或者“优探生活探店”；</view>
          <view class="action-hint">到店后主动出示订单，并让店员点击“立即核销”并确认，订单核销后请认真拍摄笔记所需照片。</view>

          <view class="qrcode-container" wx:if="{{ order.status === '10' }}">
            <!-- <t-image class="qrcode" src="{{order.qrcode}}" mode="aspectFit" alt="二维码" loading="slot" width="200" height="200">
            <t-loading slot="loading" theme="spinner" size="400rpx" loading />
          </t-image> -->
            <t-button t-class="verify-btn" theme="primary" size="large" bindtap="showVerifyDialog">点击核销</t-button>
          </view>

          <!-- <view wx:if="{{order.status === '0'}}">
          <t-button t-class="verify-btn" theme="primary" size="large" bindtap="onPayNow">立即支付</t-button>
        </view> -->
        </view>
      </view>
    </block>

    <block wx:if="{{order.status === '11'}}">
      <view class="step-item active" bindtap="expandStep">
        <view class="step-left">
          <view class="step-number">1</view>
          <view class="step-desc">到店体验，锁定名额</view>
          <!-- <view wx:if="{{order.status === '0'}}" class="countdown">
          <view class="countdown-label">离结束</view>
          <t-count-down time="{{ 5 * 60 * 1000 }}" />
        </view> -->
          <view wx:if="{{status1}}" class="step-status">
            {{status1}}
          </view>
        </view>
        <t-icon name="{{isStepExpanded ? 'chevron-up' : 'chevron-down'}}" size="32rpx" class="step-expand" />
      </view>

      <view class="step-detail {{isStepExpanded ? 'expanded' : ''}}"
        style="{{!isStepExpanded ? 'display: none;' : ''}}">
        <!-- 
      <view class="step-notice">
        <t-icon name="info-circle" size="36rpx" color="#E6A23C" />
        <text class="notice-text">请在{{expiryDate}}前到店核销，超时将自动退款，并将影响下一次活动报名</text>
      </view>
      <view class="step-notice">
        <t-icon name="info-circle" size="36rpx" color="#E6A23C" />
        <text class="notice-text">平台禁代探店、转让探店码、非外带店直接打包等消极探店行为，一经发现关小黑屋处理！</text>
      </view>
      -->
        <view class="step-actions">
          <!-- <view class="action-item">
          <text class="action-number">1.</text>
          <text class="action-desc">购买套餐，获得核销码</text>
        </view>
        <view class="action-status">活动套餐已购买，请到店体验</view>
        
        <view class="action-item">
          <text class="action-number">2.</text>
          <text class="action-desc">到店体验，完成核销</text>
        </view>
        <view class="action-hint">请先联系商家确认营业再前往核销</view>
        <view class="action-hint">到店报暗号：“优探生活”或者“优探生活探店”；</view>
        <view class="action-hint">到店后主动出示订单，并让店员点击“立即核销”并确认，订单核销后请认真拍摄笔记所需照片。</view> -->

          <view class="qrcode-container" wx:if="{{ order.status === '11' }}">
            <!-- <t-image class="qrcode" src="{{order.qrcode}}" mode="aspectFit" alt="二维码" loading="slot" width="200" height="200">
            <t-loading slot="loading" theme="spinner" size="400rpx" loading />
          </t-image> -->
            <t-button t-class="verify-btn" theme="primary" size="large" bindtap="hanleRefundOrder">申请退款</t-button>
          </view>

          <!-- <view wx:if="{{order.status === '0'}}">
          <t-button t-class="verify-btn" theme="primary" size="large" bindtap="onPayNow">立即支付</t-button>
        </view> -->
        </view>
      </view>
    </block>

    <block wx:if="{{ order.status > 11 }}">
      <view class="step-item active" bindtap="expandStep">
        <view class="step-left">
          <view class="step-number">1</view>
          <view class="step-desc">到店体验，锁定名额</view>
          <view wx:if="{{order.status === '0'}}" class="countdown">
            <view class="countdown-label">离结束</view>
            <t-count-down time="{{ 5 * 60 * 1000 }}" />
          </view>
          <view wx:if="{{status1}}" class="step-status">
            {{status1}}
          </view>
        </view>
        <t-icon name="{{isStepExpanded ? 'chevron-up' : 'chevron-down'}}" size="32rpx" class="step-expand" />
      </view>

      <view class="step-detail {{isStepExpanded ? 'expanded' : ''}}"
        style="{{!isStepExpanded ? 'display: none;' : ''}}">
        <text class="status-text">
          已于{{filter.formatDateTime(order.verifiedAt)}}到店核销
        </text>
      </view>
    </block>
  </view>

  <!-- 第二步： 上传作业 -->
  <view class="card steps-card" wx:if="{{ !miniProgram.version && order.status != '40' && order.status != '11' }}">
    <view class="step-item active" bindtap="expandSecondStep">
      <view class="step-left">
        <view class="step-number">2</view>
        <view class="step-desc">上传笔记，微信到账</view>
        <view wx:if="{{status2}}" class="step-status">
          {{status2}}
        </view>
      </view>
      <t-icon name="{{isSecondStepExpanded ? 'chevron-up' : 'chevron-down'}}" size="32rpx" class="step-expand" />
    </view>

    <view class="step-detail {{isSecondStepExpanded ? 'expanded' : ''}}"
      style="{{ !isSecondStepExpanded ? 'display: none;' : ''}}">
      <view class="step-notice">
        <t-icon name="info-circle" size="36rpx" color="#E6A23C" />
        <!-- <text class="notice-text">请在 {{expiryDate || '2025/03/20 08:00:00'}}内到店核销，超时将自动退款，并将影响下一次活动报名</text> -->
        <text class="notice-text">请在完成核销后3天内上传笔记，写笔记前请查看笔记要求，审核通过后将返现至余额，你可以提现至微信零钱</text>
      </view>

      <!-- 大众点评笔记上传表单 -->
      <view class="note-upload-form">
        <!-- 大众点评标题 -->
        <view class="platform-header">
          <t-image src="https://636c-cloud1-0gpy573m8caa7db3-**********.tcb.qcloud.la/app/dazhongdianping.svg"
            width="20" height="20" mode="aspectFill" style="display: flex;"
            wx:if="{{order.promotion_id[0].platform === 'dianping'}}" />
          <t-image src="https://636c-cloud1-0gpy573m8caa7db3-**********.tcb.qcloud.la/app/xiaohongshu.svg" width="20"
            height="20" mode="aspectFill" style="display: flex;"
            wx:if="{{order.promotion_id[0].platform === 'xiaohongshu'}}" />
          <text class="platform-title">{{filter.formatPlatform(order.promotion_id[0].platform)}}笔记</text>
          <view bind:tap="handleNotePopupShow" class="note-requirements">
            ⚠️ 查看笔记要求
          </view>
        </view>

        <!-- 用户信息 -->
        <!-- <view class="user-profile">
        <image class="user-avatar" src="{{verifiedCard.fileID}}" mode="aspectFill"></image>
        <view class="user-info">
          <text class="user-name">{{filter.formatPlatform(order.promotion_id[0].platform)}}</text>
          <view class="user-level">Lv{{order.promotion_id[0].level}}</view>
        </view>
      </view> -->

        <!-- 笔记链接 -->
        <view class="form-item">
          <view class="form-label">上传{{filter.formatPlatform(promotion.platform)}}链接</view>
          <view class="form-input-container">
            <!-- 审核中, 审核通过 -->
            <block wx:if="{{order.status === '30' || order.status === '31'}}">
              <t-input value="{{note.url}}" disabled />
            </block>
            <!-- 待上传和审核驳回 -->
            <block wx:else>
              <t-input placeholder="点击粘贴笔记链接" value="{{note.url}}" bind:change="onNoteLinkChange" suffix="{{'粘贴'}}"
                bind:click="onPasteLink" />
            </block>
          </view>
          <block wx:if="{{promotion.need_to_sync}}">
            <view class="form-label">上传同步的{{filter.formatReversePlatform(promotion.platform)}}笔记链接</view>
            <view class="form-input-container">
              <!-- 审核中, 审核通过 -->
              <block wx:if="{{order.status === '30' || order.status === '31'}}">
                <t-input value="{{note.sync_url}}" disabled />
              </block>
              <!-- 待上传和审核驳回 -->
              <block wx:else>
                <t-input placeholder="点击粘贴同步笔记链接" value="{{note.sync_url}}" bind:change="onNoteSyncLinkChange"
                  suffix="{{'粘贴'}}" bind:click="onPasteSyncLink" />
              </block>
            </view>
          </block>
        </view>


        <!-- 对商家的意见和建议 -->
        <view class="form-item">
          <view class="form-label">对商家的意见和建议</view>
          <view class="rating-container">
            <text class="rating-label">综合评价</text>
            <t-rate value="{{note.rating}}" bind:change="onRatingChange" size="24" gap="2" color="#FFC51C"
              disabled="{{uploaded}}" />
          </view>
          <t-textarea t-class="t-textarea" placeholder="如果有建议请提交建议，希望您给与鼓励的话语" value="{{note.feedback}}"
            bind:change="onFeedbackChange" maxlength="1000" disableDefaultPadding="{{true}}" autosize
            disabled="{{uploaded}}" />
        </view>

        <block wx:if="{{order.status === '32'}}"> <!-- 审核驳回 -->
          <t-button t-class="submit-btn" theme="primary" size="large" bindtap="onSubmitNote" disabled="{{uploaded}}"
            wx:if="{{note.status === 2}}">我已修改笔记，请重新审核吧</t-button>
        </block>

        <block wx:if="{{order.status === '31'}}"> <!-- 审核通过 -->
          <!-- <t-button t-class="submit-btn" theme="primary" size="large" bindtap="onSubmitNote" disabled="{{uploaded}}" wx:if="{{note.status === 2}}">重新提交</t-button> -->
        </block>

        <block wx:if="{{order.status === '30'}}"> <!-- 审核中 -->
          <t-button t-class="submit-btn" theme="primary" size="large" bindtap="onSubmitNote"
            disabled="{{true}}">请等待审核</t-button>
        </block>

        <block wx:if="{{order.status === '20'}}"> <!-- 待上传 -->
          <t-button t-class="submit-btn" theme="primary" size="large" bindtap="onSubmitNote">提交作业</t-button>
        </block>

        <!-- 提交按钮 -->
        <!-- <t-button t-class="submit-btn" theme="primary" size="large" bindtap="onSubmitNote" disabled="{{uploaded}}" wx:if="{{note.status != 1}}">{{uploaded ? '请等待审核' : '提交作业'}}</t-button> -->
        <!-- 还没核销 -->
        <!-- <t-button t-class="submit-btn" theme="primary" size="large" bindtap="onSubmitNote" disabled="{{order.status === '10'}}">提交作业</t-button>  -->
        <!-- 审核驳回 -->

        <view class="verify-status-container" wx:if="{{note.status === 2}}">
          <view class="verify-status-title">审核结果</view>
          <view class="verify-status-desc">状态：笔记已驳回</view>
          <view class="verify-status-desc">tips:</view>
          <view class="verify-status-tips">1. 请在对应的平台修改了以后，点一下上面的我已修改按钮，</view>
          <view class="verify-status-tips">2. 如果是在原笔记上修改的，不必更换这里的笔记链接，除非发错了账号了重写笔记</view>
          <view class="verify-status-tips">3. 如果不清楚要修改什么或者怎么修改，可以加运营审核微信了解一下</view>
          <view class="verify-status-reason">理由: {{note.remark}}</view>
          <view class="operator-wechat">
            <t-image src="https://636c-cloud1-0gpy573m8caa7db3-**********.tcb.qcloud.la/app/operator.jpg"
              mode="aspectFill" width="300" height="400" show-menu-by-longpress />
          </view>
        </view>

        <view class="verify-status-container" wx:if="{{note.status === 1}}">
          <view class="verify-status-title">审核结果：</view>
          <view class="verify-status-desc">笔记已审核通过</view>
        </view>
      </view>
    </view>
  </view>
</block>