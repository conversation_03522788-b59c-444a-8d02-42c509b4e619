import { createStoreBindings } from "mobx-miniprogram-bindings";
import {
	orderStore,
	productStore,
	cardStore,
	noteStore,
} from "../../../stores/index";
import { generateRandomFileName, getFileExtension } from "../../../utils/util";

import { formatPlatform, formatDateTime, formatOrderType } from "../../../utils/util";
import Toast from "tdesign-miniprogram/toast/index";
import Message from "tdesign-miniprogram/message/index";
import dayjs from "dayjs";

Page({
	data: {
		countDownTime: ********, // 24小时倒计时
		qrCodeUrl: "", // 二维码URL
	
		isShowMore: false, // 控制套餐详情展开/收起
		showMoreBtn: false, // 控制展开按钮的显示
		isSubmitting: false,
		isFirstStepFinished: false,
		isSecondStepFinished: false,
		uploaded: false,
	},

	async onLoad(options) {
		const { miniProgram } = wx.getAccountInfoSync();
		this.setData({
			miniProgram: miniProgram,
		});

		console.debug("options: ", options);
		// if (options.orderId) {
		//   this.setData({
		//     orderId: options.orderId
		//   });
		//   this.getOrderDetail(options.orderId);
		// }
		// // 生成临时二维码（实际应从后端获取）
		// this.generateQRCode();

		// 将 store 绑定到页面
		this.orderStoreBindings = createStoreBindings(this, {
			store: orderStore,
			fields: [
				"order",
				"shop",
				"product",
				"promotion",
				"whenVerificationSucceed",
			],
			actions: ["fetchOrderDetail", "verifyOrder", "cancelOrder"],
		});
		this.noteStoreBindings = createStoreBindings(this, {
			store: noteStore,
			fields: ["note"],
			actions: ["submitNote", "fetchNote"],
		});
		this.cardStoreBindings = createStoreBindings(this, {
			store: cardStore,
			fields: ["cards"],
			actions: ["fetchCards"],
		});

		if (options.id) {
			this.setData({
				id: options.id,
			});
		}
	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {
		// 解绑 store
		this.orderStoreBindings.destroyStoreBindings();
		this.noteStoreBindings.destroyStoreBindings();
		this.cardStoreBindings.destroyStoreBindings();
	},

	async init() {
		await this.fetchOrderDetail(this.data.id);
		await this.fetchOrderNote();
		await this.fetchOrderCard();

		await this.initStepStatus();
		await this.initExpandStatus();

		await this.formatOrderDetails();
		// 检查内容高度，决定是否显示展开按钮
		this.checkContentHeight();
	},

	checkContentHeight() {
		console.debug("checkContentHeight");
		const query = wx.createSelectorQuery();
		query
			.select(".product-detail")
			.boundingClientRect((rect) => {
				console.debug("rect", rect);
				if (rect && rect.height >= 50) {
					this.setData({
						showMoreBtn: true,
					});
				}
			})
			.exec();
	},

	async initStepStatus() {
		if (this.data.order.status === "10") {
			this.setData({
				status1: "待核销",
			});
		}

		if (this.data.order.status === "11") {
			this.setData({
				status1: "订单已过期",
			});
		}

		if (this.data.order.status === "20") {
			this.setData({
				status1: "订单已核销",
				status2: "笔记待上传",
			});
		}

		if (this.data.order.status === "30") {
			this.setData({
				status1: "订单已核销",
				status2: "笔记审核中",
			});
		}

		if (this.data.order.status === "31") {
			this.setData({
				status1: "订单已核销",
				status2: "笔记已通过审核",
			});
		}

		if (this.data.order.status === "32") {
			this.setData({
				status1: "订单已核销",
				status2: "笔记已驳回",
			});

			// Toast({
			// 	context: this,
			// 	selector: "#t-toast",
			// 	message: "",
			// 	icon: "",
			// 	duration: 5000,
			// });

			Message.info({
				context: this,
				offset: [500, 32],
				marquee: { speed: 100, loop: -1, delay: 5000 },
				// icon: "notification-filled",
				content:
					"笔记被驳回，滑动到页面底部可以查看驳回理由，如果有疑问可以加审核微信(更优化的笔记指南稍后就来)",
				duration: -1,
				closeBtn: true,
			});
		}

		// console.debug("note", this.data.note)
		// if (this.data.note.status === 2) {
		//   this.setData({
		//     status2: "笔记已驳回"
		//   })
		// }
	},

	async initExpandStatus() {
		// 如果订单状态是待核销，则展开第一步
		if (this.data.order.status === "10") {
			this.setData({
				isStepExpanded: true,
				isSecondStepExpanded: false,
			});
		}

		// 待上传 审核中 审核通过 审核驳回  收缩第一步 展开第二步
		if (
			this.data.order.status === "20" ||
			this.data.order.status === "30" ||
			this.data.order.status === "31" ||
			this.data.order.status === "32"
		) {
			this.setData({
				isStepExpanded: false,
				isSecondStepExpanded: true,
			});
			// const note = await this.fetchNote(this.data.order._id)
			// console.debug("note1111", note)
		} else {
			if (this.data.order.status === "0" || this.data.order.status === "100") {
				this.setData({
					isStepExpanded: false,
					isSecondStepExpanded: false,
				});
			}
		}
	},

	async formatOrderDetails() {
		if(this.data.order) {
			const order = this.data.order;
			const createdAt = formatDateTime(order.createdAt);
			const paidAt = formatDateTime(order.paidAt);
			const verifiedAt = formatDateTime(order.verifiedAt);
			const refundedAt = formatDateTime(order.refundedAt);
			const auditedAt = formatDateTime(order.auditedAt);
			const orderType = formatOrderType(order.order_type);
			let validUntil = "";
			if(order.order_type === "tandian1") {
				validUntil = dayjs(order.createdAt)
														.add(1, 'day')            // Move to next day
														.endOf('day')
														.format("YYYY-MM-DD HH:mm:ss") + "前";   
			}else if (order.order_type === "tandian2") {
				validUntil =  "活动下架前"
			} else if (order.order_type === "ykj") {
				validUntil =  dayjs(order.createdAt)
					.add(29, 'day')            // Move to next day
					.endOf('day')
					.format("YYYY-MM-DD HH:mm:ss") + "前"; 
			} else {
				validUntil = "未知"
			}
			this.setData({
				createdAt,
				paidAt,
				verifiedAt,
				refundedAt,
				auditedAt,
				orderType,
				validUntil,
			});
		}
	},


	async fetchOrderCard() {
		await this.fetchCards();
		setTimeout(() => {
			const verifiedCards = this.data.cards.filter(
				(x) => x.platform == this.data.order.promotion_id[0].platform,
			);
			console.debug("verifiedCards", verifiedCards);
			if (verifiedCards) {
				this.setData({
					verifiedCard: verifiedCards[0],
				});
			}
		}, 1000);
	},

	async fetchOrderNote() {
		console.debug("fetchOrderNote");
		this.fetchNote(this.data.order._id).then((res) => {
			console.debug("res", res);
			if (Object.keys(res).length != 0) {
				if (this.data.order.status === "32") {
					// 笔记驳回的情况下，可以修改
					this.setData({
						note: res,
						uploaded: false,
					});
				} else {
					// 笔记在审核中和通过后的情况下，不能修改
					this.setData({
						note: res,
						uploaded: true,
					});
				}

				if (
					this.data.order.status === "30" ||
					this.data.order.status === "31"
				) {
					const originalFiles = this.data.note.images.map((x) => {
						return {
							url: x,
							name: x + Math.random().toString(36).substring(2, 8) + ".jpg",
							type: "image",
						};
					});
					this.setData({
						originalFiles: originalFiles,
					});
				}
			}
		});
	},

	async onShow() {
		// 订单支付完的时候，跳转到这个页面的时候如果二维码为空，则延时再获取一下
		// setTimeout(async () => {
		//   if (!this.data.order.qrcode) {
		//     await this.fetchOrderDetail(this.data.id);
		//   }
		// }, 1000)
		await this.init();
		// this.fetchQrCode();
	},

	async fetchQrCode() {
		const interval = setInterval(async () => {
			if (this.data.order && this.data.order.status === "10") {
				if (!this.data.order.qrcode) {
					this.fetchOrderDetail(this.data.id);
				} else {
					clearInterval(interval);
				}
			} else {
				if (this.data.order) {
					clearInterval(interval);
				} else {
					setTimeout(() => {
						clearInterval(interval);
					}, 3000);
				}
			}
		}, 500);
	},

	async hanleRefundOrder(e) {
		wx.showToast({
			title: "退款中",
			mask: true,
			icon: "loading",
			duration: 2000,
		});
		const res = await this.cancelOrder(this.data.order.order_no);
		console.debug("hanleRefundOrder res:", res);
		if (res.errMsg === "cloud.callFunction:ok") {
			// await this.fetchOrders(this.data.status)
			wx.showToast({
				title: "退款成功",
				icon: "success",
				duration: 2000,
			});
			this.init();
		}
	},

	onReady() {
		const expiryDate = dayjs(this.data.order.createdAt)
			.add(1, "day")
			.endOf("day")
			.format("YYYY/MM/DD HH:mm:ss");
		this.setData({
			expiryDate: expiryDate,
		});

		this.initStepStatus();
		this.initExpandStatus();
	},

	async onPullDownRefresh() {
		if (this.data.id) {
			// await this.fetchOrderDetail(this.data.id);
			// await this.fetchOrderNote()
			// await this.fetchOrderCard();
			await this.init();
		}
		wx.stopPullDownRefresh();
	},

	async getOrderDetail(orderId) {
		Toast.loading({
			message: "加载中...",
			forbidClick: true,
		});

		try {
			// 这里应该调用真实的API获取订单详情
			// const result = await fetchOrderDetail(orderId);
			// 模拟数据
			const result = {
				code: 0,
				data: {
					orderDetail: {
						orderId: orderId,
						status: "WAIT_VERIFICATION",
						statusDesc: "待核销",
						// 其他订单数据...
					},
				},
			};

			if (result.code === 0) {
				this.setData({
					orderDetail: result.data.orderDetail,
				});
			} else {
				Toast.warning("获取订单详情失败");
			}
		} catch (error) {
			console.error("获取订单详情异常", error);
			Toast.warning("获取订单详情异常");
		} finally {
			Toast.hide();
		}
	},

	onImageTap(e) {
		console.debug(e);
	},

	onCountDownFinish() {
		// 倒计时结束处理
		Toast.warning("订单已超时");
	},

	handleNotePopupShow() {
		this.setData({
			isNotePopupShow: true,
		});
	},

	handleNotePopupHide() {
		this.setData({
			isNotePopupShow: false,
		});
	},

	toggleShowMore() {
		// 展开/收起套餐详情
		this.setData({
			isShowMore: !this.data.isShowMore,
		});
	},

	

	onNavigateTap() {
		// 导航操作
		wx.openLocation({
			latitude: this.data.shop.latitude, // 纬度（示例值，实际应从后端获取）
			longitude: this.data.shop.longitude, // 经度（示例值，实际应从后端获取）
			name: this.data.shop.name,
			address: this.data.shop.address,
			scale: 18,
		});
	},

	onPhoneTap() {
		// 拨打电话
		const phone = this.data.shop.phone;
		wx.makePhoneCall({
			phoneNumber: phone,
			success: () => {
				console.log("拨打电话成功");
			},
			fail: (error) => {
				console.error("拨打电话失败", error);
			},
		});
	},

	onPullDownRefresh() {
		this.setData({ enable: true });
		setTimeout(() => {
			this.setData({ enable: false });
		}, 1000);
		this.fetchOrderDetail(this.data.id);
	},

	showVerifyDialog() {
		this.setData({
			verifyDialogVisible: true,
		});
	},

	closeVerifyDialog() {
		this.setData({
			verifyDialogVisible: false,
		});
	},

	async onVerifyOrder() {
		if (this.data.isVerifying) return;
		try {
			this.setData({ isVerifying: true });

			wx.showToast({
				title: "正在验券",
				icon: "loading",
				duration: 2000,
			});

			const verifyResult = await this.verifyOrder(this.data.order.order_no);
			console.debug("verifyResult:", verifyResult);
			if (verifyResult?.count === 1) {
				// await this.fetchOrderDetail(this.data.id);
				await this.init();
				wx.showToast({
					title: "核销成功",
					icon: "success",
					duration: 2000,
				});
			} else {
				wx.showToast({
					title: "核销失败",
					icon: "error",
					duration: 2000,
				});
			}
		} catch (error) {
			console.error("核销失败:", error);
			wx.showToast({
				title: "核销失败",
				icon: "error",
				duration: 2000,
			});
		} finally {
			this.setData({ isVerifying: false });
			this.closeVerifyDialog();
		}
	},

	onPayNow() {
		// 立即支付
		console.log("立即支付");
		wx.showToast({
			title: "支付功能开发中",
			icon: "none",
		});
	},

	// 笔记链接输入变化
	onNoteLinkChange(e) {
		this.setData({
			"note.url": e.detail.value,
		});
	},

	onNoteSyncLinkChange(e) {
		this.setData({
			"note.sync_url": e.detail.value,
		});
	},

	// 粘贴笔记链接
	onPasteLink() {
		wx.getClipboardData({
			success: (res) => {
				if (res.data) {
					this.setData({
						"note.url": res.data,
					});
					wx.showToast({
						title: "粘贴成功",
						icon: "success",
					});
				}
			},
		});
	},

	// 粘贴同步笔记链接
	onPasteSyncLink() {
		wx.getClipboardData({
			success: (res) => {
				if (res.data) {
					this.setData({
						"note.sync_url": res.data,
					});
					wx.showToast({
						title: "粘贴成功",
						icon: "success",
					});
				}
			},
		});
	},

	// 上传图片成功
	onImagesSuccess(e) {
		console.debug("onImagesSuccess e:", e);
		const { files } = e.detail;
		this.setData({
			"note.images": files,
		});
	},

	// 删除图片
	onImagesRemove(e) {
		console.debug("onImagesRemove:", e);
		const { index } = e.detail;
		const { note } = this.data;
		const { images } = note;
		images.splice(index, 1);
		this.setData({
			"note.images": images,
		});
	},

	// 评分变化
	onRatingChange(e) {
		this.setData({
			"note.rating": e.detail.value,
		});
	},

	// 反馈内容变化
	onFeedbackChange(e) {
		this.setData({
			"note.feedback": e.detail.value,
		});
	},

	// 提交笔记
	async onSubmitNote() {
		if (this.data.isSubmitting) {
			return;
		}

		this.setData({
			isSubmitting: true,
		});

		const { url, images, rating, feedback } = this.data.note;
		console.debug(images);
		if (!url) {
			wx.showToast({
				title: "请填写笔记链接",
				icon: "none",
			});
			return;
		}

		if (this.data.promotion.need_to_sync) {
			if (!this.data.note.sync_url) {
				wx.showToast({
					title: "请填写同步笔记链接",
					icon: "none",
				});
				return;
			}
		}

		// if (images.length === 0) {
		// 	wx.showToast({
		// 		title: "请上传笔记截图",
		// 		icon: "none",
		// 	});
		// 	return;
		// }

		// wx.showToast({
		// 	title: "正在上传图片",
		// 	icon: "loading",
		// 	duration: 2000,
		// });

		// const uploadedFiles = await this.uploadFiles(images);
		// console.debug("uploadedFiles", uploadedFiles);

		// this.setData({
		// 	"note.images": uploadedFiles,
		// });

		this.setData({
			"note.order_id": {
				_id: this.data.order._id,
			},
			"note.shop_id": {
				_id: this.data.shop._id,
			},
			"note.card_id": {
				_id: this.data.verifiedCard._id,
			},
		});
		console.debug("before submit, note:", this.data.note);

		const res = await this.submitNote(this.data.note);
		console.debug(res, "res");

		if (res) {
			wx.showToast({
				title: "提交成功",
				icon: "success",
				duration: 2000,
			});
		}

		this.init();
	},

	async uploadFiles(images) {
		return new Promise((resolve, reject) => {
			if (images.length === 0) {
				resolve([]);
				return;
			}

			const uploadPromises = [];
			const uploadedFiles = [];

			for (let index = 0; index < images.length; index++) {
				const item = images[index];
				const tempFilePath = item.url;
				// 获取文件后缀
				const fileExtension = getFileExtension(tempFilePath);
				console.log("文件后缀:", fileExtension);
				// 生成随机文件名并保留后缀
				const randomFileName = `${generateRandomFileName()}.${fileExtension}`;
				const cloudPath = `notes/${this.data.order._id}/${randomFileName}`; // 云存储路径

				// 创建每个上传的Promise
				const uploadPromise = new Promise((resolveUpload, rejectUpload) => {
					// 将图片上传至云存储空间
					wx.cloud.uploadFile({
						cloudPath: cloudPath,
						filePath: tempFilePath,
						success: (res) => {
							console.log("上传成功", res);
							if (res.statusCode === 204) {
								uploadedFiles.push(res.fileID);
								resolveUpload();
							} else {
								rejectUpload(new Error("上传状态码错误"));
							}
						},
						fail: (err) => {
							console.error("上传失败", err);
							rejectUpload(err);
						},
					});
				});

				uploadPromises.push(uploadPromise);
			}

			// 等待所有上传完成
			Promise.all(uploadPromises)
				.then(() => {
					console.log("所有文件上传完成", uploadedFiles);
					resolve(uploadedFiles);
				})
				.catch((err) => {
					console.error("部分文件上传失败", err);
					reject(err);
				});
		});
	},

	handleBack() {
		wx.navigateBack();
	},

	onCopyShopName(e) {
		console.debug(e);
		wx.setClipboardData({
			data: e.currentTarget.dataset.copyContent,
			success(res) {
				wx.getClipboardData({
					success(res) {
						console.log(res.data); // data
					},
				});
				wx.showToast({
					title: "店铺名称已复制",
					icon: "success",
					duration: 2000,
				});
			},
		});
	},

	onCopyTopics(e) {
		console.debug(e);
		wx.setClipboardData({
			data: e.currentTarget.dataset.copyContent,
			success(res) {
				wx.getClipboardData({
					success(res) {
						console.log(res.data); // data
					},
				});
				wx.showToast({
					title: "话题标签已复制",
					icon: "success",
					duration: 2000,
				});
			},
		});
	},
});
