:host {
  background-color: #f8f8f8;
}

.order-detail {
  width: 100%;
  box-sizing: border-box;
  padding: 0rpx 0rpx calc(env(safe-area-inset-bottom) + 144rpx);
}

.order-detail .count-down {
  color: #ffffff;
}
.order-detail .header {
  width: 100%;
  background-color: #ffffff;
}
.order-detail .order-detail__header {
  width: 700rpx;
  height: 200rpx;
  border-radius: 24rpx;
  margin: 0 auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-image: url('https://cdn-we-retail.ym.tencent.com/miniapp/template/order-bg.png');
  background-repeat: no-repeat;
  background-size: contain;
}
.order-detail .order-detail__header .title,
.order-detail .order-detail__header .desc {
  color: #ffffff;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
.order-detail .order-detail__header .title {
  -webkit-line-clamp: 1;
  font-size: 44rpx;
  line-height: 64rpx;
  margin-bottom: 8rpx;
  font-weight: bold;
}
.order-detail .order-detail__header .desc {
  -webkit-line-clamp: 2;
  font-size: 24rpx;
  line-height: 32rpx;
}
.order-detail .order-detail__header .desc .count-down {
  display: inline;
}
.order-detail .order-logistics {
  box-sizing: border-box;
  padding: 32rpx;
  width: 100%;
  background-color: #ffffff;
  overflow: hidden;
  color: #333333;
  font-size: 32rpx;
  line-height: 48rpx;
  display: flex;
  position: relative;
}

.order-logistics .logistics-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.order-logistics .logistics-content {
  flex: 1;
}

.order-logistics .logistics-content .logistics-time {
  font-size: 28rpx;
  line-height: 40rpx;
  color: #999999;
  margin-top: 12rpx;
}

.order-logistics .logistics-back {
  color: #999999;
  align-self: center;
}

.order-logistics .edit-text {
  color: #fa4126;
  font-size: 26rpx;
  line-height: 36rpx;
}

.order-detail .border-bottom {
  margin: 0 auto;
  width: 686rpx;
  scale: 1 0.5;
  height: 2rpx;
  background-color: #e5e5e5;
}

.order-detail .border-bottom-margin {
  margin: 16rpx auto;
}

.order-detail .pay-detail {
  background-color: #ffffff;
  width: 100%;
  box-sizing: border-box;
}

.order-detail .padding-inline {
  padding: 16rpx 32rpx;
}

.order-detail .pay-detail .pay-item {
  width: 100%;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 26rpx;
  line-height: 36rpx;
  color: #666666;
  background-color: #ffffff;
}
.order-detail .pay-detail .pay-item .pay-item__right {
  color: #333333;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  max-width: 400rpx;
}
.order-detail .pay-detail .pay-item .pay-item__right .pay-remark {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  max-width: 400rpx;
  text-overflow: ellipsis;
  overflow: hidden;
}
.order-detail .pay-detail .pay-item .font-bold {
  font-weight: bold;
}
.order-detail .pay-detail .pay-item .primary {
  color: #fa4126;
}
.order-detail .pay-detail .pay-item .max-size {
  font-size: 36rpx;
  line-height: 48rpx;
}

.pay-item .pay-item__right .pay-item__right__copy {
  width: 80rpx;
  height: 40rpx;
  text-align: center;
  font-size: 24rpx;
  line-height: 40rpx;
  color: #333333;
  position: relative;
}

.pay-item .pay-item__right .pay-item__right__copy::before {
  position: absolute;
  content: '';
  width: 200%;
  height: 200%;
  border-radius: 40rpx;
  border: 2rpx solid #dddddd;
  transform: scale(0.5);
  left: 0;
  top: 0;
  transform-origin: left top;
}

.pay-item .pay-item__right .order-no {
  color: #333333;
  font-size: 26rpx;
  line-height: 40rpx;
  padding-right: 16rpx;
}

.pay-item .pay-item__right .normal-color {
  color: #333333;
}

.order-detail .pay-detail .pay-service {
  width: 100%;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  line-height: 36rpx;
  color: #333333;
  background-color: #ffffff;
}

.bottom-bar {
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 10;
  background: #fff;
  height: 112rpx;
  width: 686rpx;
  padding: 0rpx 32rpx env(safe-area-inset-bottom);
  display: flex;
  align-items: center;
}

.bottom-bar::before {
  position: absolute;
  content: '';
  width: 200%;
  height: 200%;
  border-top: 2rpx solid #dddddd;
  transform: scale(0.5);
  left: 0;
  top: 0;
  transform-origin: left top;
}

.goods-button-bar {
  height: 112rpx;
  width: 686rpx;
  margin-bottom: 16rpx;
}

.t-class-indicator {
  color: #b9b9b9 !important;
}

.add-notes__confirm {
  color: #fa4126 !important;
}

.t-button {
  --td-button-default-color: #000;
  --td-button-primary-text-color: #fa4126;
}
