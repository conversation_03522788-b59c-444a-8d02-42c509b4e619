import { createStoreBindings } from "mobx-miniprogram-bindings";
import { locationStore, productStore } from "../../stores/index";
import { fetchHome, fetchStoresList } from "../../services/home/<USER>";
// import { fetchGoodsList } from "../../services/good/fetchGoods";
import { log } from "../../utils/log";
import Toast from "tdesign-miniprogram/toast/index";
// const chooseLocation = requirePlugin("chooseLocation");

import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
dayjs.extend(isBetween);

Page({
	properties: {
		scrollTop: { type: Number, value: 0 },
	},

	data: {
		imgSrcs: [],
		tabList: [],
		goodsList: [],
		goodsListLoadStatus: 0,
		pageLoading: false,
		current: 1,
		autoplay: true,
		duration: "500",
		interval: 5000,
		navigation: { type: "dots" },
		swiperImageProps: { mode: "scaleToFit" },
		isLoading: false,
		isRefreshing: false,
		statusBarHeight: 0, // 状态栏高度
		navBarHeight: 44, // 导航栏高度
		paddingLeft: 10,
		isFixed1: false,
		location: {},
		miniProgram: {},
		// shopList: [],
		// totalShops: 0,
		selectedCategory: "0",
		categories: [],
		// storesList: [],
		rowColsAvatar: [{ size: "60rpx", type: "circle" }],
		rowColsImage: [{ size: "96rpx", type: "rect" }],
		rowColsContent: [{ width: "50%" }, { width: "100%" }],
		isUpdatingStock: false,
		backRefresh: true,
		backTopTheme: "round",
		backTopText: "顶部",
		showBackTop: false,
		isAndroid: false, // 默认为 false
		enable: false, // 下拉刷新状态
	},

	checkLocation() {
		console.debug("checkLocation");
		wx.getSetting({
			success(res) {
				const locationAuth = res.authSetting["scope.userLocation"];
				if (locationAuth === undefined) {
					// 还未询问过权限
					console.log("尚未询问定位权限");
				} else if (locationAuth === false) {
					// 用户拒绝了定位权限
					console.log("用户拒绝了定位权限");
					wx.authorize({
						scope: "scope.userLocation",
						success() {
							// 用户已经同意授权
							console.log("定位权限已授权");
						},
						fail() {
							// 用户拒绝了授权
							console.log("定位权限被拒绝");
							Toast({
								context: this,
								selector: "#t-toast",
								message: "请打开定位功能",
							});
							console.debug("请打开定位功能");
							return;
						},
					});
				} else {
					// 用户已授权定位权限
					console.log("用户已授权定位权限");
				}
			},
		});
	},

	async onLoad(options) {
		console.debug("onLoad options", options);

		const { miniProgram } = wx.getAccountInfoSync();
		this.setData({
			miniProgram: miniProgram,
			enable: false, // 初始状态不是刷新中
		});

		// 检测系统类型
		const deviceInfo = wx.getDeviceInfo();
		this.setData({
			isAndroid:
				deviceInfo.model.toLowerCase().includes("android") ||
				deviceInfo.system.toLowerCase().includes("android"),
		});

		this.bindMbxStores();

		// 确保数据结构初始化正确
		if (!this.data.shops_home) {
			this.setData({
				shops_home: [],
				page_number: 1,
				page_size: 10,
				total: 0,
				shopsLoadStatus: 0
			});
		}

		// 初始化位置记录
		this.lastProcessedLocation = null;
		this.hasHandledManualLocationInOnShow = false;

		this.locateMe();
		// this.checkLocation();

		this.initStatusBar();
		this.loadCategories();
		this.loadHomePage();

		// console.debug("this.locate")
		// const locatePromise = await this.locate();
		// console.debug("locatePromise", locatePromise);
	},

	bindMbxStores() {
		// 将 store 绑定到页面
		this.productStoreBindings = createStoreBindings(this, {
			store: productStore,
			fields: [
				// "shops",
				"shops_home",
				"page_number",
				"page_size",
				"total",
				"shopsLoadStatus",
				"selectedCategory",
				"categories",
			],
			actions: [
				"getAllCategories",
				"getNearestShops",
				"setSelectedCategory",
				"setPageNumber",
				"setTotal",
				"resetShops",
			],
		});

		this.locationStoreBindings = createStoreBindings(this, {
			store: locationStore,
			fields: ["location", "nearbyAreas", "hotAreas"],
			actions: ["locate", "loadLocationFromStorage", "getNearbyAreas", "getHotAreas", "geocoder"],
		});
	},

	async onShow() {
		this.getTabBar().init();

		// 先从存储加载位置信息，确保显示最新的位置（包括手动选择的商圈）
		await this.loadLocationFromStorage();

		const isManualLocation = this.isManuallySelectedLocation();
		const storedLocation = wx.getStorageSync("dgx-location");

		console.debug("=== 位置检查详情 ===", {
			isManualLocation,
			backRefresh: this.data.backRefresh,
			currentLocation: this.data.location,
			storedLocation: storedLocation,
			isManuallySelectedFlag: storedLocation?.isManuallySelected,
			locationName: storedLocation?.name,
			hasAddressComponent: !!storedLocation?.address_component
		});

		// 检查是否需要强制刷新（针对第一次切换的特殊处理）
		const shouldForceRefresh = this.shouldForceRefreshForLocationChange(storedLocation);

		// 只有在没有手动选择商圈的情况下才检查GPS位置变化
		if (!isManualLocation) {
			console.debug("检查GPS位置变化");
			await this.checkLocationAndReload();
		} else {
			console.debug("跳过GPS检查，使用手动选择的商圈");
			// 检查是否已经在商圈选择页面触发了加载
			const hasTriggeredFromAreaPage = this.data.lastLocationSelectTime &&
				(Date.now() - this.data.lastLocationSelectTime < 1000); // 1秒内

			// 如果是手动选择的位置且需要刷新，或者需要强制刷新，直接重新加载数据
			if ((this.data.backRefresh || shouldForceRefresh) && !hasTriggeredFromAreaPage) {
				console.debug("=== 开始手动选择位置后的数据刷新 ===");
				console.debug("触发原因:", {
					backRefresh: this.data.backRefresh,
					shouldForceRefresh: shouldForceRefresh,
					hasTriggeredFromAreaPage: hasTriggeredFromAreaPage
				});
				console.debug("当前位置信息:", storedLocation);

				// 标记已处理手动位置选择，避免在init中重复处理
				this.hasHandledManualLocationInOnShow = true;

				this.setPageNumber(1);
				this.setTotal(0);
				this.resetShops();

				// 立即开始加载，减少用户等待时间
				console.debug("开始加载新位置的数据...");
				this.loadNearestShops(this.data.selectedCategory, "tandian1");

				// 记录当前位置，用于下次比较
				this.lastProcessedLocation = {
					latitude: storedLocation.latitude,
					longitude: storedLocation.longitude,
					name: storedLocation.name
				};
			} else {
				console.debug("手动选择位置但无需刷新", {
					reason: hasTriggeredFromAreaPage ? "已在商圈页面触发" : "无需刷新"
				});
				// 即使不重新加载，也要记录位置
				this.lastProcessedLocation = {
					latitude: storedLocation.latitude,
					longitude: storedLocation.longitude,
					name: storedLocation.name
				};
			}
		}

		console.debug("init");
		this.init();
	},

	/**
	 * 检查是否是手动选择的位置（商圈选择）
	 */
	isManuallySelectedLocation() {
		const storedLocation = wx.getStorageSync("dgx-location");
		if (!storedLocation) return false;

		// 优先检查明确的手动选择标记
		if (storedLocation.isManuallySelected) {
			return true;
		}

		// 兼容旧的检测逻辑
		return storedLocation.name && (
			!storedLocation.address_component ||
			(storedLocation.address && storedLocation.name !== storedLocation.address)
		);
	},

	/**
	 * 检查是否需要强制刷新（针对位置变化的检测）
	 */
	shouldForceRefreshForLocationChange(currentLocation) {
		if (!currentLocation) return false;

		// 如果没有上次处理的位置记录，说明是第一次，需要强制刷新
		if (!this.lastProcessedLocation) {
			console.debug("没有上次位置记录，需要强制刷新");
			return true;
		}

		// 检查位置是否发生变化
		const locationChanged =
			this.lastProcessedLocation.latitude !== currentLocation.latitude ||
			this.lastProcessedLocation.longitude !== currentLocation.longitude ||
			this.lastProcessedLocation.name !== currentLocation.name;

		if (locationChanged) {
			console.debug("位置发生变化，需要强制刷新:", {
				lastLocation: this.lastProcessedLocation,
				currentLocation: {
					latitude: currentLocation.latitude,
					longitude: currentLocation.longitude,
					name: currentLocation.name
				}
			});
			return true;
		}

		return false;
	},

	/**
	 * 检查位置变化并重新加载数据
	 */
	async checkLocationAndReload() {
		try {
			// 获取当前位置
			const currentLocation = await this.getCurrentLocation();

			// 获取存储的上次位置
			const lastLocation = this.lastKnownLocation || this.data.location;

			// 检查位置是否发生显著变化（距离超过500米）
			if (this.hasLocationChanged(lastLocation, currentLocation)) {
				console.debug("位置发生变化，重新加载数据");

				// 更新位置信息
				await this.updateLocationInfo(currentLocation);

				// 重置数据并重新加载
				this.setPageNumber(1);
				this.setTotal(0);
				this.resetShops();

				// 延迟加载以确保位置信息已更新
				setTimeout(() => {
					this.loadNearestShops(this.data.selectedCategory, "tandian1");
				}, 300);

				// 记录当前位置
				this.lastKnownLocation = currentLocation;
			}
		} catch (error) {
			console.debug("位置检测失败:", error);
			// 位置检测失败时，仍然尝试从存储加载位置信息
			await this.loadLocationFromStorage();
		}
	},

	/**
	 * 获取当前位置
	 */
	getCurrentLocation() {
		return new Promise((resolve, reject) => {
			// 先检查位置权限
			wx.getSetting({
				success: (res) => {
					if (res.authSetting['scope.userLocation'] === false) {
						// 用户拒绝了位置权限
						reject(new Error('位置权限被拒绝'));
						return;
					}

					wx.getLocation({
						type: 'gcj02',
						success: (res) => {
							resolve({
								latitude: res.latitude,
								longitude: res.longitude
							});
						},
						fail: (err) => {
							reject(err);
						}
					});
				},
				fail: (err) => {
					reject(err);
				}
			});
		});
	},

	/**
	 * 检查位置是否发生显著变化
	 */
	hasLocationChanged(lastLocation, currentLocation) {
		if (!lastLocation || !currentLocation) {
			return true; // 如果没有上次位置信息，认为位置发生了变化
		}

		// 计算两点间距离（简单的距离计算）
		const distance = this.calculateDistance(
			lastLocation.latitude, lastLocation.longitude,
			currentLocation.latitude, currentLocation.longitude
		);

		// 如果距离超过500米，认为位置发生了显著变化
		return distance > 0.5; // 0.5公里
	},

	/**
	 * 计算两点间距离（公里）
	 */
	calculateDistance(lat1, lon1, lat2, lon2) {
		const R = 6371; // 地球半径（公里）
		const dLat = (lat2 - lat1) * Math.PI / 180;
		const dLon = (lon2 - lon1) * Math.PI / 180;
		const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
			Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
			Math.sin(dLon/2) * Math.sin(dLon/2);
		const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
		return R * c;
	},

	/**
	 * 更新位置信息
	 */
	async updateLocationInfo(currentLocation) {
		// 更新存储的位置信息
		const storedLocation = wx.getStorageSync("dgx-location") || {};
		const updatedLocation = {
			...storedLocation,
			latitude: currentLocation.latitude,
			longitude: currentLocation.longitude
		};

		wx.setStorageSync("dgx-location", updatedLocation);

		// 调用地理编码获取详细地址信息
		try {
			await this.locate(true); // 强制重新定位
		} catch (error) {
			console.debug("地理编码失败:", error);
		}
	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {
		// 解绑 store
		this.productStoreBindings.destroyStoreBindings();
		this.locationStoreBindings.destroyStoreBindings();
	},

	onPageScroll(e) {
		if (this.data.isAndroid) {
			console.debug("onPageScroll", e);
			// 滚动超过 300px 时显示
			this.setData({ showBackTop: e.scrollTop > 300 });
		}
	},

	init() {
		this.handleIsUpdatingStockButton();

		// 检查是否已经在onShow中处理了手动位置选择
		const isManualLocation = this.isManuallySelectedLocation();
		const hasHandledManualLocation = this.hasHandledManualLocationInOnShow;

		console.debug("=== init 方法检查 ===", {
			backRefresh: this.data.backRefresh,
			isManualLocation,
			hasHandledManualLocation,
			shopsHomeLength: this.data.shops_home ? this.data.shops_home.length : 0
		});

		// 如果是返回刷新或者没有数据，且没有在onShow中处理过手动位置选择，则重新加载
		const shopsHomeLength = this.data.shops_home ? this.data.shops_home.length : 0;
		if ((this.data.backRefresh || shopsHomeLength === 0) && !hasHandledManualLocation) {
			console.debug("init 中需要重新加载数据");

			// 确保位置信息是最新的
			this.loadLocationFromStorage();

			this.setPageNumber(1);
			this.setTotal(0);
			this.resetShops();

			setTimeout(() => {
				this.loadNearestShops(this.data.selectedCategory, "tandian1");
			}, 200);
		} else {
			console.debug("init 中跳过数据加载", {
				reason: hasHandledManualLocation ? "已在onShow中处理" : "无需刷新"
			});
		}

		// 重置标志
		if (this.data.backRefresh) {
			this.setData({
				backRefresh: false
			});
		}
		this.hasHandledManualLocationInOnShow = false;
	},
	
	async onReady() {
		// 延迟加载非关键数据​
		setTimeout(() => {
			this.getNearbyAreas();
			this.getHotAreas();
		}, 5000);
	},

	handleIsUpdatingStockButton() {
		// 定义时间区间
		const startTimeStr = `${dayjs().format("YYYY-MM-DD")} 00:00:00`;
		const endTimeStr = `${dayjs().format("YYYY-MM-DD")} 00:10:00`;
		const startTime = dayjs(startTimeStr, "YYYY-MM-DD HH:mm:ss");
		const endTime = dayjs(endTimeStr, "YYYY-MM-DD HH:mm:ss");

		// 要检查的时间
		const checkTime = dayjs();
		// 判断是否在区间内（包含边界）
		const between = checkTime.isBetween(startTime, endTime, "second", "[]");

		if (between) {
			this.setData({
				isUpdatingStock: true,
			});
		} else {
			this.setData({
				isUpdatingStock: false,
			});
		}
	},

	async onReachBottom() {
		console.debug("=== onReachBottom 触发 ===");

		// 防抖：如果上次触发时间太近，则跳过
		const now = Date.now();
		if (this.lastReachBottomTime && now - this.lastReachBottomTime < 500) {
			console.debug("触发太频繁，跳过");
			return;
		}
		this.lastReachBottomTime = now;

		const shopsHomeLength = this.data.shops_home ? this.data.shops_home.length : 0;
		console.debug("分页信息:", {
			current_shops_count: shopsHomeLength,
			page_number: this.data.page_number,
			page_size: this.data.page_size,
			total: this.data.total,
			shopsLoadStatus: this.data.shopsLoadStatus,
			selectedCategory: this.data.selectedCategory,
			shopsHomeExists: !!this.data.shops_home
		});

		// 检查是否还有更多数据可以加载
		if (this.data.shopsLoadStatus === 1) {
			console.debug("正在加载中，跳过");
			return;
		}

		if (this.data.total > 0 && shopsHomeLength >= this.data.total) {
			console.debug("已加载所有数据，没有更多了");
			this.setData({
				shopsLoadStatus: 2,
			});
		} else {
			console.debug("开始加载更多数据...");
			// 传递正确的业务类型参数，默认使用 tandian1
			await this.loadNearestShops(this.data.selectedCategory, "tandian1");
			console.debug("加载更多数据完成");
		}
	},

	async onPullDownRefresh() {
		console.debug("onPulldownRefresh 开始");

		// 设置刷新状态为 true
		this.setData({
			enable: true
		});

		try {
			// 重新定位
			const ignoreLocalStorage = true;
			await this.locate(ignoreLocalStorage);

			// 重置数据并重新加载
			this.setPageNumber(1);
			this.setTotal(0);
			this.resetShops();

			// 重新加载数据
			await this.loadNearestShops(this.data.selectedCategory, "tandian1");

			console.debug("下拉刷新数据加载完成");

		} catch (error) {
			console.error("下拉刷新失败:", error);
		} finally {
			// 延迟停止刷新状态，确保用户能看到刷新完成的提示
			setTimeout(() => {
				this.setData({
					enable: false
				});
				console.debug("下拉刷新状态重置完成");
			}, 500);
		}
	},

	onScroll() {
		console.debug("onScroll", this.data.scrollTop);
	},

	async loadCategories() {
		await this.getAllCategories();
	},

	async loadNearestShops(category_id, business_type_tags = "tandian1") {
		console.debug("loadNearestShops 开始:", {
			category_id,
			business_type_tags,
			current_shops_count: this.data.shops_home.length,
			page_number: this.data.page_number
		});

		// 安全检查城市信息
		const city = this.data.location?.address_component?.city;
		if (city && city !== "武汉市") {
			Toast({
				context: this,
				selector: "#t-toast",
				message: "不在服务范围内",
				duration: 5000,
			});
			this.setData({
				pageLoading: false,
			});
			this.setPageNumber(1);
			this.setTotal(0);
			this.resetShops();
			return;
		} else {
			this.setData({ goodsListLoadStatus: 1 });
			await this.getNearestShops(category_id, business_type_tags);

			console.debug("loadNearestShops 完成:", {
				shops_count: this.data.shops_home.length,
				total: this.data.total,
				shopsLoadStatus: this.data.shopsLoadStatus
			});
		}
	},

	initStatusBar() {
		const { statusBarHeight } = wx.getWindowInfo();

		// 获取安全区域信息
		const systemInfo = wx.getSystemInfoSync();
		const safeAreaTop = systemInfo.safeArea ? systemInfo.safeArea.top : statusBarHeight;

		this.setData({
			statusBarHeight: statusBarHeight,
			safeAreaTop: safeAreaTop,
		});

		// 动态设置下拉刷新的安全区域
		this.setSafeAreaForPullRefresh(safeAreaTop);
	},

	setSafeAreaForPullRefresh(safeAreaTop) {
		// 通过CSS变量设置安全区域
		const query = wx.createSelectorQuery().in(this);
		query.select('.custom-pull-refresh').node().exec((res) => {
			if (res[0] && res[0].node) {
				res[0].node.style.setProperty('--safe-area-top', `${safeAreaTop}px`);
			}
		});
	},

	loadHomePage() {
		this.setData({
			pageLoading: true,
		});
		fetchHome().then(({ swiper, tabList }) => {
			this.setData({
				tabList,
				imgSrcs: swiper,
				pageLoading: false,
			});
		});
	},

	onTabChange(e) {
		// this.privateData.tabIndex = e.detail;
		// this.loadGoodsList(true);
		// this.loadStoresList(true);
		// console.debug(e.detail);
		// console.debug(e.detail.value);
		this.setSelectedCategory(e.detail.value);
		this.setPageNumber(1);
		this.setTotal(0);
		this.resetShops();
		setTimeout(() => {
			this.loadNearestShops(e.detail.value, "tandian1");
		}, 10);
	},

	onReTry() {
		// this.loadGoodsList();
		// this.loadStoresList(true);
	},

	navToSearchPage() {
		wx.navigateTo({ url: "/pages/search/index" });
	},

	navToActivityDetail() {
		console.debug("click swiper");
		wx.switchTab({
			url: "/pages/invite/index",
		});
	},

	chooseLocation() {
		wx.navigateTo({
			url: "/pages/areas/index",
		});
		return;
	},

	async locateMe() {
		const loc = wx.getStorageSync("dgx-location");
		if (loc) {
			console.debug("loadLocationFromStorage in locateMe");
			const loc = await this.loadLocationFromStorage();
			// 安全检查城市信息
			const city = loc?.address_component?.city;
			if (city && city !== "武汉市") {
				Toast({
					context: this,
					selector: "#t-toast",
					message: "不在服务范围内",
					duration: 5000,
				});
				this.setData({
					pageLoading: false,
					shops: [],
				});
				return;
			}
			return;
		} else {
			this.chooseLocation();
			return;
		}

		// if (this.data.location && this.data.location.serviceUnavailable) {
		// 	Toast({
		// 		context: this,
		// 		selector: "#t-toast",
		// 		message: "暂不支持你所在区域",
		// 	});
		// 	console.debug("sorry, service unavailable in your city");
		// 	return;
		// }

		// wx.navigateTo({
		// 	url: "/pages/areas/index",
		// });
		// return;

		// 先获取当前位置，然后设置地图中心点
		// wx.getLocation({
		// 	type: 'gcj02',
		// 	success: (res) => {
		// 		console.debug("wx.getLocation success:", res)
		wx.chooseLocation({
			// latitude: res.latitude,  // 设置地图中心点为当前位置
			// longitude: res.longitude,
			success: (res) => {
				console.debug("wx.chooseLocation success:", res);
				if (res.errMsg === "chooseLocation:ok") {
					const location = {
						name: res.name,
						address: res.address,
						latitude: res.latitude,
						longitude: res.longitude,
					};
					this.setData({
						location: location,
					});
					wx.setStorageSync("dgx-location", location);
					this.setPageNumber(1);
					this.setTotal(0);
					this.resetShops();
					setTimeout(() => {
						this.loadNearestShops(this.data.selectedCategory, "tandian1");
					}, 300);
					// setTimeout(async () => {
					// 	console.debug(this.data.selectedCategory)
					// 	// this.init();
					// 	await this.loadNearestShops(this.data.selectedCategory);
					// }, 1000)
				}
			},
			fail: (err) => {
				console.debug("wx.chooseLocation fail:", err);
			},
		});
	},

	handleTapStore(e) {
		this.setData({
			backRefresh: false,
		});
		// 适配组件事件：从 e.detail 中获取数据
		const storeId = e.detail?.storeId || e.currentTarget?.dataset?.storeId;
		wx.navigateTo({
			url: `/pages/store/index?id=${storeId}`,
		});
	},

	handleTapProduct(e) {
		this.setData({
			backRefresh: false,
		});
		console.debug(e);
		// 适配组件事件：从 e.detail 中获取数据
		const promotionId = e.detail?.promotionId || e.currentTarget?.dataset?.promotionId;
		wx.navigateTo({
			url: `/pages/promotion/index?id=${promotionId}`,
		});
	},

	onShareAppMessage() {
		log.debug("onShareAppMessage");
		let referrer_id = null;
		const userInfo = wx.getStorageSync("dgx-auth");
		if (userInfo) {
			referrer_id = userInfo.user_id;
		}
		return {
			title: "快来优探生活！全城优质活动1折探！", // 分享标题
			path: `/pages/home/<USER>// 分享路径，通常是当前页面路径
			imageUrl:
				"cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-1321286342/app/share.jpg", // 分享图片
		};
	},

	onShareTimeline() {
		log.debug("onShareTimeline");
		let referrer_id = null;
		const userInfo = wx.getStorageSync("dgx-auth");
		if (userInfo) {
			referrer_id = userInfo.user_id;
		}
		return {
			title: "快来优探生活！全城优质活动1折探！", // 分享标题
			path: `/pages/home/<USER>// 分享路径，通常是当前页面路径
			imageUrl:
				"cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-1321286342/app/share.jpg", // 分享图片
			query: "referrer_id=${referrer_id}",
		};
	},

	onToTop(e) {
		this.triggerEvent("to-top", e);
	},
});
