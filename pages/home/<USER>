page {
	box-sizing: border-box;
	padding-bottom: calc(env(safe-area-inset-bottom) + 96rpx);
}

/* 下拉刷新组件安全区域适配 */
.custom-pull-refresh {
	/* 使用CSS变量和env()函数双重保障 */
	--safe-area-top: env(safe-area-inset-top, 0px);
}

/* 针对TDesign下拉刷新组件的loading区域进行适配 */
.custom-pull-refresh .t-pull-down-refresh__loading,
.custom-pull-refresh .t-pull-down-refresh__tips {
	margin-top: var(--safe-area-top, env(safe-area-inset-top, 0px)) !important;
	padding-top: 8rpx !important;
}

/* 确保下拉刷新的内容区域也正确适配 */
.custom-pull-refresh .t-pull-down-refresh__content {
	padding-top: var(--safe-area-top, env(safe-area-inset-top, 0px));
}

.navbar-addr {
	display: flex;
	justify-self: flex-start;
	align-items: center;
	height: 100rpx;
	padding-left: 24rpx;
}

.overlay-when-fixed {
	position: absolute;
	height: 100rpx;
	background-color: #f5f5f5;
}

.t-tabs.t-tabs--top .t-tabs__scroll {
	border-bottom: none !important;
}

.home-page-header {
	background: linear-gradient(#fff, #f5f5f5);
}

.search {
	padding-bottom: 20rpx;
}

/* 视频播放区域样式 */
.video-container {
	margin-top: 20rpx;
	margin-bottom: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	position: relative;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.video-player {
	width: 100%;
	height: 400rpx;
	border-radius: 16rpx;
	background: transparent;
	display: block;
}

/* 不再展示按钮 */
.video-close-btn {
	position: absolute;
	top: 16rpx;
	right: 16rpx;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 20rpx;
	padding: 8rpx 16rpx;
	display: flex;
	align-items: center;
	z-index: 20;
	backdrop-filter: blur(4rpx);
	transition: all 0.3s ease;
}

.video-close-btn:active {
	transform: scale(0.95);
	background: rgba(0, 0, 0, 0.8);
}

.close-text {
	color: #ffffff;
	font-size: 22rpx;
	margin-left: 6rpx;
	opacity: 0.9;
}

/* 视频加载状态 */
.video-loading {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 10;
	background: rgba(255, 255, 255, 0.9);
	padding: 20rpx 30rpx;
	border-radius: 12rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

/* 视频加载失败占位图 */
.video-placeholder {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 16rpx;
	z-index: 5;
}

.placeholder-text {
	color: #ffffff;
	font-size: 24rpx;
	margin-top: 16rpx;
	opacity: 0.8;
}

.home-page-container {
	background: #f5f5f5;
}

.home-page-container,
.home-page-header {
	display: block;
	padding: 0 24rpx;
}

.home-page-header .t-search__input-container {
	border-radius: 32rpx !important;
	height: 64rpx !important;
}

.home-page-header .t-search__input {
	font-size: 28rpx !important;
	color: rgb(116, 116, 116) !important;
}

.home-page-header .swiper-wrap {
	margin-top: 20rpx;
}

.home-page-header .t-image__swiper {
	width: 100%;
	height: 300rpx;
	border-radius: 10rpx;
}

.home-page-container .t-tabs {
	background: #f5f5f5 !important;
}

.home-page-container .t-tabs .t-tabs-nav {
	background-color: transparent;
	line-height: 80rpx;
	font-size: 28rpx;
	color: #333;
}

.home-page-container .t-tabs .t-tabs-scroll {
	border: none !important;
}

/* 半个字 */
.home-page-container .tab.order-nav .order-nav-item.scroll-width {
	min-width: 165rpx;
}

.home-page-container .tab .order-nav-item.active {
	color: #fa550f !important;
}

.home-page-container .tab .bottom-line {
	border-radius: 4rpx;
}

.home-page-container .tab .order-nav-item.active .bottom-line {
	background-color: #fa550f !important;
}

.home-page-container .tabs-external__item {
	/* color: #666 !important; */
	font-size: 28rpx;
}

.home-page-container .tabs-external__active {
	color: #333333 !important;
	font-size: 32rpx;
}

.home-page-container .tabs-external__track {
	/* background-color: #fa4126 !important; */
	height: 6rpx !important;
	border-radius: 4rpx !important;
	width: 48rpx !important;
}

.t-tabs.t-tabs--top .t-tabs__item,
.t-tabs.t-tabs--bottom .t-tabs__item {
	height: 86rpx !important;
}

.home-page-container .goods-list-container {
	background: #f5f5f5 !important;
	margin-top: 16rpx;
}

.home-page-tabs {
	--td-tab-nav-bg-color: transparent;
	--td-tab-border-color: transparent;
	--td-tab-item-color: #666;
	--td-tab-track-color: red;
}

/* 商品列表 */
.container {
	padding: 0;
	margin: 0;
	background-color: #f7f8fa;
	min-height: 100vh;
}

/* 加载状态 */
.loading-container {
	display: flex;
	justify-content: center;
	padding: 24rpx 0;
}

.store-list {
	/* padding: 12rpx 0; */
	margin-top: 12rpx;
}

/* 商家卡片样式 - 紧凑版 */
.store-card {
	margin-bottom: 20rpx;
	background-color: #fff;
	border-radius: 18rpx;
	/* overflow: hidden; */
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	/* transition: transform 0.2s; */
}

.store-hover {
	transform: scale(0.98);
	background-color: #fafafa;
}

/* 店铺信息紧凑版样式 */
.store-info-compact {
	padding: 20rpx;
	border-bottom: 1rpx solid #f2f2f2;
	display: flex;
}

/* 商店头部行 */
.store-header-row {
	display: flex;
	/* justify-content: space-between; */
	/* align-items: center; */
	align-items: flex-start;
	/* margin-bottom: 8rpx; */
	flex-direction: column;
	gap: 10rpx;
}

.store-left {
	display: flex;
	align-items: center;
	flex: 1;
	overflow: hidden;
}

.store-logo {
	display: flex;
	margin-top: 8rpx;
}

.logo-image-small {
	width: 60rpx;
	height: 60rpx;
	/* border-radius: 50%; */
	margin-right: 12rpx;
	flex-shrink: 0;
}

.store-name-compact {
	font-size: 26rpx;
	/* font-weight: bold; */
	color: #333;
	margin-right: 10rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 400rpx;
	padding-left: 8rpx;
}

.store-tag-compact {
	font-size: 20rpx;
	color: #ff5f15;
	/* background-color: #f7f7f7; */
	padding: 2rpx 8rpx;
	border-radius: 4rpx;
	flex-shrink: 0;
}

.store-right {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	margin-left: auto;
	gap: 15rpx;
}

.location-distance {
	display: flex;
	align-items: center;
	padding-top: 4rpx;
}

.location-text {
	font-size: 20rpx;
	color: #666;
	margin-right: 8rpx;
}

.distance-text {
	font-size: 20rpx;
	color: #999;
}

/* 第二行：排名 + 标签 */
.store-meta-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.store-meta-left {
	display: flex;
	align-items: center;
	flex: 1;
	overflow: hidden;
	margin-left: 70rpx;
}

.popularity-text-compact {
	font-size: 20rpx;
	color: #ff5f15;
	background-color: rgba(255, 95, 21, 0.1);
	padding: 2rpx 8rpx;
	border-radius: 6rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.badge-container {
	display: flex;
	align-items: center;
}

.badge-compact {
	font-size: 20rpx;
	color: #999;
	/* background-color: #f7f7f7; */
	/* padding: 2rpx 8rpx; */
	/* margin-left: 8rpx; */
	border-radius: 4rpx;
	display: flex;
	/* gap: 10rpx; */
	align-items: center;
	color: #ff5f15;
	gap: 2rpx;
}

.badge-compact t-icon {
	margin-right: 4rpx;
}

/* 食品列表样式 */
.food-list {
	padding: 0;
}

.food-card {
	display: flex;
	padding: 26rpx 20rpx;
	border-bottom: 1rpx solid #f5f5f5;
	position: relative;
}

.food-card:last-child {
	border-bottom: none;
}

.food-card-hover {
	background-color: #fafafa;
	transform: scale(0.98);
}

.food-image-container {
	width: 180rpx;
	height: 180rpx;
	margin-right: 20rpx;
	flex-shrink: 0;
	position: relative;
	overflow: hidden;
	border-radius: 16rpx;
}

.food-image {
	width: 100%;
	height: 100%;
	border-radius: 8rpx;
	transition: transform 0.3s;
}

.food-image:hover {
	transform: scale(1.05);
}

.food-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	/* height: 180rpx; */
	position: relative;
}

/* 标签和份数行 */
.food-top-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	margin-bottom: 8rpx;
}

/* 食品标签 */
.food-tag-compact {
	display: inline-flex;
	align-items: center;
	background-color: #fde7e2;
	color: #ff5f15;
	font-size: 22rpx;
	padding: 4rpx 10rpx;
	border-radius: 6rpx;
	font-weight: 500;
	flex-shrink: 0;
}

/* 剩余份数标签 */
.coupon-count-container {
	display: flex;
	align-items: center;
	flex-shrink: 0;
}

.brand-icon1 {
	margin: 0;
	padding: 0;
	margin-right: 10rpx;
	display: flex;
	align-items: center;
}

.coupon-count {
	font-size: 22rpx;
	color: #ff5f15;
	padding: 4rpx 10rpx;
	/* background-color: rgba(255, 95, 21, 0.08); */
	border-radius: 4rpx;
	display: inline-block;
}

/* 食品名称 */
.food-title-area {
	margin-bottom: 12rpx;
	width: 100%;
}

.food-name-compact {
	font-size: 26rpx;
	color: #333;
	/* font-weight: 500; */
	line-height: 1.4;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* 价格信息和按钮 */
.food-footer-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 50rpx;
}

.price-discount-group {
	display: flex;
	align-items: center;
}

.current-price {
	font-size: 32rpx;
	color: #ff5f15;
	font-weight: bold;
	margin-right: 8rpx;
}

.original-price {
	font-size: 22rpx;
	color: #999;
	text-decoration: line-through;
	margin-right: 10rpx;
}

.discount-tag-compact {
	display: flex;
	align-items: center;
	border: 1rpx solid #ff5f15;
	border-radius: 6rpx;
	background-color: rgba(255, 95, 21, 0.08);
	padding: 2rpx 8rpx;
}

.discount-tag-compact text {
	font-size: 20rpx;
	color: #ff5f15;
}

.buy-button {
	background: linear-gradient(to right, #ff7a45, #ff5f15);
	color: #fff;
	font-size: 24rpx;
	padding: 6rpx 24rpx;
	border-radius: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(255, 95, 21, 0.3);
	transition: transform 0.2s;
}

.buy-button-hover {
	transform: scale(0.95);
	opacity: 0.9;
}

.soldout {
	background: #b6b6b6 !important;
	box-shadow: none;
}

/* 空状态样式 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.empty-text {
	font-size: 26rpx;
	color: #999;
	margin-top: 16rpx;
}

.group-avatar {
	margin-right: 24rpx;
}

.group-content {
	width: 566rpx;
}

.quantity-info {
	position: absolute;
	right: 20rpx;
	bottom: 26px;
	font-size: 24rpx;
	color: #ff5f15;
}

.block {
	color: var(--td-text-color-secondary);
	display: flex;
	align-items: center;
	justify-content: center;
}

.block--center {
	width: 480rpx;
	height: 120rpx;
}

/* back to top button size */
/* .t-back-top--round,
.t-back-top--round-dark {
	width: 68rpx !important;
	height: 68rpx !important;
}
.t-back-top__text--half-round,
.t-back-top__text--half-round-dark,
.t-back-top__text--round,
.t-back-top__text--round-dark {
	font-size: var(--td-font-size, 16rpx) !important;
	line-height: 24rpx !important;
}
.t-back-top__icon {
	font-size: 30rpx !important;
} */
/* back to top button size */

/* 骨架屏样式 */
.skeleton-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
