<wxs module="filter" src="../../utils/util.wxs"></wxs>

<t-pull-down-refresh
  value="{{enable}}"
  loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新']}}"
  usingCustomNavbar
  t-class="custom-pull-refresh"
  bind:refresh="onPullDownRefresh"
  bind:scrolltolower="onReachBottom">

  <view style="padding-top: {{statusBarHeight}}px;" class="nav-bar">
    <!--标题内容-->
    <!-- <t-sticky offset-top="47"> -->
    <view class="navbar-addr" catchtap="chooseLocation">
      <t-icon prefix="wr" name="location" size="36rpx" style="color:#bbb" />
      {{location.name || location.address || "请选择地址"}}
      <t-icon prefix="" name="chevron-down" size="36rpx" style="color:#bbb" />
    </view>
    <!-- </t-sticky> -->

    <!-- <view class="nav-bar-content">
      <slot>
        <view class="nav-bar-title">优探生活</view>
      </slot>
    </view> -->
  </view>

  <view wx:if="{{isFixed1}}" class="overlay-when-fixed">
  </view>

  <view style="text-align: center; color: #b9b9b9" wx:if="{{pageLoading}}">
    <t-loading theme="circular" size="40rpx" text="加载中..." inherit-color />
  </view>
  <view class="home-page-header">
    <view class="search" bind:tap="navToSearchPage">
      <t-search t-class-input="t-search__input" t-class-input-container="t-search__input-container" placeholder="曾宴"
        leftIcon="">
        <t-icon slot="left-icon" prefix="wr" name="search" size="40rpx" color="#bbb" />
      </t-search>
    </view>

    <!-- 视频播放区域 -->
    <view class="video-container">
      <video
        src="https://636c-cloud1-0gpy573m8caa7db3-1321286342.tcb.qcloud.la/app/videos/588cd9882f133d1d3fd8bcc57e6ffbdc.mp4?sign=963ef30c4c791cb72beba6845fddedb0&t=1750904687"
        controls="{{true}}"
        autoplay="{{false}}"
        loop="{{false}}"
        muted="{{false}}"
        show-center-play-btn="{{true}}"
        show-play-btn="{{true}}"
        show-fullscreen-btn="{{true}}"
        object-fit="contain"
        poster=""
        class="video-player">
      </video>
    </view>

    <view class="swiper-wrap" wx:if="{{!miniProgram.version}}">
      <t-swiper wx:if="{{imgSrcs.length > 0}}" current="{{current}}" autoplay="{{autoplay}}" duration="{{duration}}"
        interval="{{interval}}" navigation="{{navigation}}" imageProps="{{swiperImageProps}}" list="{{imgSrcs}}"
        bind:click="navToActivityDetail" />
    </view>
  </view>


  <view class="home-page-container">
    <!-- <t-sticky offset-top="97" bind:scroll="onSroll"> -->
    <view class="home-page-tabs">
      <t-tabs t-class="t-tabs" t-class-active="tabs-external__active" t-class-item="tabs-external__item"
        defaultValue="{{0}}" space-evenly="{{false}}" bind:change="onTabChange">
        <t-tab-panel wx:for="{{categories}}" wx:for-index="index" wx:key="_id" label="{{item.name}}"
          value="{{item._id}}" />
      </t-tabs>
    </view>
    <!-- </t-sticky> -->

    <!-- <goods-list
    wr-class="goods-list-container"
    goodsList="{{goodsList}}"
    bind:click="goodListClickHandle"
    bind:addcart="goodListAddCartHandle"
  /> -->

    <view class="container">
      <!-- 顶部加载状态 -->
      <!-- <view class="loading-container" wx:if="{{shopsLoadStatus===1}}">
        <t-loading theme="circular" size="48rpx" text="数据加载中..." />
      </view> -->

      <view class="store-list">
        <block wx:for="{{shops_home}}" wx:key="_id" wx:for-item="shop" wx:if="{{shops_home.length > 0}}">
          <!-- 使用店铺卡片组件 -->
          <shop-card
            shop="{{shop}}"
            isUpdatingStock="{{isUpdatingStock}}"
            miniProgram="{{miniProgram}}"
            rowColsImage="{{rowColsImage}}"
            rowColsContent="{{rowColsContent}}"
            bind:tapstore="handleTapStore"
            bind:tapproduct="handleTapProduct">
          </shop-card>
        </block>

        <!-- 加载中的骨架屏 - 统一的加载状态显示 -->
        <block wx:if="{{shopsLoadStatus === 1}}">
          <view class="skeleton-card" wx:for="{{[1,2,3]}}" wx:key="*this">
            <t-skeleton
              theme="paragraph"
              row="3"
              row-width="{{['100%', '80%', '60%']}}"
              animation="gradient">
            </t-skeleton>
          </view>
        </block>
      </view>

      <!-- 空状态 - 只有在确实没有数据且加载完成时才显示 -->
      <view class="empty-state" wx:if="{{shops_home.length === 0 && shopsLoadStatus === 2}}">
        <t-icon name="shop" size="80rpx" color="#cccccc" />
        <text class="empty-text">没有更多了</text>
      </view>

      <load-more list-is-empty="{{!shops_home.length}}" status="{{shopsLoadStatus}}" bind:retry="onReTry" />
    </view>

    <t-toast id="t-toast" />

    <t-popup visible="{{false}}" usingCustomNavbar bind:visible-change="onVisibleChange" placement="{{'center'}}">
      <view class="block block--center">在你所在的区域暂未开放</view>
    </t-popup>

  </view>
</t-pull-down-refresh>


<t-back-top visibility-height="300px" style="right:20rpx; bottom: 200px;" theme="{{backTopTheme}}"
  text="{{backTopText}}" scroll-top="{{scrollTop}}" bind:to-top="onToTop"
  wx:if="{{showBackTop && isAndroid}}"></t-back-top>