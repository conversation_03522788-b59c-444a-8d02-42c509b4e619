// pages/test/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    goodsList: [
      {
        id: 1,
        thumb: 'https://placehold.co/20x40',
        title: '拾伍贰食·小…',
        tags: ['1.4折'],
        price: 25,
        originPrice: 168,
      },
      {
        id: 2,
        thumb: 'https://placehold.co/20x40',
        title: '八两半斤·中…',
        tags: ['全额返'],
        price: 0,
        originPrice: 88,
      },
      {
        id: 3,
        thumb: 'https://placehold.co/26x26',
        title: '單單记螺蛳單單记螺蛳…',
        tags: ['0.9折'],
        price: 8,
        originPrice: 81,
      },
      {
        id: 4,
        thumb: 'https://placehold.co/26x26',
        title: '<PERSON><PERSON><PERSON>私<PERSON><PERSON><PERSON>私…',
        tags: ['1.5折'],
        price: 39.9,
        originPrice: 249.9,
      },
      {
        id: 5,
        thumb: 'https://placehold.co/26x26',
        title: '<PERSON><PERSON><PERSON>私<PERSON><PERSON><PERSON>私…',
        tags: ['1.5折'],
        price: 39.9,
        originPrice: 249.9,
      },
      {
        id: 6,
        thumb: 'https://placehold.co/26x26',
        title: '<PERSON><PERSON><PERSON>私<PERSON><PERSON><PERSON>私…',
        tags: ['2折'],
        price: 39.9,
        originPrice: 249.9,
      },
      {
        id: 7,
        thumb: 'https://placehold.co/26x26',
        title: 'UncleHan私UncleHan私…',
        tags: ['2折'],
        price: 39.9,
        originPrice: 249.9,
      },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  onTapSwiperItem(e) {
    console.log(e);
  }
})