.card {
  width: 25vw !important;
  min-width: 25vw !important;
  max-width: 25vw !important;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card__img {
  width: 80%;
  height: 140rpx;
  object-fit: cover;
  border-radius: 16rpx;
}
.card__info {
  width: 80%;
  padding: 16rpx;
}
.card__title {
  font-size: 20rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  height: 56rpx;
  overflow: hidden;
}
.card__tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 8rpx;
}
.card__tag {
  font-size: 20rpx;
  color: #fa4126;
  border: 1rpx solid #fa4126;
  border-radius: 12rpx;
  padding: 0 8rpx;
  margin-right: 8rpx;
}
.card__price-row {
  display: flex;
  align-items: baseline;
}
.card__price {
  color: #fa4126;
  font-size: 32rpx;
  font-weight: bold;
}
.card__origin-price {
  color: #bbb;
  font-size: 24rpx;
  margin-left: 12rpx;
  text-decoration: line-through;
} 