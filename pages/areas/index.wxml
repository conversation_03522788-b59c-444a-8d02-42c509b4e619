<import src="index.skeleton.wxml" />


<t-pull-down-refresh value="{{enable}}" loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}" usingCustomNavbar
  bind:refresh="onPullDownRefresh">

  <template is="skeleton" wx:if="{{loading}}" />

  <view class="areas-container" wx:if="{{!loading}}">
    <t-search value="{{searchValue}}" placeholder="商圈,街道,地区" center bind:tap="chooseLocation" />

    <view class="location-section">
      <view class="location-header">
        <t-icon name="location" size="44rpx" />
        <text class="location-text">{{location.name || location.address}}</text>
        <text class="refresh-text" bindtap="refreshLocation">重新定位</text>
      </view>
    </view>

    <view class="nearby-section">
      <view class="section-title">附近商圈</view>
      <view class="tag-group">
        <t-tag wx:for="{{nearbyAreas}}" wx:key="index" class="area-tag" variant="light" bindtap="onTapArea"
          data-area="{{item}}">{{item.name}}</t-tag>
      </view>
    </view>

    <view class="hot-section">
      <view class="section-title">热门商圈</view>
      <view class="tag-group">
        <t-tag wx:for="{{hotAreas}}" wx:key="index" class="area-tag" variant="light" bindtap="onTapArea"
          data-area="{{item}}">{{item.name}}</t-tag>
      </view>
    </view>
  </view>

  <t-toast id="t-toast" />
</t-pull-down-refresh>