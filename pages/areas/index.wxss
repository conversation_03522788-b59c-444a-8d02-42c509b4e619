page {
	background-color: #fff;
}

.areas-container {
	padding: 20rpx;
}

.areas-container .t-search {
	margin-bottom: 20rpx;
}

.location-section {
	margin-bottom: 30rpx;
}

.location-header {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
}

.location-text {
	margin-left: 10rpx;
	font-size: 28rpx;
	color: #333;
}

.refresh-text {
	margin-left: auto;
	font-size: 26rpx;
	color: #0052d9;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 42rpx;
}

.tag-group {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.area-tag {
	margin-bottom: 16rpx;
}

.nearby-section,
.hot-section {
	margin-bottom: 40rpx;
}

.t-toast {
	--td-toast-max-width: 800rpx;
}
