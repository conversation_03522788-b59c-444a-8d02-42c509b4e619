import { createStoreBindings } from "mobx-miniprogram-bindings";
import { locationStore } from "../../stores/locationStore";
import Toast from "tdesign-miniprogram/toast/index";
import { log } from "../../utils/log";

Page({
	data: {
		searchValue: "",
		currentLocation: "",
		nearbyAreas: [
			{ id: 1, name: "后湖" },
			{ id: 2, name: "徐东" },
			{ id: 3, name: "司门口" },
			{ id: 4, name: "虎泉" },
			{ id: 5, name: "经开万达" },
			{ id: 6, name: "武汉广场" },
		],
		hotAreas: [
			{ id: 7, name: "王家湾" },
			{ id: 8, name: "武汉天地" },
			{ id: 9, name: "街道口" },
			{ id: 10, name: "光谷步行街" },
			{ id: 11, name: "中南路" },
			{ id: 12, name: "汉街·万达广场" },
			{ id: 13, name: "江汉步行街" },
			{ id: 14, name: "武汉广场" },
		],
		loading: true,
	},

	onLoad() {
		// 将 store 绑定到页面
		this.locationStoreBindings = createStoreBindings(this, {
			store: locationStore,
			fields: ["location", "nearbyAreas", "hotAreas"], // 需要绑定的字段
			actions: ["getNearbyAreas", "getHotAreas", "geocoder", "setLocation"], // 需要绑定的 actions
		});
		console.debug("onload page/areas/index");
		this.loadData();
	},

	onUnload() {
		this.locationStoreBindings.destroyStoreBindings();
	},

	loadData() {
		this.locate();
		this.setData({
			loading: false,
		});
	},

	locate() {
		log.debug("wx.getLocation");
		const that = this;
		wx.getLocation({
			type: "gcj02",
			async success(res) {
				log.debug("地理位置:", res);
				// await locationStore.geocoder({
				// 	location: `${res.latitude},${res.longitude}`,
				// });

				// Using async/await
				// try {
				//   const location = await locationStore.geocoder(params);
				//   console.log('Got location:', location);
				// } catch (error) {
				//   console.error('Geocoding failed:', error);
				// }

				// Or using .then()/.catch()
				locationStore
					.geocoder({
						location: `${res.latitude},${res.longitude}`,
					})
					.then((loc) => {
						console.log("Got location:", loc);
						if (loc.address_component?.city !== "武汉市") {
							Toast({
								context: that,
								selector: "#t-toast",
								message: "不在服务范围内",
								duration: 5000,
							});
							that.setData({
								loading: true,
							});
							return;
						}
					})
					.catch((error) => {
						console.error("Geocoding failed:", error);
						Toast({
							context: that,
							selector: "#t-toast",
							message: "获取位置失败，请检查定位服务是否正常",
							duration: 5000,
						});
						that.setData({
							loading: true,
						});
						return;
					});

				// 如果正常就加载商圈
				that.setData({
					loading: false,
				});
				that.getNearbyAreas();
				that.getHotAreas();

				// if (locationStore.location.address_component?.city !== "武汉市") {
				// 	Toast({
				// 		context: that,
				// 		selector: "#t-toast",
				// 		message: "不在服务范围内",
				// 		duration: 5000,
				// 	});
				// 	that.setData({
				// 		loading: true,
				// 	});
				// 	return;
				// } else {
				// 	that.setData(
				// 		{
				// 			loading: false,
				// 		},
				// 		() => {
				// 			wx.hideLoading();
				// 			that.getNearbyAreas();
				// 			that.getHotAreas();
				// 		},
				// 	);
				// }
			},
			fail(err) {
				log.error("获取地理位置失败:", err);
				if (err.errMsg === "getLocation:fail auth deny") {
					log.error("getLocation:fail auth deny");
					Toast({
						context: that,
						selector: "#t-toast",
						message: "请检查定位服务是否正常",
						duration: 5000,
					});
				}
			},
		});
	},

	chooseLocation() {
		wx.chooseLocation({
			// latitude: res.latitude,  // 设置地图中心点为当前位置
			// longitude: res.longitude,
			success: (res) => {
				console.debug("wx.chooseLocation success:", res);
				if (res.errMsg === "chooseLocation:ok") {
					const location = {
						name: res.name,
						address: res.address,
						latitude: res.latitude,
						longitude: res.longitude,
					};
					console.debug("LOCATION:", location);
					// this.setData({
					//   location: location
					// })
					this.setLocation(location);
					wx.setStorageSync("dgx-location", location);

					// this.geocoder();
					locationStore
						.geocoder({
							location: `${res.latitude},${res.longitude}`,
						})
						.then((loc) => {
							console.log("Got location:", loc);
							if (loc.address_component?.city !== "武汉市") {
								Toast({
									context: that,
									selector: "#t-toast",
									message: "不在服务范围内",
									duration: 5000,
								});
								that.setData({
									loading: true,
								});
								return;
							}
							wx.navigateBack({
								delta: 2,
								success() {
									const pages = getCurrentPages();
									const prevPage = pages[pages.length - 1];
									if (prevPage.route === "pages/home/<USER>") {
										prevPage.setData({ backRefresh: true });
									}
								},
							});
							// wx.switchTab({
							// 	url: "/pages/home/<USER>",
							// });
							return;
						})
						.catch((error) => {
							console.error("Geocoding failed:", error);
							Toast({
								context: that,
								selector: "#t-toast",
								message: "获取位置失败，请检查定位服务是否正常",
								duration: 5000,
							});
							that.setData({
								loading: true,
							});
							return;
						});
					// this.setPageNumber(1)
					// this.setTotal(0)
					// this.resetShops()
					// setTimeout(() => {
					// 	this.loadNearestShops(this.data.selectedCategory);
					// }, 300)

					// setTimeout(async () => {
					// 	console.debug(this.data.selectedCategory)
					// 	// this.init();
					// 	await this.loadNearestShops(this.data.selectedCategory);
					// }, 1000)
				}
			},
			fail: (err) => {
				console.debug("wx.chooseLocation fail:", err);
			},
		});
	},

	refreshLocation() {
		wx.getLocation({
			type: "gcj02",
			async success(res) {
				wx.setStorageSync("dgx-location", {
					longitude: res.longitude,
					latitude: res.latitude,
				});
				console.debug("locationStore.locate, res", res);
				await locationStore.geocoder({
					location: `${res.latitude},${res.longitude}`,
				});
				wx.showToast({
					title: "位置已更新",
					icon: "success",
				});
				wx.switchTab({
					url: "/pages/home/<USER>",
				});
			},
			fail(err) {
				console.error("获取地理位置失败:", err);
			},
		});
	},

	onTapArea(e) {
		const area = e.currentTarget.dataset.area;
		console.debug(area);
		// 实际项目中这里应该跳转到对应的商圈详情页

		wx.showToast({
			title: `选择了${area.name}`,
			icon: "none",
		});
		var loc = wx.getStorageSync("dgx-location");
		console.debug(loc);

		var city = loc.address_component?.city;
		if (city !== "武汉市") {
			Toast({
				context: this,
				selector: "#t-toast",
				message: "不在服务范围内",
				duration: 5000,
			});
			return;
		}
		loc.latitude = area.latitude;
		loc.longitude = area.longitude;
		loc.name = area.name;

		// 添加标记，表示这是手动选择的位置
		loc.isManuallySelected = true;
		loc.manuallySelectedTime = Date.now();

		wx.setStorageSync("dgx-location", loc);

		// 同时更新 locationStore，确保 MobX 绑定能收到更新
		this.setLocation(loc);

		// 立即返回，减少等待时间
		wx.navigateBack({
			delta: 1,
			success() {
				const pages = getCurrentPages();
				const prevPage = pages[pages.length - 1];
				if (prevPage.route === "pages/home/<USER>") {
					// 设置刷新标志，并记录选择时间
					prevPage.setData({
						backRefresh: true,
						lastLocationSelectTime: Date.now()
					});

					// 立即触发数据加载，不等待onShow
					setTimeout(() => {
						if (prevPage.loadNearestShops) {
							console.debug("商圈选择后立即开始加载数据");
							prevPage.setPageNumber(1);
							prevPage.setTotal(0);
							prevPage.resetShops();
							prevPage.loadNearestShops(prevPage.data.selectedCategory || "0", "tandian1");
						}
					}, 100);
				}
			},
		});
		return;
	},

	async onPullDownRefresh() {
		this.setData({ enable: true });
		setTimeout(() => {
			this.setData({ enable: false });
		}, 1000);
		// await this.loadData();
		this.locate();
	},
});
