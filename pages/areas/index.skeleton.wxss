/*
此文件为开发者工具生成，生成时间: 5/19/2025, 9:20:25 PM

在 /Users/<USER>/code/dagexian/tandian-user/pages/areas/index.wxss 中引入样式
```
@import "./index.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
*/
.sk-transparent {
    color: transparent !important;
  }
.sk-text-14-2857-86 {
    background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
    background-size: 100% 38.2282rpx;
    position: relative !important;
  }
.sk-text {
    background-origin: content-box !important;
    background-clip: content-box !important;
    background-color: transparent !important;
    color: transparent !important;
    background-repeat: repeat-y !important;
  }
.sk-text-14-2857-660 {
    background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
    background-size: 100% 35.6796rpx;
    position: relative !important;
  }
.sk-text-14-2857-381 {
    background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
    background-size: 100% 43.3252rpx;
    position: relative !important;
  }
.sk-text-25-0000-823 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-71 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-555 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-168 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-51 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-133 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-769 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-878 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-705 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-745 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-14-2857-489 {
    background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
    background-size: 100% 43.3252rpx;
    position: relative !important;
  }
.sk-text-25-0000-749 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-123 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-433 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-931 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-713 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-140 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-903 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-912 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-text-25-0000-334 {
    background-image: linear-gradient(transparent 25.0000%, #EEEEEE 0%, #EEEEEE 75.0000%, transparent 0%) !important;
    background-size: 100% 47.3301rpx;
    position: relative !important;
  }
.sk-pseudo::before, .sk-pseudo::after {
      background: #EFEFEF !important;
      background-image: none !important;
      color: transparent !important;
      border-color: transparent !important;
    }
.sk-pseudo-rect::before, .sk-pseudo-rect::after {
      border-radius: 0 !important;
    }
.sk-pseudo-circle::before, .sk-pseudo-circle::after {
      border-radius: 50% !important;
    }
.sk-image {
    background: #EFEFEF !important;
  }
.sk-container {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: transparent;
  }
