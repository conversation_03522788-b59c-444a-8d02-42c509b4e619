<wxs module="filter" src="../../utils/util.wxs"></wxs>

<t-pull-down-refresh
    value="{{enable}}"
    loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}"
    usingCustomNavbar
    bind:refresh="onPullDownRefresh"
>

<!-- 提示信息 -->
<view class="notice-bar" wx:if="{{showNotice}}">
  <t-icon name="info-circle-filled" size="36rpx" color="#FF9D00" />
  <view class="notice-text">
    <text style="display: block;">1. 请勿在有订单未完成的情况下修改名片，否则会导致订单检测失效; </text>
    <text style="display: block;">2. 严禁一个点评账号或小红书账号绑定到多个微信号上, 我们会定期检测; </text>
    <text style="display: block;">3. 更新名片需要再审核, 认证通过后一个月内仅能更新一次名片，频繁更新会加大审核工作人员的工作量</text>
    <text style="display: block;">4. 提交后我们会48小时内完成审核, 节假日可能会慢点，请谅解</text>
  </view>
</view>

<view class="cards-container">
  <!-- 未认证状态 -->
  <!-- <block wx:if="{{!cards.length}}">
    <view class="user-profile empty-card" bind:tap="navigateToAddDP">
        <image class="avatar" src="cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-**********/app/dazhongdianping.svg" mode="aspectFill"></image>
        <button>添加大众点评名片</button>
    </view>

    <view class="user-profile empty-card" bind:tap="navigateToAddXiaohongshu">
        <image class="avatar" src="cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-**********/app/xiaohongshu.svg" mode="aspectFill"></image>
        <button>添加小红书名片</button>
    </view>
  </block> -->

  <block wx:if="{{!dpCard}}">
    <view class="user-profile empty-card" bind:tap="navigateToAddDP">
      <image class="avatar" src="cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-**********/app/dazhongdianping.svg" mode="aspectFill"></image>
      <view class="add-card-btn">添加大众点评名片</view>
    </view>
  </block>
  <block wx:else>
    <view class="card-info">
      <view class="user-profile">
        <image class="avatar" src="cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-**********/app/dazhongdianping.svg" mode="aspectFill"></image>
        <view class="user-status">
          <view class="status-tag {{dpCard.verified ? 'verified' : 'pending'}}">
            <t-icon name="{{dpCard.verified ? 'check-circle-filled' : 'time'}}" size="28rpx" color="{{dpCard.verified ? '#07C160' : '#FF9D00'}}" />
            <text>{{filter.formatCardStatus(dpCard.verified)}}</text>
          </view>
          <view class="platform-tag">{{dpCard.name}}</view>
          <view class="flex items-center gap10">
            <label class="label">等级: </label>
            <view class="tag">Lv{{dpCard.level || 0}}</view>
          </view>
          <view class="flex items-center gap10">
            <label class="label">粉丝数: </label>
            <view class="tag">{{dpCard.followers_count || 0}}</view>
          </view>
          <view class="flex items-center gap10">
            <label class="label">上次更新时间: </label>
            <view class="tag">{{filter.formatDate(dpCard.updatedAt)}}</view>
          </view>
        </view>
      </view>
      
      <view class="card-actions">
        <button class="action-btn" bindtap="unbindCard" data-card-id="{{dpCard._id}}">解绑名片</button>
        <button class="action-btn update" bindtap="navigateToUpdateDP">更新名片</button>
      </view>
    </view>
  </block>

  <block wx:if="{{!xhsCard}}">
    <view class="user-profile empty-card" bind:tap="navigateToAddXiaohongshu">
      <image class="avatar" src="cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-**********/app/xiaohongshu.svg" mode="aspectFill"></image>
      <view class="add-card-btn">添加小红书名片</view>
    </view>
  </block>
  <block wx:else>
    <view class="card-info">
      <view class="user-profile">
        <image class="avatar" src="cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-**********/app/xiaohongshu.svg" mode="aspectFill"></image>
        <view class="user-status">
          <view class="status-tag {{xhsCard.verified ? 'verified' : 'pending'}}">
            <t-icon name="{{xhsCard.verified ? 'check-circle-filled' : 'time'}}" size="28rpx" color="{{xhsCard.verified ? '#07C160' : '#FF9D00'}}" />
            <text>{{filter.formatCardStatus(xhsCard.verified)}}</text>
          </view>
          <view class="platform-tag">{{xhsCard.name}}</view>
          <!-- <view class="flex items-center gap10">
            <label class="label">等级: </label>
            <view class="tag">Lv{{xhsCard.level || 0}}</view>
          </view> -->
          <view class="flex items-center gap10">
            <label class="label">粉丝数: </label>
            <view class="tag">{{xhsCard.followers_count || 0}}</view>
          </view>
          <view class="flex items-center gap10">
            <label class="label">上次更新时间: </label>
            <view class="tag">{{filter.formatDate(xhsCard.updatedAt)}}</view>
          </view>
        </view>
      </view>
      
      <view class="card-actions">
        <button class="action-btn" bindtap="unbindCard" data-card-id="{{xhsCard._id}}">解绑名片</button>
        <button class="action-btn update" bindtap="navigateToUpdateXiaohongshu">更新名片</button>
      </view>
    </view>
  </block>
  
  <!-- <block>
    <view class="card-info" wx:for="{{cards}}" wx:key="_id">
      <view class="user-profile">
        <image class="avatar" src="cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-**********/app/xiaohongshu.svg" mode="aspectFill"></image>
        <image class="avatar" src="cloud://cloud1-0gpy573m8caa7db3.636c-cloud1-0gpy573m8caa7db3-**********/app/dazhongdianping.svg" mode="aspectFill"></image>
        <view class="user-status">
          <view class="status-tag {{item.verified ? 'verified' : 'pending'}}">
            <t-icon name="{{item.verified ? 'check-circle-filled' : 'time'}}" size="28rpx" color="{{item.verified ? '#07C160' : '#FF9D00'}}" />
            <text>{{filter.formatCardStatus(item.verified)}}</text>
          </view>
          <view class="platform-tag">{{item.name}}</view>
          
          <view class="flex items-center gap10">
            <label class="label">等级: </label>
            <view class="tag">Lv{{item.level || 0}}</view>
          </view>
          <view class="flex items-center gap10">
            <label class="label">粉丝数: </label>
            <view class="tag">{{item.followers_count || 0}}</view>
          </view>
        </view>
      </view>
      
      <view class="card-actions">
        <button class="action-btn" bindtap="unbindCard" data-card-id="{{item._id}}">解绑名片</button>
        <button class="action-btn update" bindtap="navigateToAddCard">更新名片</button>
      </view>
    </view>
  </block> -->

  <!-- 底部添加名片按钮 -->
  <!-- <view class="action-button" wx:if="{{!hasCard}}"> -->
 
</view>

<!-- <view class="action-button">
  <t-button theme="primary" size="large" block bindtap="navigateToAddCard">添加名片</t-button>
</view> -->
<t-toast id="t-toast" />
<!-- <t-fab
  icon="gesture-press"
  text="规则"
  aria-label="规则"
  usingCustomNavbar
  y-bounds="{{[0, 48]}}"
  bind:click="handleClick"
></t-fab> -->

</t-pull-down-refresh>
