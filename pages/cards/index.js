import { createStoreBindings } from "mobx-miniprogram-bindings";
import { cardStore } from "../../stores/cardStore";
import { Toast } from "tdesign-miniprogram";
import dayjs from "dayjs";

Page({
	data: {
		showNotice: true,
		hasCard: false,
		userInfo: {
			avatarUrl:
				"https://636c-cloud1-0gpy573m8caa7db3-1321286342.tcb.qcloud.la/app/default-avatar.svg",
			level: 0,
		},
	},

	onLoad: function (options) {
		// // 加载用户名片信息
		// this.loadCardInfo();

		// 将 store 绑定到页面
		this.storeBindings = createStoreBindings(this, {
			store: cardStore,
			fields: ["cards", "dpCard", "xhsCard"], // 需要绑定的字段
			actions: ["fetchCards", "delete"], // 需要绑定的 actions
		});

		// this.fetchCards();
	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {
		// 解绑 store
		this.storeBindings.destroyStoreBindings();
	},

	onShow: async function () {
		// // 每次页面显示时重新加载名片信息
		this.loadData();
	},

	async onPullDownRefresh() {
		this.setData({ enable: true });
		setTimeout(() => {
			this.setData({ enable: false });
		}, 1000);

		await this.loadData();
	},

	async loadData() {
		console.log("开始加载数据");
		const response = await this.fetchCards();
		console.log("获取到的数据:", response);
		if (response.data.records) {
			const cards = response.data.records;
			const dpCard = cards.find((x) => x.platform === "dianping");
			const xhsCard = cards.find((x) => x.platform === "xiaohongshu");
			console.debug("处理后的数据:", { dpCard, xhsCard });
			this.setData(
				{
					dpCard: dpCard || null,
					xhsCard: xhsCard || null,
				},
				() => {
					console.log("数据更新完成");
				},
			);
		}
	},

	onReady() {},

	navigateToAddDP() {
		wx.navigateTo({
			url: "/pages/cards/add/index?platform=dianping",
		});
	},

	navigateToAddXiaohongshu() {
		wx.navigateTo({
			url: "/pages/cards/add/index?platform=xiaohongshu",
		});
	},

	navigateToUpdateDP() {
		if (this.data.dpCard) {
			if (
				dayjs(this.data.dpCard.updatedAt).isBefore(dayjs().subtract(1, "month"))
			) {
				wx.navigateTo({
					url: "/pages/cards/add/index?platform=dianping&isUpdate=true",
				});
				return;
			} else {
				wx.showToast({
					title: "你近期更新过名片，请勿太频繁更新",
					icon: "none",
					duration: 2000,
				});
				return;
			}
		}
	},

	navigateToUpdateXiaohongshu() {
		if (this.data.xhsCard) {
			if (
				dayjs(this.data.xhsCard.updatedAt).isBefore(
					dayjs().subtract(1, "month"),
				)
			) {
				wx.navigateTo({
					url: "/pages/cards/add/index?platform=xiaohongshu&isUpdate=true",
				});
				return;
			} else {
				wx.showToast({
					title: "你近期更新过名片，请勿太频繁更新",
					icon: "none",
					duration: 2000,
				});
				return;
			}
		}
	},

	// 解绑名片
	unbindCard(e) {
		Toast({
			context: this,
			selector: "#t-toast",
			message: "请联系客服",
		});
		// wx.showModal({
		//   title: '提示',
		//   content: '确定要解绑名片吗？',
		//   success: async (res) => {
		//     if (res.confirm) {
		//       console.debug(e)
		//       const cardId = e.currentTarget.dataset.cardId;
		//       console.debug("cardId", cardId)
		//       const res = await this.delete(cardId);
		//       console.debug(res)
		//       await this.loadData();
		//       if (res.count > 0) {
		//         wx.showToast({
		//           title: '解绑成功',
		//           icon: 'success',
		//           duration: 2000
		//         })
		//       }
		//     }
		//   }
		// });
	},
});
