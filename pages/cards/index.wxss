page {
	background-color: #f5f5f5;
}

.cards-container {
	width: 100%;
	/* min-height: 100vh; */
	display: flex;
	flex-direction: column;
	padding-bottom: 220rpx;
	padding-top: 20rpx;
}

/* 提示信息样式 */
.notice-bar {
	display: flex;
	/* align-items: center; */
	background-color: #fff9e6;
	padding: 20rpx 30rpx;
	margin-bottom: 20rpx;
	/* position: fixed; */
	z-index: 999;
}

.notice-text {
	font-size: 26rpx;
	color: #ff9d00;
	margin-left: 10rpx;
	line-height: 1.4;
}

/* 空状态样式 */
.empty-state {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 60rpx 0;
}

/* 已认证状态样式 */
.card-info {
	background-color: #fff;
	border-radius: 12rpx;
	margin: 30rpx;
	padding: 30rpx;
	position: relative;
}

.user-profile {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.avatar {
	width: 120rpx;
	height: 120rpx;
	margin-right: 20rpx;
}

.user-status {
	display: flex;
	flex-direction: column;
}

.status-tag {
	position: absolute;
	top: 30rpx;
	right: 30rpx;
	display: flex;
	align-items: center;
	gap: 4rpx;
	padding: 4rpx 12rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	z-index: 1;
}

.status-tag.verified {
	background-color: rgba(7, 193, 96, 0.1);
}

.status-tag.pending {
	background-color: rgba(255, 157, 0, 0.1);
}

.status-tag text {
	color: #333;
}

.level-tag {
	background-color: #ffd700;
	color: #fff;
	font-size: 24rpx;
	padding: 4rpx 16rpx;
	border-radius: 20rpx;
	width: fit-content;
}

.card-actions {
	display: flex;
	justify-content: space-between;
	margin-top: 20rpx;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	border: 1px solid #ddd;
	border-radius: 40rpx;
	font-size: 28rpx;
	color: #333;
	background-color: #fff;
	margin: 0 10rpx;
}

.action-btn.update {
	background-color: #fff;
	color: #fa550f;
	border-color: #fa550f;
}

/* 底部按钮样式 */
.action-button {
	position: fixed;
	bottom: 60rpx;
	left: 0;
	right: 0;
	padding: 20rpx 30rpx;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
/* 
.action-button {
  padding: 30rpx;
}  */

.flex {
	display: flex;
}

.gap10 {
	gap: 10rpx;
}

.items-center {
	align-items: center;
}

.label {
	display: flex;
	font-size: 24rpx;
}

.tag {
	display: flex;
}

.level-tag {
	display: flex;
}

.empty-card {
	margin-left: 20rpx;
	margin-right: 20rpx;
	border: 1px solid #e8dcdc;
	padding: 10px;
	margin-bottom: 60rpx;
	margin-top: 60rpx;
	background: #ffff;
	border-radius: 15rpx;
}

.add-card-btn {
	width: 100%;
	text-align: center;
	line-height: 80rpx;
	font-size: 28rpx;
	color: #333;
	background-color: #fff;
	margin: 0 10rpx;
}
