import { generateRandomFileName, getFileExtension } from "../../../utils/util";
import { createStoreBindings } from "mobx-miniprogram-bindings";
import { cardStore } from "../../../stores/cardStore";

Page({
	data: {
		// hasVerifiedCard: false,
		selectedPlatform: "xiaohongshu", // 默认选择小红书
		// cardLink: '', // 小红书链接
		// cardImage: '', // 小红书截图
		// dianpingLink: '', // 大众点评链接
		// dianpingImage: '', // 大众点评截图
		isUpdate: false, // 是否为更新操作
		// pageTitle: '添加名片', // 页面标题
		// image: '',
		// url: '',
		// fileID: '',
		// nickname: '',
		card: {
			name: "",
			level: null,
			followers_count: null,
			url: "",
			fileID: null,
			platform: null,
		},
		disabled: true,
		submitting: false,
	},

	onLoad: async function (options) {
		// 将 store 绑定到页面
		this.storeBindings = createStoreBindings(this, {
			store: cardStore,
			fields: ["cards", "dpCard", "xhsCard"], // 需要绑定的字段
			actions: ["fetchCards", "delete"], // 需要绑定的 actions
		});

		console.debug(options);
		if (options.platform) {
			this.setData({
				"card.platform": options.platform,
				selectedPlatform: options.platform,
			});
		}
		if (options.isUpdate) {
			this.setData({
				isUpdate: true,
				disabled: false,
			});
			wx.setNavigationBarTitle({
				title:
					options.platform === "xiaohongshu"
						? "更新小红书名片"
						: "更新大众点评名片",
			});
			await this.fetchCards();
		}
	},

	onReady() {
		this.setData({
			card: this.data.cards.find(
				(x) => x.platform === this.data.selectedPlatform,
			),
		});
	},

	onInput(e) {
		console.debug(e.currentTarget.dataset.name);
		console.debug(e.detail.value);
		const field = `card.${e.currentTarget.dataset.name}`;
		console.debug(field);
		this.setData({
			[field]: e.detail.value,
		});
	},

	onInputNumber(e) {
		console.debug(e.currentTarget.dataset.name);
		console.debug(e.detail.value);
		const field = `card.${e.currentTarget.dataset.name}`;
		console.debug(field);
		const value = e.detail.value;
		const num = value.match(/\d+(\.\d+)?/)?.[0];
		this.setData({
			[field]: num,
		});
	},

	// // 选择认证平台
	// selectPlatform(e) {
	//   const platform = e.currentTarget.dataset.platform;
	//   console.debug(platform);
	//   this.setData({
	//     selectedPlatform: platform
	//   });
	// },

	chooseImage() {
		wx.chooseMedia({
			count: 1,
			mediaType: ["image"],
			sourceType: ["album", "camera"],
			success: (res) => {
				const tempFilePath = res.tempFiles[0].tempFilePath;
				this.setData({
					cardImage: tempFilePath,
				});

				// 获取文件后缀
				const fileExtension = getFileExtension(tempFilePath);
				console.log("文件后缀:", fileExtension);
				// 生成随机文件名并保留后缀
				const randomFileName = `${generateRandomFileName()}.${fileExtension}`;
				const cloudPath = `cards/${randomFileName}`; // 云存储路径

				// 将图片上传至云存储空间
				wx.cloud.uploadFile({
					// 指定上传到的云路径
					cloudPath: cloudPath,
					// 指定要上传的文件的小程序临时文件路径
					filePath: tempFilePath,
					// 成功回调
					success: (res) => {
						console.log("上传成功", res);
						if (res.statusCode === 204) {
							this.setData({
								fileID: res.fileID,
								"card.fileID": res.fileID,
								disabled: false,
							});
						}
					},
				});
			},
		});
	},

	openImageViewer1() {
		this.setData({
			visible: true,
			imagesToPreview: [
				"https://636c-cloud1-0gpy573m8caa7db3-1321286342.tcb.qcloud.la/app/xiaohongshu_example.jpg",
			],
		});
	},

	openImageViewer2() {
		this.setData({
			visible: true,
			imagesToPreview: [
				"https://636c-cloud1-0gpy573m8caa7db3-1321286342.tcb.qcloud.la/app/dzdp_example.jpg",
			],
		});
	},

	onCloseImageViewer(e) {
		const { trigger } = e.detail;
		console.log(trigger);
		this.setData({
			visible: false,
		});
	},

	// 显示帮助信息
	showHelpInfo() {
		const { selectedPlatform } = this.data;
		let title = "";
		let content = "";

		if (selectedPlatform === "xiaohongshu") {
			title = "如何认证小红书平台";
			content =
				'小红书认证步骤：\n1. 打开小红书APP\n2. 进入个人主页\n3. 点击右上角"..."按钮\n4. 选择"分享"，复制链接\n5. 将链接粘贴到输入框\n6. 截图个人主页并上传';
		} else if (selectedPlatform === "dianping") {
			title = "如何认证大众点评平台";
			content =
				'大众点评认证步骤：\n1. 打开大众点评APP\n2. 点击底部"我的"\n3. 进入个人主页\n4. 点击"分享"按钮\n5. 复制链接并粘贴到输入框\n6. 截图个人主页并上传';
		}

		wx.showModal({
			title: title,
			content: content,
			showCancel: false,
		});
	},

	// 提交名片
	async submitCard() {
		if (this.data.submitting) {
			return;
		}

		this.setData({
			submitting: true,
		});

		const { name, level, followers_count, url, fileID, platform, image } =
			this.data.card;
		if (!name) {
			wx.showToast({
				title: "昵称必填",
				icon: "error",
			});
			this.setData({
				submitting: false,
			});
			return;
		}
		if (platform === "dianping" && !level) {
			wx.showToast({
				title: "等级必填",
				icon: "error",
			});
			this.setData({
				submitting: false,
			});
			return;
		}
		if (!followers_count) {
			wx.showToast({
				title: "粉丝数必填",
				icon: "error",
			});
			this.setData({
				submitting: false,
			});
			return;
		}
		if (!url) {
			wx.showToast({
				title: "个人主要链接必填",
				icon: "error",
			});
			this.setData({
				submitting: false,
			});
			return;
		}
		if (!fileID) {
			wx.showToast({
				title: "个人主页截图必传",
				icon: "error",
			});
			this.setData({
				submitting: false,
			});
			return;
		}
		// const { selectedPlatform, cardLink, cardImage, dianpingLink, dianpingImage, isUpdate } = this.data;

		// 显示加载中
		wx.showLoading({
			title: "提交中...",
		});

		// const card = {
		//   platform: this.data.selectedPlatform,
		//   url: this.data.url,
		//   image: this.data.image,
		//   fileID: this.data.fileID
		// }
		console.debug("card: ", this.data.card);
		const auth = wx.getStorageSync("dgx-auth");
		if (auth) {
			const userId = auth.user_id;
			// card.user_id = {
			//   _id: userId
			// }
			this.setData({
				"card.user_id": {
					_id: userId,
				},
				"card.platform": this.data.selectedPlatform,
			});
		}

		if (this.data.selectedPlatform === "xiaohongshu") {
			this.setData({
				"card.level": 0,
			});
		}

		if (this.data.isUpdate) {
			const { data } = await cardStore.update(this.data.card);
			console.debug(data);
			if (data.count > 0) {
				wx.hideLoading();
				wx.showToast({
					title: "更新成功",
					icon: "success",
					duration: 2000,
					success: () => {
						// 延迟返回上一页
						setTimeout(() => {
							wx.navigateBack();
						}, 800);
					},
				});
			}
		} else {
			const { data } = await cardStore.add(this.data.card);
			console.debug(data);
			if (data.id) {
				wx.hideLoading();
				wx.showToast({
					title: "提交成功",
					icon: "success",
					duration: 2000,
					success: () => {
						// 延迟返回上一页
						setTimeout(() => {
							// navigateBack的时候刷新列表数据
							// 获取页面栈
							const pages = getCurrentPages();
							// 获取上一页实例
							const prevPage = pages[pages.length - 2];
							// 返回并刷新
							wx.navigateBack({
								delta: 1,
								success: () => {
									// 调用上一页的刷新方法
									prevPage.loadData();
								},
							});
						}, 1000);
					},
				});
			}
		}

		this.setData({
			submitting: false,
		});
		return;
	},
});
