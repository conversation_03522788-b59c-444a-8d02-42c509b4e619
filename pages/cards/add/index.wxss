page {
  background-color: #f5f5f5;
}

.add-card-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 120rpx;
  overflow-y: hidden;
}

/* 通用样式 */
.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding: 0 30rpx;
}

/* 已有认证名片区域 */
.card-status-section {
  background-color: #fff;
  padding: 30rpx 0;
  margin-bottom: 20rpx;
}

.card-status-content {
  padding: 0 30rpx;
}

.empty-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 40rpx 0;
}

.empty-image {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 认证流程 */
.verification-section {
  background-color: #fff;
  padding: 30rpx 0;
  margin-bottom: 20rpx;
}

/* 选项卡样式 */
.platform-tabs {
  display: flex;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  position: relative;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-right: 20rpx;
}

.tab-item.active {
  color: #fa550f;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1rpx;
  left: 20rpx;
  right: 20rpx;
  height: 4rpx;
  background-color: #fa550f;
  border-radius: 2rpx;
}

.help-link {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 30rpx;
  font-size: 26rpx;
  color: #fa550f;
}

/* 上传个人名片链接 */
.link-section {
  background-color: #fff;
  padding: 30rpx 0;
  margin-bottom: 20rpx;
}

.link-input-area {
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}

.custom-input {
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

/* 上传名片截图 */
.screenshot-section {
  background-color: #fff;
  padding: 30rpx 0;
  margin-bottom: 40rpx;
}

.screenshot-example {
  display: flex;
  align-items: flex-start;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}

.example-image-container {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.example-image {
  width: 200rpx;
  height: 200rpx;
  margin-left: 20rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
}

.upload-area {
  margin: 0 30rpx;
  height: 300rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  margin-bottom: 80rpx;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.uploaded-image {
  width: 100%;
  height: 100%;
}

/* 提交按钮 */
.action-button {
  position: fixed;
  bottom: 60rpx;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
} 

.hide {
  display: none;
}