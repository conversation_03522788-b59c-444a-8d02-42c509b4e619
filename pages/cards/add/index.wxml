<view class="add-card-container">
  <!-- 认证流程 - 选项卡式 -->
  <view class="verification-section">
    <view class="section-title">1.选择认证平台</view>
    <view class="platform-tabs">
      <view class="tab-item {{selectedPlatform === 'dianping' ? 'active' : 'hide'}}" 
          bindtap="selectPlatform" data-platform="dianping">
        <text>大众点评</text>
      </view>
      <view class="tab-item {{selectedPlatform === 'xiaohongshu' ? 'active' : 'hide'}}" 
        bindtap="selectPlatform" data-platform="xiaohongshu">
        <text>小红书</text>
      </view>
    </view>
    <view class="help-link" bindtap="showHelpInfo">
      <text>如何认证平台</text>
      <t-icon name="help-circle" size="32rpx" color="#fa550f" />
    </view>
  </view>

  <!-- 大众点评认证内容 -->
  <block wx:if="{{selectedPlatform === 'dianping'}}">
    <view class="section-title">2.昵称 (大众点评)</view>
    <view class="link-input-area">
      <t-input
        placeholder="昵称"
        value="{{card.name}}"
        data-name="name"
        bind:change="onInput"
        borderless
        t-class="custom-input"
      />
    </view>

    <view class="section-title">3.等级 (大众点评)</view>
    <view class="link-input-area">
      <t-input
        placeholder="等级"
        value="{{card.level}}"
        data-name="level"
        type="number"
        bind:change="onInputNumber"
        borderless
        t-class="custom-input"
      />
    </view>

    <view class="section-title">4.粉丝数 (大众点评)</view>
    <view class="link-input-area">
      <t-input
        placeholder="粉丝数"
        value="{{card.followers_count}}"
        data-name="followers_count"
        type="number"
        bind:change="onInputNumber"
        borderless
        t-class="custom-input"
      />
    </view>

    <!-- 上传个人名片链接 -->
    <view class="section-title">5.粘贴个人名片链接 (大众点评)</view>
    <view class="link-input-area">
      <t-input
        placeholder="粘贴大众点评个人主页链接"
        value="{{card.url}}"
        data-name="url"
        bind:change="onInput"
        borderless
        t-class="custom-input"
      />
    </view>

    <!-- 上传名片截图 -->
    <view class="screenshot-section">
      <view class="section-title">6.上传名片截图 (大众点评)</view>
      <view class="screenshot-example">
        <text>示例:</text>
        <view class="example-image-container">
          <image src="https://636c-cloud1-0gpy573m8caa7db3-1321286342.tcb.qcloud.la/app/dzdp_example.jpg" mode="aspectFit" class="example-image" bindtap="openImageViewer2"></image>
        </view>
      </view>
      <view class="upload-area" bindtap="chooseImage">
        <view class="upload-placeholder" wx:if="{{!card.fileID}}">
          <t-icon name="camera" size="60rpx" color="#999" />
        </view>
        <image wx:else src="{{card.fileID}}" mode="aspectFit" class="uploaded-image"></image>
      </view>
    </view>
  </block>

  <!-- 小红书认证内容 -->
  <block wx:if="{{selectedPlatform === 'xiaohongshu'}}">
    <view class="section-title">2.昵称 (小红书)</view>
    <view class="link-input-area">
      <t-input
        placeholder="昵称"
        value="{{card.name}}"
        data-name="name"
        bind:change="onInput"
        borderless
        t-class="custom-input"
      />
    </view>

    <view class="section-title">3.粉丝数 (小红书)</view>
    <view class="link-input-area">
      <t-input
        placeholder="粉丝数"
        value="{{card.followers_count}}"
        data-name="followers_count"
        type="number"
        bind:change="onInputNumber"
        borderless
        t-class="custom-input"
      />
    </view>
    <!-- 上传个人名片链接 -->
    <view class="section-title">4.粘贴个人名片链接 (小红书)</view>
    <view class="link-input-area">
      <t-input
        placeholder="粘贴小红书个人名片链接"
        value="{{card.url}}"
        data-name="url"
        bind:change="onInput"
        borderless
        t-class="custom-input"
      />
    </view>

    <!-- 上传名片截图 -->
    <view class="screenshot-section">
      <view class="section-title">5.上传名片截图 (小红书)</view>
      <view class="screenshot-example">
        <text>示例:</text>
        <view class="example-image-container">
          <image src="https://636c-cloud1-0gpy573m8caa7db3-1321286342.tcb.qcloud.la/app/xiaohongshu_example.jpg" mode="aspectFit" class="example-image" bindtap="openImageViewer1"></image>
        </view>
      </view>
      <view class="upload-area" bindtap="chooseImage">
        <view class="upload-placeholder" wx:if="{{!card.fileID}}">
          <t-icon name="camera" size="60rpx" color="#999" />
        </view>
        <image wx:else src="{{card.fileID}}" mode="aspectFit" class="uploaded-image"></image>
      </view>
    </view>
  </block>

  <!-- 提交按钮 -->
  <view class="action-button">
    <t-button theme="primary" size="large" block bindtap="submitCard" disabled="{{disabled}}">{{this.data.isUpdate ? '更新' : '提交'}}</t-button>
  </view>
</view> 


<t-image-viewer
  usingCustomNavbar
  deleteBtn="{{deleteBtn}}"
  closeBtn="{{closeBtn}}"
  showIndex="{{showIndex}}"
  visible="{{visible}}"
  images="{{imagesToPreview}}"
  bind:close="onCloseImageViewer"
></t-image-viewer>
