{"name": "@tencent/tdesign-miniprogram-starter-retail", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint --cache --fix --ext .js", "check": "node config/eslintCheck.js", "prepare": "husky install", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"}, "author": "", "license": "ISC", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js, ts}": "eslint --cache --fix", "*.{js,ts,wxml,html,json,css,less}": ["npx @biomejs/biome format --write"]}, "dependencies": {"@cloudbase/wx-cloud-client-sdk": "1.6.1", "dayjs": "^1.9.3", "mobx-miniprogram": "^6.12.3", "mobx-miniprogram-bindings": "^5.0.0", "tdesign-miniprogram": "1.9.3", "tslib": "^1.11.1"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@commitlint/cli": "^17.4.2", "@commitlint/config-conventional": "^17.4.2", "commitizen": "^4.3.0", "conventional-changelog-cli": "^2.2.2", "cz-conventional-changelog": "^3.3.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-prettier": "^3.1.2", "husky": "^8.0.3", "lint-staged": "^10.0.8", "prettier": "^2.1.2"}}